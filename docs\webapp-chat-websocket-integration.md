# Webapp Chat WebSocket Integration

## Tổng quan

Tài liệu này mô tả cách tích hợp WebSocket cho webapp chat với AI agent. Hệ thống bao gồm:

- **Backend**: NestJS WebSocket Gateway với namespace `webapp-chat`
- **Frontend**: React components và hooks để kết nối WebSocket
- **Features**: Real-time chat, AI streaming response, typing indicators

## Cấu trúc Backend

### WebSocket Gateway

**File**: `backend/chat/gateways/webapp-chat.gateway.ts`

```typescript
@WebSocketGateway({
  cors: { origin: '*', methods: ['GET', 'POST'], credentials: true },
  namespace: 'webapp-chat',
})
export class WebappChatGateway {
  // Xử lý các events: send_message, join_conversation, typing, etc.
}
```

### Events được hỗ trợ

| Event | Mô tả |
|-------|-------|
| `webapp_chat:send_message` | G<PERSON>i tin nhắn từ user |
| `webapp_chat:message_received` | Xác nhận tin nhắn đã nhận |
| `webapp_chat:ai_response` | Phản hồi AI hoàn chỉnh |
| `webapp_chat:ai_stream_chunk` | Streaming chunk từ AI |
| `webapp_chat:ai_stream_end` | Kết thúc streaming |
| `webapp_chat:join_conversation` | Tham gia cuộc hội thoại |
| `webapp_chat:conversation_joined` | Xác nhận đã tham gia |
| `webapp_chat:typing_start/stop` | Typing indicators |
| `webapp_chat:ai_typing` | AI đang xử lý |
| `webapp_chat:error` | Xử lý lỗi |

### REST API Controller

**File**: `backend/chat/controllers/webapp-chat.controller.ts`

Cung cấp các endpoint:
- `POST /webapp-chat/conversations` - Tạo cuộc hội thoại
- `GET /webapp-chat/conversations/active` - Lấy cuộc hội thoại active
- `POST /webapp-chat/messages` - Gửi tin nhắn (fallback)
- `GET /webapp-chat/conversations/:id/history` - Lịch sử chat

## Cấu trúc Frontend

### WebSocket Service

**File**: `src/shared/services/webapp-chat-websocket.service.ts`

```typescript
export class WebappChatWebSocketService {
  // Quản lý kết nối WebSocket
  // Xử lý events và callbacks
  // Singleton pattern
}
```

### React Hook

**File**: `src/shared/hooks/chat/useWebappChatWebSocket.ts`

```typescript
export function useWebappChatWebSocket(options: UseWebappChatWebSocketOptions) {
  return {
    isConnected,
    messages,
    sendMessage,
    streamingMessage,
    isStreaming,
    isAITyping,
    // ... other methods
  };
}
```

### React Component

**File**: `src/shared/components/layout/chat-panel/WebappChatPanel.tsx`

```typescript
const WebappChatPanel: React.FC<WebappChatPanelProps> = ({
  onClose,
  conversationId,
  websocketConfig,
}) => {
  // Sử dụng useWebappChatWebSocket hook
  // Render chat interface
};
```

## Cách sử dụng

### 1. Cấu hình Environment

Thêm vào `.env`:

```bash
REACT_APP_WEBSOCKET_URL=ws://localhost:3001
```

### 2. Sử dụng Component

```tsx
import { WebappChatPanel } from '@/shared/components/layout/chat-panel';

function App() {
  const websocketConfig = {
    url: process.env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:3001',
    namespace: 'webapp-chat',
    auth: {
      token: localStorage.getItem('access_token'),
    },
  };

  return (
    <WebappChatPanel
      onClose={() => console.log('Chat closed')}
      websocketConfig={websocketConfig}
      conversationId={123}
    />
  );
}
```

### 3. Sử dụng Hook trực tiếp

```tsx
import { useWebappChatWebSocket } from '@/shared/hooks/chat/useWebappChatWebSocket';

function CustomChatComponent() {
  const {
    isConnected,
    messages,
    sendMessage,
    streamingMessage,
    isStreaming,
  } = useWebappChatWebSocket({
    config: websocketConfig,
    conversationId: 123,
    autoJoinConversation: true,
  });

  // Custom chat implementation
}
```

## Tính năng chính

### 1. Real-time Messaging

- Gửi/nhận tin nhắn real-time
- Auto-join conversation khi kết nối
- Message confirmation

### 2. AI Streaming Response

- Streaming chunks từ AI
- Real-time hiển thị response
- Progress indicators

### 3. Typing Indicators

- User typing indicators
- AI processing indicators
- Auto timeout

### 4. Connection Management

- Auto-reconnection
- Connection status tracking
- Error handling

### 5. Authentication

- JWT token authentication
- Secure WebSocket connection

## Demo Page

Truy cập `/demo/webapp-chat` để test WebSocket connection:

- Cấu hình connection parameters
- Test real-time messaging
- Debug WebSocket events
- Monitor connection status

## Troubleshooting

### 1. Connection Issues

```bash
# Kiểm tra backend có chạy không
curl http://localhost:3001/health

# Kiểm tra WebSocket endpoint
wscat -c ws://localhost:3001/webapp-chat
```

### 2. Authentication Issues

- Đảm bảo JWT token hợp lệ
- Kiểm tra SocketAuthGuard trong backend
- Verify token trong localStorage

### 3. Event Issues

- Mở Developer Console để xem WebSocket logs
- Kiểm tra event names có đúng prefix `webapp_chat:`
- Verify event data structure

### 4. Streaming Issues

- Kiểm tra AI service có hoạt động không
- Monitor streaming events trong console
- Verify streaming timeout settings

## Best Practices

### 1. Error Handling

```typescript
// Luôn handle errors
try {
  await sendMessage(content);
} catch (error) {
  showNotification('error', 'Failed to send message');
}
```

### 2. Memory Management

```typescript
// Cleanup khi component unmount
useEffect(() => {
  return () => {
    service.disconnect();
  };
}, []);
```

### 3. Performance

- Sử dụng React.memo cho message components
- Debounce typing indicators
- Limit message history

### 4. Security

- Validate tất cả input
- Sanitize message content
- Use HTTPS trong production

## Roadmap

- [ ] File upload support
- [ ] Message reactions
- [ ] Message editing/deletion
- [ ] Multi-user conversations
- [ ] Voice messages
- [ ] Video calls integration
