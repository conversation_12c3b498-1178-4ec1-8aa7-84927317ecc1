import React from 'react';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Card,
  Form,
  FormItem,
  Input,
  Select,
  Button,
} from '@/shared/components/common';
import { useCreatePhysicalWarehouse } from '../../hooks/usePhysicalWarehouseQuery';
import { useWarehouses } from '../../hooks/useWarehouseQuery';
import { CreatePhysicalWarehouseDto } from '../../types/physical-warehouse.types';
import { createPhysicalWarehouseSchema, CreatePhysicalWarehouseFormValues } from '../../schemas/physical-warehouse.schema';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

interface CreatePhysicalWarehouseFormProps {
  initialData?: Partial<CreatePhysicalWarehouseFormValues>;
  onSubmit?: (data: CreatePhysicalWarehouseFormValues) => void;
  onCancel?: () => void;
}

/**
 * Component form tạo kho vật lý mới
 */
const CreatePhysicalWarehouseForm: React.FC<CreatePhysicalWarehouseFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const notification = useSmartNotification();

  // Mutation để tạo kho vật lý mới
  const { mutateAsync: createPhysicalWarehouse, isPending: isCreating } = useCreatePhysicalWarehouse();

  // Lấy danh sách warehouses để chọn
  const { data: warehousesData } = useWarehouses({
    page: 1,
    limit: 100, // Lấy nhiều để có đủ options
  });

  // Khởi tạo form
  const form = useForm<CreatePhysicalWarehouseFormValues>({
    resolver: zodResolver(createPhysicalWarehouseSchema),
    defaultValues: {
      warehouseId: initialData?.warehouseId || 0,
      address: initialData?.address || '',
      capacity: initialData?.capacity || undefined,
    },
  });

  // Xử lý submit form
  const handleSubmit = async (values: CreatePhysicalWarehouseFormValues) => {
    try {
      // Tạo kho vật lý mới
      await createPhysicalWarehouse(values as CreatePhysicalWarehouseDto);
      notification.success({ message: t('business:physicalWarehouse.createSuccess') });

      // Gọi callback onSubmit nếu có
      if (onSubmit) {
        onSubmit(values);
      }
    } catch (error) {
      console.error('Error creating physical warehouse:', error);
      notification.error({
        message: t('business:physicalWarehouse.createError')
      });
    }
  };

  // Tạo options cho warehouse select
  const warehouseOptions = warehousesData?.result?.items?.map(warehouse => ({
    value: warehouse.warehouseId,
    label: warehouse.name,
  })) || [];

  return (
    <Card title={t('business:physicalWarehouse.add')}>
      <Form
        schema={createPhysicalWarehouseSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        className="p-4 space-y-4"
      >
        <div className="grid grid-cols-1 gap-4">
          <FormItem
            name="warehouseId"
            label={t('business:physicalWarehouse.warehouse')}
            required
          >
            <Controller
              control={form.control}
              name="warehouseId"
              render={({ field }) => (
                <Select
                  fullWidth
                  placeholder={t('business:physicalWarehouse.form.selectWarehouse')}
                  options={warehouseOptions}
                  value={field.value}
                  onChange={field.onChange}
                />
              )}
            />
          </FormItem>

          <FormItem
            name="address"
            label={t('business:physicalWarehouse.address')}
            required
          >
            <Input
              fullWidth
              placeholder={t('business:physicalWarehouse.form.addressPlaceholder')}
              {...form.register('address')}
            />
          </FormItem>

          <FormItem
            name="capacity"
            label={t('business:physicalWarehouse.capacity')}
          >
            <Input
              type="number"
              fullWidth
              placeholder={t('business:physicalWarehouse.form.capacityPlaceholder')}
              {...form.register('capacity', { valueAsNumber: true })}
            />
          </FormItem>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={onCancel}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            isLoading={isCreating}
          >
            {t('common:create')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default CreatePhysicalWarehouseForm;
