# Chat Module - Facebook Messenger Integration & AI Assistant

## Tổng quan

Module Chat cung cấp tích hợp với Facebook Messenger và hệ thống AI Assistant sử dụng RAG (Retrieval-Augmented Generation) để trả lời các câu hỏi về dữ liệu nghiệp vụ.

## Tính năng chính

### 1. Facebook Messenger Integration
- Nhận/gửi tin nhắn từ Facebook Page
- Webhook verification và signature validation
- Hỗ trợ text, attachments, quick replies
- Multi-tenant support

### 2. AI Assistant với RAG
- Phân tích intent và entities từ tin nhắn
- Tìm kiếm thông tin trong vector database
- Truy vấn dữ liệu real-time từ HRM, Todo modules
- Tạo phản hồi contextual với OpenAI GPT

### 3. Business Intelligence
- Thống kê công việc (hoàn thành, chậm deadline)
- Thông tin nhân viên và phòng ban
- Báo cáo chấm công và đi muộn
- Metrics và KPIs

## Kiến trúc

```
Chat Module
├── entities/           # Database entities
├── controllers/        # API controllers
├── services/          # Business logic
├── repositories/      # Data access (TODO)
├── migrations/        # Database migrations
└── README.md
```

## Setup

### 1. Database Migration

```sql
-- Chạy migration để tạo bảng
psql -d your_database -f src/modules/chat/migrations/create-chat-tables.sql
```

### 2. Environment Variables

```env
# Facebook App Configuration
FACEBOOK_APP_ID=your_app_id
FACEBOOK_APP_SECRET=your_app_secret
FACEBOOK_WEBHOOK_VERIFY_TOKEN=your_verify_token

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
```

### 3. Facebook App Setup

1. Tạo Facebook App tại https://developers.facebook.com
2. Thêm Messenger product
3. Setup webhook URL: `https://your-domain.com/v1/api/chat/facebook-webhook`
4. Subscribe to page events: `messages`, `messaging_postbacks`
5. Lấy Page Access Token

### 4. Webhook Configuration

```bash
# Verify webhook
curl -X GET "https://your-domain.com/v1/api/chat/facebook-webhook?hub.verify_token=your_verify_token&hub.challenge=challenge_string&hub.mode=subscribe"
```

## API Endpoints

### Facebook Webhook
- `GET /v1/api/chat/facebook-webhook` - Verify webhook
- `POST /v1/api/chat/facebook-webhook` - Receive events

## Luồng xử lý tin nhắn

```mermaid
sequenceDiagram
    participant FB as Facebook
    participant WH as Webhook Controller
    participant MP as Message Processor
    participant AI as AI Orchestrator
    participant RAG as RAG Service
    participant DB as Database

    FB->>WH: Webhook Event
    WH->>MP: Process Message
    MP->>AI: Analyze Intent
    AI->>RAG: Query Business Data
    RAG->>DB: Get Real-time Data
    RAG->>AI: Return Context
    AI->>MP: Generate Response
    MP->>FB: Send Reply
```

## Cấu hình AI Assistant

### 1. Intent Categories
- `greeting` - Lời chào
- `get_todo_statistics` - Thống kê công việc
- `get_employee_info` - Thông tin nhân viên
- `get_late_employees` - Nhân viên đi muộn
- `get_overdue_tasks` - Công việc chậm deadline
- `general_question` - Câu hỏi chung

### 2. Quick Replies
```javascript
const quickReplies = [
  { title: '📊 Thống kê công việc', payload: 'get_todo_stats' },
  { title: '👥 Thông tin nhân viên', payload: 'get_employee_info' },
  { title: '⏰ Nhân viên đi muộn', payload: 'get_late_employees' },
  { title: '🚨 Công việc chậm deadline', payload: 'get_overdue_tasks' },
];
```

## Vector Database Setup (Optional)

Để sử dụng đầy đủ tính năng RAG, cần setup vector database:

### 1. PostgreSQL với pgvector
```sql
-- Install pgvector extension
CREATE EXTENSION vector;

-- Enable vector index
CREATE INDEX idx_ai_knowledge_base_embedding 
ON ai_knowledge_base USING ivfflat (embedding vector_cosine_ops);
```

### 2. Alternative: Pinecone
```typescript
// Configure Pinecone in RAGService
const pinecone = new PineconeClient();
await pinecone.init({
  environment: 'your-env',
  apiKey: 'your-api-key',
});
```

## Development

### 1. Tạo services còn thiếu
```bash
# ChatService
touch src/modules/chat/services/chat.service.ts

# PromptManagerService  
touch src/modules/chat/services/prompt-manager.service.ts

# Repositories
mkdir src/modules/chat/repositories
touch src/modules/chat/repositories/chat-conversation.repository.ts
touch src/modules/chat/repositories/chat-message.repository.ts
```

### 2. Implement missing methods
- `TodoService.getOverdueTodos()`
- `TodoService.getCompletedTodosInMonth()`
- `StatisticsService.getTodoStatistics()`
- `EmployeeService.getEmployeeStatistics()`

### 3. Testing
```bash
# Unit tests
npm run test src/modules/chat

# E2E tests
npm run test:e2e chat
```

## Monitoring & Logging

### 1. Metrics to track
- Message volume per tenant
- AI response accuracy
- Response time
- Error rates
- User satisfaction

### 2. Logging
```typescript
this.logger.log('Facebook webhook event received');
this.logger.error('AI processing error', error.stack);
```

## Security

### 1. Webhook Verification
- Verify Facebook signature
- Validate verify token
- Rate limiting

### 2. Data Protection
- Encrypt sensitive data
- Audit logs
- GDPR compliance

## Troubleshooting

### Common Issues

1. **Webhook verification failed**
   - Check verify token
   - Ensure HTTPS endpoint
   - Verify app secret

2. **AI responses are poor**
   - Check OpenAI API key
   - Improve training data
   - Tune prompts

3. **Database connection issues**
   - Check tenant isolation
   - Verify migrations
   - Monitor connection pool

## Roadmap

- [ ] Implement ChatService và repositories
- [ ] Add vector database integration
- [ ] Implement prompt management
- [ ] Add conversation analytics
- [ ] Support multiple languages
- [ ] Add human handoff workflow
- [ ] Implement conversation export
- [ ] Add A/B testing for prompts
