import { useQuery } from '@tanstack/react-query';
import { CustomerImportService } from '../services';
import { CUSTOMER_IMPORT_QUERY_KEYS } from '../constants/customer-import.constants';

/**
 * Interface cho analytics query parameters
 */
export interface AnalyticsQueryParams extends Record<string, unknown> {
  startDate?: string;
  endDate?: string;
  groupBy?: 'day' | 'week' | 'month';
}

/**
 * Hook để lấy import analytics
 */
export const useImportAnalytics = (params?: AnalyticsQueryParams) => {
  const {
    data: analyticsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: CUSTOMER_IMPORT_QUERY_KEYS.ANALYTICS(params),
    queryFn: () => CustomerImportService.getImportAnalytics(params),
    select: (response) => response.result,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    analytics: analyticsData,
    isLoading,
    error,
    refetch,
  };
};

/**
 * Hook để lấy analytics summary
 */
export const useImportAnalyticsSummary = () => {
  const { analytics, isLoading, error } = useImportAnalytics();

  const summary = analytics ? {
    totalImports: analytics.totalImports,
    successRate: analytics.successRate,
    averageProcessingTime: analytics.averageProcessingTime,
    totalProcessedRows: analytics.performanceMetrics.averageRowsPerSecond * analytics.totalImports,
    
    // Calculated metrics
    failureRate: 100 - analytics.successRate,
    averageFileSize: analytics.performanceMetrics.averageFileSize,
    peakPerformance: analytics.performanceMetrics.peakRowsPerSecond,
    
    // Top used fields
    topFields: analytics.mostUsedFields.slice(0, 5),
    
    // Common errors
    commonErrors: analytics.errorPatterns.slice(0, 3),
  } : null;

  return {
    summary,
    isLoading,
    error,
  };
};

/**
 * Hook để lấy performance metrics
 */
export const useImportPerformanceMetrics = (params?: AnalyticsQueryParams) => {
  const { analytics, isLoading, error } = useImportAnalytics(params);

  const performanceMetrics = analytics ? {
    // Processing speed
    averageSpeed: analytics.performanceMetrics.averageRowsPerSecond,
    peakSpeed: analytics.performanceMetrics.peakRowsPerSecond,
    
    // File handling
    averageFileSize: analytics.performanceMetrics.averageFileSize,
    averageProcessingTime: analytics.averageProcessingTime,
    
    // Efficiency metrics
    efficiency: analytics.successRate / 100,
    throughput: analytics.performanceMetrics.averageRowsPerSecond * (analytics.successRate / 100),
    
    // Calculated insights
    estimatedTimeFor1000Rows: Math.round(1000 / analytics.performanceMetrics.averageRowsPerSecond),
    estimatedTimeFor10000Rows: Math.round(10000 / analytics.performanceMetrics.averageRowsPerSecond),
  } : null;

  return {
    performanceMetrics,
    isLoading,
    error,
  };
};

/**
 * Hook để lấy error analysis
 */
export const useImportErrorAnalysis = () => {
  const { analytics, isLoading, error } = useImportAnalytics();

  const errorAnalysis = analytics ? {
    totalErrors: analytics.errorPatterns.reduce((sum: number, pattern: { frequency: number }) => sum + pattern.frequency, 0),
    errorPatterns: analytics.errorPatterns.map((pattern: { error: string; frequency: number }) => ({
      ...pattern,
      percentage: (pattern.frequency / analytics.totalImports) * 100,
    })),

    // Error categories
    validationErrors: analytics.errorPatterns.filter((p: { error: string }) =>
      p.error.includes('validation') || p.error.includes('format')
    ),
    mappingErrors: analytics.errorPatterns.filter((p: { error: string }) =>
      p.error.includes('mapping') || p.error.includes('column')
    ),
    systemErrors: analytics.errorPatterns.filter((p: { error: string }) =>
      p.error.includes('system') || p.error.includes('network')
    ),
  } : null;

  return {
    errorAnalysis,
    isLoading,
    error,
  };
};

/**
 * Hook để lấy field usage statistics
 */
export const useFieldUsageStats = () => {
  const { analytics, isLoading, error } = useImportAnalytics();

  const fieldStats = analytics ? {
    mostUsedFields: analytics.mostUsedFields,
    totalFieldMappings: analytics.mostUsedFields.reduce((sum: number, field: { usage: number }) => sum + field.usage, 0),

    // Field categories
    requiredFields: analytics.mostUsedFields.filter((field: { field: string }) =>
      ['name', 'email'].includes(field.field)
    ),
    optionalFields: analytics.mostUsedFields.filter((field: { field: string }) =>
      !['name', 'email'].includes(field.field)
    ),

    // Usage percentages
    fieldUsagePercentages: analytics.mostUsedFields.map((field: { field: string; usage: number }) => ({
      ...field,
      percentage: (field.usage / analytics.totalImports) * 100,
    })),
  } : null;

  return {
    fieldStats,
    isLoading,
    error,
  };
};

/**
 * Hook để lấy time-based analytics
 */
export const useTimeBasedAnalytics = (groupBy: 'day' | 'week' | 'month' = 'day') => {
  const endDate = new Date().toISOString().split('T')[0];
  const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 30 days ago

  const { analytics, isLoading, error } = useImportAnalytics({
    startDate,
    endDate,
    groupBy,
  });

  return {
    timeBasedData: analytics,
    isLoading,
    error,
    dateRange: { startDate, endDate },
    groupBy,
  };
};

/**
 * Hook để compare periods
 */
export const useAnalyticsComparison = () => {
  const currentEndDate = new Date().toISOString().split('T')[0];
  const currentStartDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  
  const previousEndDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  const previousStartDate = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

  const { analytics: currentPeriod, isLoading: isLoadingCurrent } = useImportAnalytics({
    startDate: currentStartDate,
    endDate: currentEndDate,
  });

  const { analytics: previousPeriod, isLoading: isLoadingPrevious } = useImportAnalytics({
    startDate: previousStartDate,
    endDate: previousEndDate,
  });

  const comparison = (currentPeriod && previousPeriod) ? {
    totalImportsChange: currentPeriod.totalImports - previousPeriod.totalImports,
    successRateChange: currentPeriod.successRate - previousPeriod.successRate,
    processingTimeChange: currentPeriod.averageProcessingTime - previousPeriod.averageProcessingTime,
    
    // Percentage changes
    totalImportsChangePercent: previousPeriod.totalImports > 0 
      ? ((currentPeriod.totalImports - previousPeriod.totalImports) / previousPeriod.totalImports) * 100
      : 0,
    successRateChangePercent: previousPeriod.successRate > 0
      ? ((currentPeriod.successRate - previousPeriod.successRate) / previousPeriod.successRate) * 100
      : 0,
  } : null;

  return {
    currentPeriod,
    previousPeriod,
    comparison,
    isLoading: isLoadingCurrent || isLoadingPrevious,
  };
};

export default useImportAnalytics;
