import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * Trang quản lý vị trí tuyển dụng
 */
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, Card, Table, Typography } from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';

import { useJobPositions } from '../hooks/useRecruitment';
import { JobPosition, JobPositionStatus, JobType, Priority } from '../types/recruitment.types';

/**
 * Component trang quản lý vị trí tuyển dụng
 */
const JobPositionsPage: React.FC = () => {
  const { t } = useTranslation(['common', 'hrm']);

  // State cho filters
  const [filters, setFilters] = useState({
    status: '',
    jobType: '',
    priority: '',
    departmentId: '',
  });

  // Đ<PERSON>nh nghĩa columns cho table
  const columns = useMemo(
    () => [
      {
        title: t('hrm:recruitment.jobPosition.title', 'Tiêu đề'),
        dataIndex: 'title',
        key: 'title',
        sortable: true,
        render: (value: string, record: JobPosition) => (
          <div>
            <Typography variant="body1" className="font-medium">
              {value}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {record.departmentName}
            </Typography>
          </div>
        ),
      },
      {
        title: t('hrm:recruitment.jobPosition.jobType', 'Loại công việc'),
        dataIndex: 'jobType',
        key: 'jobType',
        render: (value: JobType) => {
          const typeLabels = {
            [JobType.FULL_TIME]: t('hrm:recruitment.jobType.fullTime', 'Toàn thời gian'),
            [JobType.PART_TIME]: t('hrm:recruitment.jobType.partTime', 'Bán thời gian'),
            [JobType.CONTRACT]: t('hrm:recruitment.jobType.contract', 'Hợp đồng'),
            [JobType.INTERNSHIP]: t('hrm:recruitment.jobType.internship', 'Thực tập'),
            [JobType.FREELANCE]: t('hrm:recruitment.jobType.freelance', 'Freelance'),
          };
          return (
            <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
              {typeLabels[value]}
            </span>
          );
        },
      },
      {
        title: t('hrm:recruitment.jobPosition.status', 'Trạng thái'),
        dataIndex: 'status',
        key: 'status',
        render: (value: JobPositionStatus) => {
          const statusConfig = {
            [JobPositionStatus.DRAFT]: {
              label: t('hrm:recruitment.status.draft', 'Nháp'),
              color: 'gray',
            },
            [JobPositionStatus.ACTIVE]: {
              label: t('hrm:recruitment.status.active', 'Đang tuyển'),
              color: 'green',
            },
            [JobPositionStatus.PAUSED]: {
              label: t('hrm:recruitment.status.paused', 'Tạm dừng'),
              color: 'yellow',
            },
            [JobPositionStatus.CLOSED]: {
              label: t('hrm:recruitment.status.closed', 'Đã đóng'),
              color: 'red',
            },
            [JobPositionStatus.CANCELLED]: {
              label: t('hrm:recruitment.status.cancelled', 'Đã hủy'),
              color: 'red',
            },
          };
          const config = statusConfig[value];
          return (
            <span
              className={`px-2 py-1 bg-${config.color}-100 text-${config.color}-800 rounded-full text-xs`}
            >
              {config.label}
            </span>
          );
        },
      },
      {
        title: t('hrm:recruitment.jobPosition.priority', 'Ưu tiên'),
        dataIndex: 'priority',
        key: 'priority',
        render: (value: Priority) => {
          const priorityConfig = {
            [Priority.LOW]: { label: t('hrm:recruitment.priority.low', 'Thấp'), color: 'gray' },
            [Priority.MEDIUM]: {
              label: t('hrm:recruitment.priority.medium', 'Trung bình'),
              color: 'blue',
            },
            [Priority.HIGH]: { label: t('hrm:recruitment.priority.high', 'Cao'), color: 'orange' },
            [Priority.URGENT]: {
              label: t('hrm:recruitment.priority.urgent', 'Khẩn cấp'),
              color: 'red',
            },
          };
          const config = priorityConfig[value];
          return (
            <span
              className={`px-2 py-1 bg-${config.color}-100 text-${config.color}-800 rounded-full text-xs`}
            >
              {config.label}
            </span>
          );
        },
      },
      {
        title: t('hrm:recruitment.jobPosition.openings', 'Số lượng'),
        dataIndex: 'openings',
        key: 'openings',
        sortable: true,
        render: (value: number) => (
          <Typography variant="body2" className="text-center">
            {value}
          </Typography>
        ),
      },
      {
        title: t('hrm:recruitment.jobPosition.location', 'Địa điểm'),
        dataIndex: 'location',
        key: 'location',
      },
      {
        title: t('hrm:recruitment.jobPosition.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        key: 'createdAt',
        sortable: true,
        render: (value: string) => new Date(value).toLocaleDateString('vi-VN'),
      },
      {
        title: t('common:actions', 'Thao tác'),
        key: 'actions',
        render: (record: JobPosition) => (
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              {t('common:edit', 'Sửa')}
            </Button>
            <Button variant="outline" size="sm">
              {t('common:view', 'Xem')}
            </Button>
          </div>
        ),
      },
    ],
    [t]
  );

  // Setup data table
  const dataTable = useDataTable(useDataTableConfig({ columns }));

  // Fetch data
  const { data, isLoading } = useJobPositions({
    ...dataTable.queryParams,
    ...filters,
  });

  // Menu items cho filter
  const filterMenuItems = [
    {
      label: t('hrm:recruitment.filter.status', 'Lọc theo trạng thái'),
      items: [
        { label: t('common:all', 'Tất cả'), value: '' },
        {
          label: t('hrm:recruitment.status.active', 'Đang tuyển'),
          value: JobPositionStatus.ACTIVE,
        },
        { label: t('hrm:recruitment.status.draft', 'Nháp'), value: JobPositionStatus.DRAFT },
        { label: t('hrm:recruitment.status.paused', 'Tạm dừng'), value: JobPositionStatus.PAUSED },
        { label: t('hrm:recruitment.status.closed', 'Đã đóng'), value: JobPositionStatus.CLOSED },
      ],
    },
  ];

  return (
    <div className="w-full bg-background text-foreground">
    
      {/* Content */}
      <Card>
        {/* Menu Bar */}
        <MenuIconBar
          onSearch={dataTable.handleSearch}
          onAdd={() => console.log('Add new job position')}
          items={[]}
          onColumnVisibilityChange={dataTable.columnVisibility.setColumns}
          columns={dataTable.columnVisibility.columns}
          showDateFilter={false}
          showColumnFilter={true}
        />

        {/* Table */}
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={data?.items || []}
          loading={isLoading}
          pagination={{
            current: data?.page || 1,
            pageSize: data?.limit || 10,
            total: data?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              t('common:pagination.showTotal', `Hiển thị {{start}}-{{end}} của {{total}} mục`, {
                start: range[0],
                end: range[1],
                total,
              }),
            onChange: dataTable.pagination.handlePageChange,
            onShowSizeChange: dataTable.pagination.handlePageSizeChange,
          }}
        />
      </Card>
    </div>
  );
};

export default JobPositionsPage;
