import { ImportSource, ValidationRule } from '../types/customer-import.types';

/**
 * Query keys cho TanStack Query
 */
export const CUSTOMER_IMPORT_QUERY_KEYS = {
  ALL: ['customer-import'] as const,
  JOBS: (params?: Record<string, unknown>) => [...CUSTOMER_IMPORT_QUERY_KEYS.ALL, 'jobs', params] as const,
  JOB: (id: string) => [...CUSTOMER_IMPORT_QUERY_KEYS.ALL, 'job', id] as const,
  TEMPLATES: (params?: Record<string, unknown>) => [...CUSTOMER_IMPORT_QUERY_KEYS.ALL, 'templates', params] as const,
  TEMPLATE: (id: string) => [...CUSTOMER_IMPORT_QUERY_KEYS.ALL, 'template', id] as const,
  ANALYTICS: (params?: Record<string, unknown>) => [...CUSTOMER_IMPORT_QUERY_KEYS.ALL, 'analytics', params] as const,
  AUTO_MAPPING: (data: unknown) => [...CUSTOMER_IMPORT_QUERY_KEYS.ALL, 'auto-mapping', data] as const,
  DATA_QUALITY: (data: unknown) => [...CUSTOMER_IMPORT_QUERY_KEYS.ALL, 'data-quality', data] as const,
} as const;

/**
 * Các import sources được hỗ trợ
 */
export const IMPORT_SOURCES: ImportSource[] = [
  {
    type: 'excel',
    name: 'Excel File',
    icon: 'file-spreadsheet',
    description: 'Import from Excel (.xlsx, .xls) files',
    supportedFormats: ['.xlsx', '.xls'],
    requiresAuth: false,
  },
  {
    type: 'csv',
    name: 'CSV File',
    icon: 'file-text',
    description: 'Import from CSV (.csv) files',
    supportedFormats: ['.csv'],
    requiresAuth: false,
  },
  {
    type: 'google-sheets',
    name: 'Google Sheets',
    icon: 'external-link',
    description: 'Import directly from Google Sheets',
    requiresAuth: true,
  },
  {
    type: 'api',
    name: 'API Endpoint',
    icon: 'database',
    description: 'Import from REST API endpoint',
    requiresAuth: false,
  },
  {
    type: 'crm',
    name: 'CRM System',
    icon: 'users',
    description: 'Import from CRM systems',
    requiresAuth: true,
  },
];

/**
 * Default validation rules
 */
export const DEFAULT_VALIDATION_RULES: ValidationRule[] = [
  {
    id: 'name-required',
    field: 'name',
    type: 'required',
    message: 'Customer name is required',
    enabled: true,
  },
  {
    id: 'email-format',
    field: 'email',
    type: 'email',
    message: 'Invalid email format',
    enabled: true,
  },
  {
    id: 'phone-format',
    field: 'phone',
    type: 'phone',
    message: 'Invalid phone number format',
    enabled: false,
  },
  {
    id: 'name-length',
    field: 'name',
    type: 'length',
    value: { min: 2, max: 100 },
    message: 'Name must be between 2 and 100 characters',
    enabled: true,
  },
];

/**
 * Customer fields mapping options
 */
export const CUSTOMER_FIELD_OPTIONS = [
  { value: 'name', label: 'Customer Name', required: true },
  { value: 'email', label: 'Email Address', required: false },
  { value: 'phone', label: 'Phone Number', required: false },
  { value: 'address', label: 'Address', required: false },
  { value: 'city', label: 'City', required: false },
  { value: 'state', label: 'State/Province', required: false },
  { value: 'country', label: 'Country', required: false },
  { value: 'zipCode', label: 'ZIP/Postal Code', required: false },
  { value: 'company', label: 'Company', required: false },
  { value: 'jobTitle', label: 'Job Title', required: false },
  { value: 'website', label: 'Website', required: false },
  { value: 'notes', label: 'Notes', required: false },
  { value: 'tags', label: 'Tags', required: false },
  { value: 'source', label: 'Lead Source', required: false },
  { value: 'dateOfBirth', label: 'Date of Birth', required: false },
  { value: 'gender', label: 'Gender', required: false },
  { value: 'preferredLanguage', label: 'Preferred Language', required: false },
  { value: 'timezone', label: 'Timezone', required: false },
] as const;

/**
 * Import step configurations
 */
export const IMPORT_STEPS = [
  {
    key: 'upload',
    title: 'Upload Data',
    description: 'Upload your file or connect to data source',
    icon: 'upload',
  },
  {
    key: 'mapping',
    title: 'Map Columns',
    description: 'Map your data columns to customer fields',
    icon: 'arrow-right-left',
  },
  {
    key: 'preview',
    title: 'Preview & Validate',
    description: 'Review and validate your data before import',
    icon: 'eye',
  },
  {
    key: 'importing',
    title: 'Importing',
    description: 'Processing your data import',
    icon: 'loader',
  },
  {
    key: 'complete',
    title: 'Complete',
    description: 'Import completed successfully',
    icon: 'check-circle',
  },
] as const;

/**
 * File size limits
 */
export const FILE_LIMITS = {
  MAX_SIZE: 50 * 1024 * 1024, // 50MB
  MAX_ROWS: 50000,
  PREVIEW_ROWS: 10,
  BATCH_SIZE: 100,
} as const;

/**
 * Import job statuses với colors
 */
export const IMPORT_JOB_STATUSES = {
  pending: {
    label: 'Pending',
    color: 'gray',
    icon: 'clock',
  },
  processing: {
    label: 'Processing',
    color: 'blue',
    icon: 'loader',
  },
  completed: {
    label: 'Completed',
    color: 'green',
    icon: 'check-circle',
  },
  failed: {
    label: 'Failed',
    color: 'red',
    icon: 'x-circle',
  },
  cancelled: {
    label: 'Cancelled',
    color: 'orange',
    icon: 'x',
  },
} as const;

/**
 * Data quality thresholds
 */
export const DATA_QUALITY_THRESHOLDS = {
  EXCELLENT: 95,
  GOOD: 80,
  FAIR: 60,
  POOR: 40,
} as const;

/**
 * Auto-mapping confidence thresholds
 */
export const AUTO_MAPPING_CONFIDENCE = {
  HIGH: 0.8,
  MEDIUM: 0.6,
  LOW: 0.4,
} as const;

/**
 * Error messages
 */
export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: `File size exceeds ${FILE_LIMITS.MAX_SIZE / (1024 * 1024)}MB limit`,
  UNSUPPORTED_FORMAT: 'Unsupported file format',
  EMPTY_FILE: 'File is empty or contains no data',
  INVALID_HEADERS: 'Invalid or missing headers',
  NO_MAPPING: 'At least one column must be mapped',
  REQUIRED_FIELD_MISSING: 'Required field mapping is missing',
  VALIDATION_FAILED: 'Data validation failed',
  IMPORT_FAILED: 'Import process failed',
  NETWORK_ERROR: 'Network error occurred',
  UNAUTHORIZED: 'Authentication required',
} as const;

/**
 * Success messages
 */
export const SUCCESS_MESSAGES = {
  FILE_UPLOADED: 'File uploaded successfully',
  MAPPING_SAVED: 'Column mapping saved',
  TEMPLATE_SAVED: 'Template saved successfully',
  IMPORT_STARTED: 'Import process started',
  IMPORT_COMPLETED: 'Import completed successfully',
  VALIDATION_PASSED: 'Data validation passed',
} as const;

/**
 * Default configurations
 */
export const DEFAULT_IMPORT_CONFIG = {
  skipFirstRow: true,
  validateEmail: true,
  validatePhone: false,
  allowDuplicates: false,
  batchSize: FILE_LIMITS.BATCH_SIZE,
  maxConcurrency: 3,
  retryAttempts: 3,
  retryDelay: 1000,
  enableQueue: true,
} as const;
