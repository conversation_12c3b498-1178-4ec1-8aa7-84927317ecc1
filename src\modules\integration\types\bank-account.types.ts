/**
 * Bank Account Integration Types
 * Định nghĩa các interface cho Bank Account integration
 */

/**
 * Enum cho các ngân hàng
 */
export enum BankCode {
  OCB = 'OCB',
  VIETCOMBANK = 'VCB',
  TECHCOMBANK = 'TCB',
  BIDV = 'BIDV',
  VIETINBANK = 'VTB',
  AGRIBANK = 'AGB',
  SACOMBANK = 'STB',
  MBBANK = 'MB',
  VPBANK = 'VPB',
  TPBANK = 'TPB',
}

/**
 * Enum cho trạng thái tài khoản ngân hàng
 */
export enum BankAccountStatus {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  EXPIRED = 'EXPIRED',
}

/**
 * Enum cho các trường sắp xếp
 */
export enum BankAccountSortBy {
  BANK_NAME = 'bankName',
  ACCOUNT_NUMBER = 'accountNumber',
  ACCOUNT_NAME = 'accountName',
  STATUS = 'status',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * Interface cho Bank Account Response
 */
export interface BankAccountDto {
  id: string;
  bankCode: BankCode;
  bankName: string;
  accountNumber: string;
  accountName: string;
  idNumber: string;
  phoneNumber: string;
  storeName: string;
  storeAddress: string;
  status: BankAccountStatus;
  isOcb: boolean;
  hasVirtualAccount: boolean;
  virtualAccountNumber?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho Bank Account Query Parameters
 */
export interface BankAccountQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: BankAccountSortBy;
  sortDirection?: SortDirection;
  bankCode?: BankCode;
  status?: BankAccountStatus;
}

/**
 * Interface cho Create Bank Account Request
 */
export interface CreateBankAccountDto {
  bankCode: BankCode;
  accountNumber: string;
  idNumber: string;
  phoneNumber: string;
  storeName: string;
  storeAddress: string;
}

/**
 * Interface cho Update Bank Account Request
 */
export interface UpdateBankAccountDto {
  storeName?: string;
  storeAddress?: string;
  phoneNumber?: string;
}

/**
 * Interface cho OTP Verification Request
 */
export interface OTPVerificationDto {
  bankAccountId: string;
  otpCode: string;
}

/**
 * Interface cho Virtual Account Creation Request
 */
export interface CreateVirtualAccountDto {
  bankAccountId: string;
}

/**
 * Interface cho Bank Account Statistics
 */
export interface BankAccountStatsDto {
  totalAccounts: number;
  activeAccounts: number;
  pendingAccounts: number;
  ocbAccounts: number;
  virtualAccounts: number;
}

/**
 * Interface cho Bank Option (dùng trong Select)
 */
export interface BankOption {
  value: BankCode;
  label: string;
  requiresOtp: boolean;
  otpLength: number;
}

/**
 * Danh sách các ngân hàng với thông tin OTP
 */
export const BANK_OPTIONS: BankOption[] = [
  {
    value: BankCode.OCB,
    label: 'Ngân hàng TMCP Phương Đông (OCB)',
    requiresOtp: false,
    otpLength: 0,
  },
  {
    value: BankCode.VIETCOMBANK,
    label: 'Ngân hàng TMCP Ngoại thương Việt Nam (Vietcombank)',
    requiresOtp: true,
    otpLength: 6,
  },
  {
    value: BankCode.TECHCOMBANK,
    label: 'Ngân hàng TMCP Kỹ thương Việt Nam (Techcombank)',
    requiresOtp: true,
    otpLength: 6,
  },
  {
    value: BankCode.BIDV,
    label: 'Ngân hàng TMCP Đầu tư và Phát triển Việt Nam (BIDV)',
    requiresOtp: true,
    otpLength: 8,
  },
  {
    value: BankCode.VIETINBANK,
    label: 'Ngân hàng TMCP Công thương Việt Nam (VietinBank)',
    requiresOtp: true,
    otpLength: 6,
  },
  {
    value: BankCode.AGRIBANK,
    label: 'Ngân hàng Nông nghiệp và Phát triển Nông thôn Việt Nam (Agribank)',
    requiresOtp: true,
    otpLength: 6,
  },
  {
    value: BankCode.SACOMBANK,
    label: 'Ngân hàng TMCP Sài Gòn Thương Tín (Sacombank)',
    requiresOtp: true,
    otpLength: 6,
  },
  {
    value: BankCode.MBBANK,
    label: 'Ngân hàng TMCP Quân đội (MB Bank)',
    requiresOtp: true,
    otpLength: 6,
  },
  {
    value: BankCode.VPBANK,
    label: 'Ngân hàng TMCP Việt Nam Thịnh vượng (VPBank)',
    requiresOtp: true,
    otpLength: 6,
  },
  {
    value: BankCode.TPBANK,
    label: 'Ngân hàng TMCP Tiên Phong (TPBank)',
    requiresOtp: true,
    otpLength: 6,
  },
];

/**
 * Helper function để lấy thông tin ngân hàng theo mã
 */
export const getBankInfo = (bankCode: BankCode): BankOption | undefined => {
  return BANK_OPTIONS.find(bank => bank.value === bankCode);
};

/**
 * Helper function để kiểm tra ngân hàng có cần OTP không
 */
export const requiresOTP = (bankCode: BankCode): boolean => {
  const bankInfo = getBankInfo(bankCode);
  return bankInfo?.requiresOtp ?? true;
};

/**
 * Helper function để lấy độ dài OTP
 */
export const getOTPLength = (bankCode: BankCode): number => {
  const bankInfo = getBankInfo(bankCode);
  return bankInfo?.otpLength ?? 6;
};
