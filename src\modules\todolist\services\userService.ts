import { apiClient } from '@/shared/api/axios';

/**
 * User status enum
 */
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  SUSPENDED = 'SUSPENDED',
}

/**
 * User response DTO
 */
export interface UserResponseDto {
  id: number;
  email: string;
  fullName: string | null;
  employeeId: number | null;
  departmentId: number | null;
  departmentName: string | null;
  status: UserStatus | null;
  position: string | null;
  phoneNumber: string | null;
  address: string | null;
  dateOfBirth: number | null;
  gender: string | null;
  userType: string | null;
  createdAt: number | null;
  employeeCode: string | null;
  employeeName: string | null;
}

/**
 * User query DTO
 */
export interface UserQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  status?: UserStatus;
  departmentId?: number;
  employeeId?: number;
  hasEmployee?: boolean;
  userType?: string;
}

/**
 * Paginated result interface
 */
export interface PaginatedResult<T> {
  items: T[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  limit: number;
}

/**
 * API response wrapper
 */
export interface ApiResponseDto<T> {
  success: boolean;
  message: string;
  data: T;
}

/**
 * User service for API calls
 */
export class UserService {
  /**
   * Get all users with pagination and filtering
   */
  static async findAllUsers(
    query: UserQueryDto = {}
  ): Promise<ApiResponseDto<PaginatedResult<UserResponseDto>>> {
    const response = await apiClient.get('/api/hrm/employees/users-management', {
      params: query,
    });
    return response.data;
  }

  /**
   * Get user by ID
   */
  static async findUserById(id: number): Promise<ApiResponseDto<UserResponseDto>> {
    const response = await apiClient.get(`/api/hrm/employees/users-management/${id}`);
    return response.data;
  }
}

export default UserService;
