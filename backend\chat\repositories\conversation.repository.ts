import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChatConversation } from '../entities/chat-conversation.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';

export interface ConversationQueryDto {
  page?: number;
  limit?: number;
  pageId?: string;
  status?: string;
  conversationType?: string;
  assignedAgentId?: number;
  language?: string;
  search?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  startDate?: number;
  endDate?: number;
}

/**
 * Repository for ChatConversation entity
 */
@Injectable()
export class ConversationRepository {
  private readonly logger = new Logger(ConversationRepository.name);

  constructor(
    @InjectRepository(ChatConversation)
    private readonly repository: Repository<ChatConversation>,
  ) {}

  /**
   * Find all conversations with pagination and filtering
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Query parameters
   * @returns Paginated list of conversations
   */
  async findAll(
    tenantId: number,
    query: ConversationQueryDto,
  ): Promise<PaginatedResult<ChatConversation>> {
    const {
      page = 1,
      limit = 10,
      pageId,
      status,
      conversationType,
      assignedAgentId,
      language,
      search,
      sortBy = 'lastMessageAt',
      sortDirection = 'DESC',
      startDate,
      endDate,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('conversation');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.where('conversation.tenantId = :tenantId', { tenantId });

    // Apply filters if provided
    if (pageId) {
      queryBuilder.andWhere('conversation.pageId = :pageId', { pageId });
    }

    if (status) {
      queryBuilder.andWhere('conversation.status = :status', { status });
    }

    if (conversationType) {
      queryBuilder.andWhere(
        'conversation.conversationType = :conversationType',
        { conversationType },
      );
    }

    if (assignedAgentId) {
      queryBuilder.andWhere('conversation.assignedAgentId = :assignedAgentId', {
        assignedAgentId,
      });
    }

    if (language) {
      queryBuilder.andWhere('conversation.language = :language', { language });
    }

    if (startDate && endDate) {
      queryBuilder.andWhere(
        'conversation.createdAt BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate,
        },
      );
    }

    // Apply search filter if provided
    if (search) {
      queryBuilder.andWhere('conversation.userName ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // Apply sorting
    queryBuilder.orderBy(`conversation.${sortBy}`, sortDirection);

    // Apply pagination
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find conversation by ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Conversation ID
   * @returns Conversation or null if not found
   */
  async findById(
    tenantId: number,
    id: number,
  ): Promise<ChatConversation | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Find conversation by Facebook user ID and page ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param facebookUserId Facebook user ID
   * @param pageId Facebook page ID
   * @returns Conversation or null if not found
   */
  async findByFacebookUserAndPage(
    tenantId: number,
    facebookUserId: string,
    pageId: string,
  ): Promise<ChatConversation | null> {
    return this.repository.findOne({
      where: { facebookUserId, pageId, tenantId },
    });
  }

  /**
   * Find conversations by page ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param pageId Facebook page ID
   * @returns List of conversations
   */
  async findByPageId(
    tenantId: number,
    pageId: string,
  ): Promise<ChatConversation[]> {
    return this.repository.find({
      where: { pageId, tenantId },
      order: { lastMessageAt: 'DESC' },
    });
  }

  /**
   * Find conversations assigned to agent
   * @param tenantId ID tenant (required for tenant isolation)
   * @param agentId Agent ID
   * @returns List of conversations
   */
  async findByAssignedAgent(
    tenantId: number,
    agentId: number,
  ): Promise<ChatConversation[]> {
    return this.repository.find({
      where: { assignedAgentId: agentId, tenantId },
      order: { lastMessageAt: 'DESC' },
    });
  }

  /**
   * Create a new conversation
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Conversation data
   * @returns Created conversation
   */
  async create(
    tenantId: number,
    data: Partial<ChatConversation>,
  ): Promise<ChatConversation> {
    const conversation = this.repository.create({ ...data, tenantId });
    return this.repository.save(conversation);
  }

  /**
   * Update conversation
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Conversation ID
   * @param data Updated conversation data
   * @returns Updated conversation or null if not found
   */
  async update(
    tenantId: number,
    id: number,
    data: Partial<ChatConversation>,
  ): Promise<ChatConversation | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Delete conversation
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Conversation ID
   * @returns True if deleted, false if not found
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Update last message timestamp
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Conversation ID
   * @param timestamp Last message timestamp
   * @returns Updated conversation or null if not found
   */
  async updateLastMessageAt(
    tenantId: number,
    id: number,
    timestamp: number,
  ): Promise<ChatConversation | null> {
    await this.repository.update(
      { id, tenantId },
      { lastMessageAt: timestamp },
    );
    return this.findById(tenantId, id);
  }

  /**
   * Alias for updateLastMessageAt for consistency
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Conversation ID
   * @param timestamp Last message timestamp
   */
  async updateLastMessageTime(
    tenantId: number,
    id: number,
    timestamp: number,
  ): Promise<void> {
    await this.repository.update(
      { id, tenantId },
      { lastMessageAt: timestamp, updatedAt: timestamp },
    );
  }

  /**
   * Find active conversation by user and page/source
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId User identifier
   * @param source Source type (webapp, facebook, etc.)
   * @returns Active conversation or null if not found
   */
  async findActiveByUser(
    tenantId: number,
    userId: string,
    source: string,
  ): Promise<ChatConversation | null> {
    return this.repository.findOne({
      where: {
        facebookUserId: userId,
        pageId: source,
        status: 'active',
        tenantId,
      },
      order: { lastMessageAt: 'DESC' },
    });
  }

  /**
   * Assign conversation to agent
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Conversation ID
   * @param agentId Agent ID
   * @returns Updated conversation or null if not found
   */
  async assignToAgent(
    tenantId: number,
    id: number,
    agentId: number,
  ): Promise<ChatConversation | null> {
    await this.repository.update(
      { id, tenantId },
      { assignedAgentId: agentId },
    );
    return this.findById(tenantId, id);
  }

  /**
   * Update conversation status
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Conversation ID
   * @param status New status
   * @returns Updated conversation or null if not found
   */
  async updateStatus(
    tenantId: number,
    id: number,
    status: string,
  ): Promise<ChatConversation | null> {
    await this.repository.update({ id, tenantId }, { status });
    return this.findById(tenantId, id);
  }

  /**
   * Count conversations by status
   * @param tenantId ID tenant (required for tenant isolation)
   * @param status Conversation status
   * @returns Number of conversations
   */
  async countByStatus(tenantId: number, status: string): Promise<number> {
    return this.repository.count({
      where: { status, tenantId },
    });
  }

  /**
   * Get active conversations count
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Number of active conversations
   */
  async getActiveConversationsCount(tenantId: number): Promise<number> {
    return this.repository.count({
      where: { status: 'active', tenantId },
    });
  }

  /**
   * Find conversations that need attention (no recent activity)
   * @param tenantId ID tenant (required for tenant isolation)
   * @param hoursThreshold Hours since last message
   * @returns List of conversations
   */
  async findStaleConversations(
    tenantId: number,
    hoursThreshold: number = 24,
  ): Promise<ChatConversation[]> {
    const thresholdTimestamp = Date.now() - hoursThreshold * 60 * 60 * 1000;

    return this.repository
      .createQueryBuilder('conversation')
      .where('conversation.tenantId = :tenantId', { tenantId })
      .andWhere('conversation.status = :status', { status: 'active' })
      .andWhere('conversation.lastMessageAt < :threshold', {
        threshold: thresholdTimestamp,
      })
      .orderBy('conversation.lastMessageAt', 'ASC')
      .getMany();
  }
}
