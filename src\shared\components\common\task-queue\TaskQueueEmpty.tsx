/**
 * Component hiển thị khi không có task nào trong queue
 */
import React from 'react';
import { Icon } from '@/shared/components/common';

/**
 * Props cho TaskQueueEmpty
 */
export interface TaskQueueEmptyProps {
  /**
   * Tiêu đề
   */
  title?: string;

  /**
   * Mô tả
   */
  description?: string;
}

/**
 * Component hiển thị khi không có task nào trong queue
 */
const TaskQueueEmpty: React.FC<TaskQueueEmptyProps> = ({
  title = 'Không có tác vụ nào',
  description = 'Hiện tại không có tác vụ nào trong hàng đợi.',
}) => {
  return (
    <div className="flex flex-col items-center justify-center py-6 px-4 text-center">
      <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center mb-4">
        <Icon name="list" className="w-8 h-8 text-muted" />
      </div>
      <h3 className="text-sm font-medium text-foreground mb-1">{title}</h3>
      <p className="text-xs text-muted">{description}</p>
    </div>
  );
};

export default TaskQueueEmpty;
