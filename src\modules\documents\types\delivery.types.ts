/**
 * <PERSON><PERSON><PERSON> c<PERSON> types cho Delivery trong module business
 */

/**
 * Interface cho thông tin dịch vụ
 */
export interface ServiceDeliveryInfo {
  serviceDate?: string;
  serviceTime?: string;
  serviceLocation?: string;
  serviceNotes?: string;
  contactPerson?: string;
  contactPhone?: string;
}

/**
 * Interface cho thông tin sự kiện
 */
export interface EventDeliveryInfo {
  eventDate?: string;
  eventTime?: string;
  eventLocation?: string;
  eventAddress?: string;
  ticketDeliveryMethod?: 'EMAIL' | 'SMS' | 'PICKUP' | 'MAIL';
  ticketRecipient?: string;
  eventNotes?: string;
  attendeeName?: string;
  attendeePhone?: string;
  attendeeEmail?: string;
}

/**
 * Enum cho phương thức giao vé sự kiện
 */
export enum TicketDeliveryMethod {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  PICKUP = 'PICKUP',
  MAIL = 'MAIL',
}

/**
 * Interface cho props của ServiceDeliveryForm
 */
export interface ServiceDeliveryFormProps {
  serviceInfo?: ServiceDeliveryInfo;
  onServiceInfoChange: (info: ServiceDeliveryInfo) => void;
  customerName?: string;
  customerPhone?: string;
}

/**
 * Interface cho props của EventDeliveryForm
 */
export interface EventDeliveryFormProps {
  eventInfo?: EventDeliveryInfo;
  onEventInfoChange: (info: EventDeliveryInfo) => void;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
}
