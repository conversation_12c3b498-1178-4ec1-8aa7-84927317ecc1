import { Injectable, Logger } from '@nestjs/common';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { RedisService } from '@shared/services/redis.service';
import { TodoService } from '@/modules/todolists/services/todo.service';
import { EmployeeService } from '@/modules/hrm/employees/services/employee.service';
import { StatisticsService } from '@/modules/todolists/services/statistics.service';

/**
 * Interface cho kết quả tìm kiếm vector
 */
interface VectorSearchResult {
  content: string;
  metadata: any;
  score: number;
  source: string;
}

/**
 * Interface cho context được xây dựng
 */
interface RAGContext {
  relevantData: VectorSearchResult[];
  businessMetrics: any;
  userContext: any;
  timestamp: number;
}

/**
 * Interface cho query analysis
 */
interface QueryAnalysis {
  intent: string;
  entities: any[];
  category: string;
  confidence: number;
  requiresRealTimeData: boolean;
}

/**
 * Service thực hiện RAG (Retrieval-Augmented Generation)
 */
@Injectable()
export class RAGService {
  private readonly logger = new Logger(RAGService.name);
  private readonly vectorStoreId = 'erp_business_data';

  constructor(
    private readonly openAiService: OpenAiService,
    private readonly redisService: RedisService,
    private readonly todoService: TodoService,
    private readonly employeeService: EmployeeService,
    private readonly statisticsService: StatisticsService,
  ) {}

  /**
   * Xử lý query với RAG
   */
  async processQuery(
    query: string,
    tenantId: number,
    userContext?: any,
  ): Promise<{
    answer: string;
    context: RAGContext;
    confidence: number;
  }> {
    try {
      // 1. Phân tích query
      const queryAnalysis = await this.analyzeQuery(query);

      // 2. Tìm kiếm vector
      const vectorResults = await this.searchVectorStore(query, tenantId);

      // 3. Lấy dữ liệu real-time nếu cần
      const realTimeData = queryAnalysis.requiresRealTimeData
        ? await this.getRealTimeBusinessData(queryAnalysis, tenantId)
        : null;

      // 4. Xây dựng context
      const context = await this.buildContext(
        vectorResults,
        realTimeData,
        userContext,
        queryAnalysis,
      );

      // 5. Tạo prompt và gọi LLM
      const prompt = await this.buildPrompt(query, context, queryAnalysis);
      const response = await this.openAiService.simpleChatCompletion(
        prompt,
        this.getSystemPrompt(queryAnalysis.category),
        'gpt-4',
      );

      return {
        answer: response,
        context,
        confidence: queryAnalysis.confidence,
      };
    } catch (error) {
      this.logger.error(`RAG processing error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Phân tích query để xác định intent và entities
   */
  private async analyzeQuery(query: string): Promise<QueryAnalysis> {
    const analysisPrompt = `
Phân tích câu hỏi sau và trả về JSON với format:
{
  "intent": "string", // get_statistics, get_employee_info, get_todo_status, general_question
  "entities": [], // các thực thể được nhắc đến
  "category": "string", // hrm, todo, general, statistics
  "confidence": number, // 0-1
  "requiresRealTimeData": boolean // có cần dữ liệu real-time không
}

Câu hỏi: "${query}"
`;

    try {
      const response = await this.openAiService.simpleChatCompletion(
        analysisPrompt,
        'Bạn là chuyên gia phân tích ngôn ngữ tự nhiên.',
        'gpt-3.5-turbo',
      );

      return JSON.parse(response);
    } catch (error) {
      // Fallback analysis
      return {
        intent: 'general_question',
        entities: [],
        category: 'general',
        confidence: 0.5,
        requiresRealTimeData: false,
      };
    }
  }

  /**
   * Tìm kiếm trong vector store
   */
  private async searchVectorStore(
    query: string,
    tenantId: number,
  ): Promise<VectorSearchResult[]> {
    try {
      // Tạo embedding cho query
      const embeddingResponse = await this.openAiService.createEmbedding(
        query,
        'text-embedding-ada-002',
      );

      // Tìm kiếm vector (giả lập - cần implement vector database thực tế)
      const searchResults = await this.performVectorSearch(
        embeddingResponse.data[0].embedding,
        tenantId,
      );

      return searchResults;
    } catch (error) {
      this.logger.error(`Vector search error: ${error.message}`);
      return [];
    }
  }

  /**
   * Lấy dữ liệu business real-time
   */
  private async getRealTimeBusinessData(
    analysis: QueryAnalysis,
    tenantId: number,
  ): Promise<any> {
    const data: any = {};

    try {
      if (
        analysis.category === 'todo' ||
        analysis.intent === 'get_statistics'
      ) {
        // TODO: Implement these methods in respective services
        data.todoStats = { total: 100, completed: 80, pending: 20 };
        data.overdueTodos = [];
        data.monthlyCompletedTodos = [];
      }

      if (analysis.category === 'hrm') {
        // TODO: Implement these methods in respective services
        data.employeeStats = { total: 50, active: 45, inactive: 5 };
        data.lateEmployees = [];
      }

      return data;
    } catch (error) {
      this.logger.error(`Error getting real-time data: ${error.message}`);
      return {};
    }
  }

  /**
   * Xây dựng context cho prompt
   */
  private async buildContext(
    vectorResults: VectorSearchResult[],
    realTimeData: any,
    userContext: any,
    analysis: QueryAnalysis,
  ): Promise<RAGContext> {
    return {
      relevantData: vectorResults,
      businessMetrics: realTimeData,
      userContext: userContext || {},
      timestamp: Date.now(),
    };
  }

  /**
   * Xây dựng prompt cho LLM
   */
  private async buildPrompt(
    query: string,
    context: RAGContext,
    analysis: QueryAnalysis,
  ): Promise<string> {
    let prompt = `Câu hỏi: ${query}\n\n`;

    // Thêm dữ liệu từ vector search
    if (context.relevantData.length > 0) {
      prompt += 'Thông tin liên quan từ hệ thống:\n';
      context.relevantData.forEach((item, index) => {
        prompt += `${index + 1}. ${item.content} (Nguồn: ${item.source})\n`;
      });
      prompt += '\n';
    }

    // Thêm dữ liệu real-time
    if (context.businessMetrics) {
      prompt += 'Dữ liệu hiện tại:\n';

      if (context.businessMetrics.todoStats) {
        prompt += `- Thống kê công việc: ${JSON.stringify(context.businessMetrics.todoStats)}\n`;
      }

      if (context.businessMetrics.overdueTodos) {
        prompt += `- Số công việc chậm deadline: ${context.businessMetrics.overdueTodos.length}\n`;
      }

      if (context.businessMetrics.employeeStats) {
        prompt += `- Thống kê nhân viên: ${JSON.stringify(context.businessMetrics.employeeStats)}\n`;
      }

      prompt += '\n';
    }

    prompt +=
      'Hãy trả lời câu hỏi dựa trên thông tin trên một cách chính xác và hữu ích.';

    return prompt;
  }

  /**
   * Lấy system prompt theo category
   */
  private getSystemPrompt(category: string): string {
    const basePrompt = 'Bạn là trợ lý AI thông minh của hệ thống ERP. ';

    switch (category) {
      case 'hrm':
        return (
          basePrompt +
          'Bạn chuyên về quản lý nhân sự, có thể trả lời các câu hỏi về nhân viên, phòng ban, hợp đồng.'
        );
      case 'todo':
        return (
          basePrompt +
          'Bạn chuyên về quản lý công việc, có thể trả lời về tiến độ, deadline, thống kê công việc.'
        );
      case 'statistics':
        return (
          basePrompt +
          'Bạn chuyên về phân tích dữ liệu và thống kê, có thể đưa ra insights từ số liệu.'
        );
      default:
        return basePrompt + 'Hãy trả lời một cách chính xác và hữu ích.';
    }
  }

  /**
   * Thực hiện vector search (cần implement với vector database thực tế)
   */
  private async performVectorSearch(
    queryEmbedding: number[],
    tenantId: number,
  ): Promise<VectorSearchResult[]> {
    // Placeholder - cần implement với Pinecone, Weaviate, hoặc vector DB khác
    return [];
  }

  /**
   * Lấy danh sách nhân viên đi muộn trong tháng (placeholder)
   */
  private async getLateEmployeesThisMonth(tenantId: number): Promise<any[]> {
    // Placeholder - cần implement với attendance system
    return [];
  }
}
