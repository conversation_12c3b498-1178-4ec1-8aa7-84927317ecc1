import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Card, Icon, Badge, Table, CollapsibleCard } from '@/shared/components/common';
import { CustomerDetailData, CustomerInteraction } from './types';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { SortDirection } from '@/shared/dto/request/query.dto';

interface CustomerInteractionsProps {
  customer: CustomerDetailData;
}

// Define query params interface for interactions
interface InteractionQueryParams {
  page: number;
  limit: number;
  search?: string;
  sortBy?: string;
  sortDirection?: SortDirection;
  type?: string;
  status?: string;
}

/**
 * Component hiển thị tương tác của khách hàng
 */
const CustomerInteractions: React.FC<CustomerInteractionsProps> = ({ customer }) => {
  const { t } = useTranslation('business');

  // Format date
  const formatDate = useCallback((dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return dateString;
    }
  }, []);

  // Get interaction icon
  const getInteractionIcon = useCallback((type: string) => {
    switch (type) {
      case 'email':
        return 'mail';
      case 'phone':
        return 'phone';
      case 'chat':
        return 'message-circle';
      case 'social':
        return 'users';
      case 'meeting':
        return 'calendar';
      default:
        return 'message-square';
    }
  }, []);

  // Get interaction color
  const getInteractionColor = useCallback((type: string) => {
    switch (type) {
      case 'email':
        return 'text-blue-600';
      case 'phone':
        return 'text-green-600';
      case 'chat':
        return 'text-purple-600';
      case 'social':
        return 'text-pink-600';
      case 'meeting':
        return 'text-orange-600';
      default:
        return 'text-gray-600';
    }
  }, []);

  // Get interaction background color
  const getInteractionBgColor = useCallback((type: string) => {
    switch (type) {
      case 'email':
        return 'bg-blue-50';
      case 'phone':
        return 'bg-green-50';
      case 'chat':
        return 'bg-purple-50';
      case 'social':
        return 'bg-pink-50';
      case 'meeting':
        return 'bg-orange-50';
      default:
        return 'bg-gray-50';
    }
  }, []);

  // Get status variant
  const getStatusVariant = useCallback((status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'danger';
      default:
        return 'info';
    }
  }, []);

  // Get status text
  const getStatusText = useCallback((status: string) => {
    return t(`customer.interaction.statuses.${status}`, status);
  }, [t]);

  // Define columns for interactions table
  const columns: TableColumn<CustomerInteraction>[] = useMemo(() => [
    {
      key: 'type',
      title: t('customer.interaction.type'),
      dataIndex: 'type',
      render: (value: unknown) => {
        const typeValue = String(value);
        return (
          <div className="flex items-center space-x-2">
            <div className={`p-2 rounded-full ${getInteractionBgColor(typeValue)}`}>
              <Icon name={getInteractionIcon(typeValue)} size="sm" className={getInteractionColor(typeValue)} />
            </div>
            <Typography variant="body2" className="text-foreground capitalize">
              {t(`customer.interaction.type.${typeValue}`, typeValue)}
            </Typography>
          </div>
        );
      },
    },
    {
      key: 'title',
      title: t('customer.interaction.title'),
      dataIndex: 'title',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-foreground font-medium">
          {String(value)}
        </Typography>
      ),
    },
    {
      key: 'channel',
      title: t('customer.interaction.channel'),
      dataIndex: 'channel',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-muted">
          {String(value)}
        </Typography>
      ),
    },
    {
      key: 'status',
      title: t('customer.interaction.status'),
      dataIndex: 'status',
      render: (value: unknown) => {
        const statusValue = String(value);
        return (
          <Badge variant={getStatusVariant(statusValue)} size="sm">
            {getStatusText(statusValue)}
          </Badge>
        );
      },
    },
    {
      key: 'date',
      title: t('customer.interaction.date'),
      dataIndex: 'date',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-muted">
          {formatDate(String(value))}
        </Typography>
      ),
    },
  ], [t, getInteractionIcon, getInteractionBgColor, getInteractionColor, getStatusVariant, getStatusText, formatDate]);

  // Create query params function
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
  }): InteractionQueryParams => ({
    page: params.page,
    limit: params.pageSize,
    search: params.searchTerm || undefined,
    sortBy: params.sortBy || undefined,
    sortDirection: params.sortDirection || undefined,
    type: params.filterValue && params.filterValue !== 'all' ? String(params.filterValue) : undefined,
  });

  // Filter options for interaction types
  const filterOptions = [
    { id: 'all', label: t('common.all'), icon: 'list', value: 'all' },
    { id: 'email', label: t('customer.interaction.types.email', 'Email'), icon: 'mail', value: 'email' },
    { id: 'phone', label: t('customer.interaction.types.phone', 'Phone'), icon: 'phone', value: 'phone' },
    { id: 'chat', label: t('customer.interaction.types.chat', 'Chat'), icon: 'message-square', value: 'chat' },
    { id: 'social', label: t('customer.interaction.types.social', 'Social'), icon: 'users', value: 'social' },
    { id: 'meeting', label: t('customer.interaction.types.meeting', 'Meeting'), icon: 'calendar', value: 'meeting' },
  ];

  // Use data table hook
  const dataTable = useDataTable(
    useDataTableConfig<CustomerInteraction, InteractionQueryParams>({
      columns,
      filterOptions,
      createQueryParams,
    })
  );

  // Mock data - in real app, this would come from API
  const interactionsData = {
    items: customer.interactions || [],
    meta: {
      currentPage: 1,
      totalItems: customer.interactions?.length || 0,
      totalPages: 1,
    },
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="text-foreground">
            {t('customer.detail.interactions')}
          </Typography>
          <Typography variant="body2" className="text-muted">
            {customer.interactions?.length || 0} {t('customer.detail.interactions').toLowerCase()}
          </Typography>
        </div>
      }
      defaultOpen={false}
    >
      <div className="space-y-4">
        {/* Menu Icon Bar */}
        <MenuIconBar
          onSearch={dataTable.tableData.handleSearch}
          items={dataTable.menuItems}
          onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
          columns={dataTable.columnVisibility.visibleColumns}
          showDateFilter={false}
          showColumnFilter={true}
        />

        {/* Table */}
        <Card className="overflow-hidden">
          <Table<CustomerInteraction>
            columns={dataTable.columnVisibility.visibleTableColumns}
            data={interactionsData.items}
            rowKey="id"
            loading={false}
            sortable={true}
            onSortChange={dataTable.tableData.handleSortChange}
            pagination={{
              current: interactionsData.meta.currentPage,
              pageSize: dataTable.tableData.pageSize,
              total: interactionsData.meta.totalItems,
              onChange: dataTable.tableData.handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>
    </CollapsibleCard>
  );
};

export default CustomerInteractions;
