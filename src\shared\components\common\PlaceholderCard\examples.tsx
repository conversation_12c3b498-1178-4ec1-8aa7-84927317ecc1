import React from 'react';

import PlaceholderCard from './PlaceholderCard';

/**
 * Common usage examples for PlaceholderCard component
 */

// Example 1: Statistics placeholder (like in HRM employees page)
export const StatsPlaceholder: React.FC = () => (
  <PlaceholderCard
    icon="bar-chart"
    title="hrm:employee.stats.title"
    description="hrm:employee.stats.description"
    titleFallback="Thống kê nhân viên"
    descriptionFallback="Tính năng thống kê đang được phát triển"
    namespace="hrm"
  />
);

// Example 2: Reports placeholder
export const ReportsPlaceholder: React.FC = () => (
  <PlaceholderCard
    icon="file-text"
    title="Reports Dashboard"
    description="Advanced reporting features are coming soon"
    iconSize="xl"
  />
);

// Example 3: Analytics placeholder
export const AnalyticsPlaceholder: React.FC = () => (
  <PlaceholderCard
    icon="trending-up"
    title="Analytics"
    description="Data visualization and insights will be available soon"
  />
);

// Example 4: Calendar placeholder
export const CalendarPlaceholder: React.FC = () => (
  <PlaceholderCard
    icon="calendar"
    title="Calendar Integration"
    description="Schedule management features are under development"
  />
);

// Example 5: Settings placeholder
export const SettingsPlaceholder: React.FC = () => (
  <PlaceholderCard
    icon="settings"
    title="Advanced Settings"
    description="Configuration options will be available in the next update"
  />
);

// Example 6: Empty state for data
export const EmptyDataPlaceholder: React.FC<{ entityName?: string }> = ({ 
  entityName = "items" 
}) => (
  <PlaceholderCard
    icon="inbox"
    title={`No ${entityName} found`}
    description={`There are no ${entityName} to display at the moment`}
    showCard={false}
    contentClassName="text-center py-8"
  />
);

// Example 7: Error state
export const ErrorPlaceholder: React.FC = () => (
  <PlaceholderCard
    icon="alert-circle"
    title="Something went wrong"
    description="Please try again later or contact support if the problem persists"
    iconClassName="mx-auto mb-4 text-red-500"
    titleClassName="text-lg font-medium mb-2 text-red-700"
    descriptionClassName="text-red-600"
  />
);

// Example 8: Loading state
export const LoadingPlaceholder: React.FC = () => (
  <PlaceholderCard
    icon="loader"
    title="Loading..."
    description="Please wait while we fetch your data"
    iconClassName="mx-auto mb-4 text-blue-500 animate-spin"
  />
);

// Example 9: Premium feature
export const PremiumFeaturePlaceholder: React.FC = () => (
  <PlaceholderCard
    icon="star"
    title="Premium Feature"
    description="Upgrade your plan to access this feature"
    className="p-6 bg-gradient-to-br from-yellow-50 to-orange-50 border-2 border-yellow-200"
    iconClassName="mx-auto mb-4 text-yellow-500"
    titleClassName="text-lg font-medium mb-2 text-yellow-800"
    descriptionClassName="text-yellow-700"
  />
);

// Example 10: Maintenance mode
export const MaintenancePlaceholder: React.FC = () => (
  <PlaceholderCard
    icon="tool"
    title="Under Maintenance"
    description="This feature is temporarily unavailable due to maintenance"
    iconClassName="mx-auto mb-4 text-orange-500"
  />
);
