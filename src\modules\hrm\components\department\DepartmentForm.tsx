import React from 'react';
import { FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { Button, Card, Form, FormItem, Input, Textarea } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { useFormErrors } from '@/shared/hooks';

import { loadDepartmentsForAsyncSelect } from '../../hooks/useDepartments';
import { loadEmployeesForAsyncSelect } from '../../hooks/useEmployees';
import { useInitialOptions } from '../../hooks/useInitialOptions';
import { createDepartmentSchema } from '../../schemas/department.schema';

// Định nghĩa props cho component
interface DepartmentFormProps {
  initialData?: z.infer<typeof createDepartmentSchema>;
  onSubmit: (data: z.infer<typeof createDepartmentSchema>) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
}

/**
 * Form tạo/cập nhật phòng ban
 */
const DepartmentForm: React.FC<DepartmentFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['hrm', 'common']);
  const isEditMode = !!initialData?.name;

  // Form hook with error handling
  const { formRef, setFormErrors } = useFormErrors<z.infer<typeof createDepartmentSchema>>();

  // Load initial options cho AsyncSelectWithPagination trong chế độ edit
  const { departmentOptions, managerOptions } = useInitialOptions({
    departmentId: initialData?.parentId,
    managerId: initialData?.managerId,
  });

  // Xử lý submit form
  const handleSubmit = (values: unknown) => {
    try {
      // Reset form errors
      setFormErrors({});

      // Type assertion to the correct type
      const departmentValues = values as z.infer<typeof createDepartmentSchema>;

      // Chuyển đổi giá trị từ string sang number cho parentId và managerId
      if (departmentValues.parentId && typeof departmentValues.parentId === 'string') {
        departmentValues.parentId = departmentValues.parentId
          ? Number(departmentValues.parentId)
          : null;
      }

      if (departmentValues.managerId && typeof departmentValues.managerId === 'string') {
        departmentValues.managerId = departmentValues.managerId
          ? Number(departmentValues.managerId)
          : null;
      }

      // Call the onSubmit callback
      onSubmit(departmentValues);
    } catch (error) {
      console.error('Error submitting department form:', error);

      // Check if error has field-specific errors
      if (error && typeof error === 'object' && 'response' in error && error.response) {
        const axiosError = error as {
          response: { data?: { message?: string; errors?: Record<string, string> } };
        };

        // If there are field-specific errors, set them
        if (axiosError.response.data?.errors) {
          setFormErrors(axiosError.response.data.errors);
        }
      }
    }
  };

  return (
    <Card>
      <Form
        ref={formRef as unknown as React.RefObject<FormRef<FieldValues>>}
        schema={createDepartmentSchema}
        onSubmit={handleSubmit as unknown as (data: FieldValues) => void}
        className="space-y-6"
        defaultValues={initialData || {}}
      >
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-6">
            {isEditMode
              ? t('hrm:department.form.editTitle', 'Cập nhật phòng ban')
              : t('hrm:department.form.createTitle', 'Thêm phòng ban mới')}
          </h2>

          <div className="space-y-6">
            <FormItem name="name" label={t('hrm:department.form.name', 'Tên phòng ban')} required>
              <Input
                placeholder={t('hrm:department.form.namePlaceholder', 'Nhập tên phòng ban')}
                fullWidth
              />
            </FormItem>

            <FormItem name="description" label={t('hrm:department.form.description', 'Mô tả')}>
              <Textarea
                rows={4}
                placeholder={t(
                  'hrm:department.form.descriptionPlaceholder',
                  'Nhập mô tả phòng ban'
                )}
              />
            </FormItem>

            <FormItem
              name="parentId"
              label={t('hrm:department.form.parentId', 'Phòng ban cấp trên')}
            >
              <AsyncSelectWithPagination
                loadOptions={loadDepartmentsForAsyncSelect}
                initialOptions={departmentOptions}
                placeholder={t(
                  'hrm:department.form.parentDepartmentPlaceholder',
                  'Tìm kiếm phòng ban cấp trên...'
                )}
                debounceTime={300}
                noOptionsMessage={t('common:noResults', 'Không tìm thấy phòng ban')}
                loadingMessage={t('common:loading', 'Đang tìm kiếm...')}
                autoLoadInitial={true}
                itemsPerPage={10}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="managerId"
              label={t('hrm:department.form.managerId', 'Quản lý phòng ban')}
            >
              <AsyncSelectWithPagination
                loadOptions={loadEmployeesForAsyncSelect}
                initialOptions={managerOptions}
                placeholder={t(
                  'hrm:department.form.managerIdPlaceholder',
                  'Tìm kiếm quản lý phòng ban...'
                )}
                debounceTime={300}
                noOptionsMessage={t('common:noResults', 'Không tìm thấy nhân viên')}
                loadingMessage={t('common:loading', 'Đang tìm kiếm...')}
                autoLoadInitial={true}
                itemsPerPage={10}
                fullWidth
              />
            </FormItem>
          </div>
        </div>

        <div className="flex items-center justify-end gap-2 p-6 border-t">
          <Button type="button" variant="outline" onClick={onCancel}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button type="submit" isLoading={isSubmitting}>
            {isSubmitting ? t('common:saving', 'Đang lưu...') : t('common:save', 'Lưu')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default DepartmentForm;
