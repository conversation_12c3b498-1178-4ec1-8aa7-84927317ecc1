import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum cho loại component của trường tùy chỉnh
 */
export enum CustomFieldComponentEnum {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  NUMBER = 'number',
  SELECT = 'select',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  DATE = 'date',
  TIME = 'time',
  DATETIME = 'datetime',
  FILE = 'file',
  IMAGE = 'image',
  PHONE = 'phone',
  EMAIL = 'email',
  URL = 'url',
  PASSWORD = 'password',
  HIDDEN = 'hidden',
  RANGE = 'range',
  COLOR = 'color',
  SWITCH = 'switch',
  RATING = 'rating',
  AUTOCOMPLETE = 'autocomplete',
  MULTI_SELECT = 'multi-select',
  RICH_TEXT = 'rich-text',
}

/**
 * Enum cho loại dữ liệu của trường tùy chỉnh
 */
export enum CustomFieldTypeEnum {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  DATE = 'date',
  OBJECT = 'object',
  ARRAY = 'array',
}

/**
 * Interface cho cấu hình trường tùy chỉnh
 */
export interface CustomFieldConfig {
  id?: string;
  label: string;
  type: string;
  required: boolean;
  validation?: Record<string, unknown>;
  placeholder?: string;
  defaultValue?: string | number | boolean | null;
  options?: Array<{ label: string; value: string | number | boolean }>;
  [key: string]: unknown;
}

/**
 * Interface cho trường tùy chỉnh
 */
export interface CustomFieldDto {
  id: number;
  component: CustomFieldComponentEnum;
  config: CustomFieldConfig;
  formGroupId?: number;
  grid?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho danh sách trường tùy chỉnh
 */
export interface CustomFieldListItemDto {
  id: number;
  component: CustomFieldComponentEnum;
  config: CustomFieldConfig;
  formGroupId?: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho chi tiết trường tùy chỉnh
 */
export interface CustomFieldDetailResponseDto extends CustomFieldDto {
  formGroup?: CustomGroupFormDto;
}

/**
 * Interface cho nhóm trường tùy chỉnh
 */
export interface CustomGroupFormDto {
  id: number;
  label: string;
  productId?: number;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho danh sách nhóm trường tùy chỉnh
 */
export interface CustomGroupFormListItemDto {
  id: number;
  label: string;
  productId?: number;
  fieldsCount: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho chi tiết nhóm trường tùy chỉnh
 */
export interface CustomGroupFormResponseDto extends CustomGroupFormDto {
  fields: CustomFieldDto[];
}

/**
 * Interface cho tham số truy vấn trường tùy chỉnh
 */
export interface QueryCustomFieldDto extends QueryDto {
  type?: string;
  sort?: string;
}

/**
 * Interface cho tham số truy vấn nhóm trường tùy chỉnh
 */
export interface QueryCustomGroupFormDto extends QueryDto {
  productId?: number;
}

/**
 * Interface cho dữ liệu tạo trường tùy chỉnh
 */
export interface CreateCustomFieldDto {
  component: CustomFieldComponentEnum;
  configId?: string;
  label: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  formGroupId?: number;
  grid?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  value?: Record<string, unknown>;
  userId?: number;
}

/**
 * Interface cho dữ liệu cập nhật trường tùy chỉnh
 */
export interface UpdateCustomFieldDto {
  component?: CustomFieldComponentEnum;
  label?: string;
  type?: string;
  required?: boolean;
  configJson?: Record<string, unknown>;
}

/**
 * Interface cho dữ liệu tạo nhóm trường tùy chỉnh
 */
export interface CreateCustomGroupFormDto {
  label: string;
  productId?: number;
  userId?: number;
}

/**
 * Interface cho dữ liệu cập nhật nhóm trường tùy chỉnh
 */
export interface UpdateCustomGroupFormDto {
  label?: string;
  userId?: number;
}
