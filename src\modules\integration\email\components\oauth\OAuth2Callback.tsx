/**
 * OAuth2 Callback Handler Component
 */

import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Icon, Button } from '@/shared/components/common';
import { useOAuth2 } from '../../hooks/useOAuth2';

export interface OAuth2CallbackProps {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export const OAuth2Callback: React.FC<OAuth2CallbackProps> = ({
  onSuccess,
  onError
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Extract OAuth2 parameters from URL
  const code = searchParams.get('code');
  const state = searchParams.get('state');
  const error = searchParams.get('error');
  const providerId = searchParams.get('provider') || 'gmail'; // Default to gmail

  const oauth2 = useOAuth2({
    providerId,
    onSuccess: (tokens) => {
      setStatus('success');
      onSuccess?.();
      
      // Close popup if this is running in a popup
      if (window.opener) {
        window.opener.postMessage({
          type: 'oauth_success',
          providerId,
          tokens
        }, window.location.origin);
        window.close();
      } else {
        // Redirect back to integration page after 2 seconds
        setTimeout(() => {
          navigate('/integration/email');
        }, 2000);
      }
    },
    onError: (error) => {
      setStatus('error');
      setErrorMessage(error.message);
      onError?.(error);
    }
  });

  useEffect(() => {
    const handleOAuth2Callback = async () => {
      // Check for OAuth2 error
      if (error) {
        setStatus('error');
        setErrorMessage(error);
        return;
      }

      // Check for required parameters
      if (!code || !state) {
        setStatus('error');
        setErrorMessage(t('integration:oauth.missingParameters', 'Thiếu thông số OAuth2'));
        return;
      }

      try {
        // Exchange code for tokens
        await oauth2.exchangeCodeForTokens(code, state);
      } catch (err) {
        console.error('OAuth2 callback error:', err);
        setStatus('error');
        setErrorMessage(err instanceof Error ? err.message : 'Unknown error');
      }
    };

    handleOAuth2Callback();
  }, [code, state, error, oauth2, t]);

  const handleRetry = () => {
    if (window.opener) {
      window.close();
    } else {
      navigate('/integration/email');
    }
  };

  const renderContent = () => {
    switch (status) {
      case 'processing':
        return (
          <div className="text-center py-8">
            <Icon name="loader" className="w-8 h-8 mx-auto mb-4 animate-spin text-blue-600" />
            <Typography variant="h6" className="mb-2">
              {t('integration:oauth.processing', 'Đang xử lý xác thực...')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('integration:oauth.processingDescription', 'Vui lòng đợi trong khi chúng tôi xác thực tài khoản của bạn.')}
            </Typography>
          </div>
        );

      case 'success':
        return (
          <div className="text-center py-8">
            <Icon name="check-circle" className="w-8 h-8 mx-auto mb-4 text-green-600" />
            <Typography variant="h6" className="mb-2 text-green-800">
              {t('integration:oauth.success', 'Xác thực thành công!')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('integration:oauth.successDescription', 'Tài khoản của bạn đã được kết nối thành công.')}
            </Typography>
            {!window.opener && (
              <Typography variant="caption" className="block mt-4 text-muted-foreground">
                {t('integration:oauth.redirecting', 'Đang chuyển hướng...')}
              </Typography>
            )}
          </div>
        );

      case 'error':
        return (
          <div className="text-center py-8">
            <Icon name="x-circle" className="w-8 h-8 mx-auto mb-4 text-red-600" />
            <Typography variant="h6" className="mb-2 text-red-800">
              {t('integration:oauth.failed', 'Xác thực thất bại')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground mb-4">
              {errorMessage || t('integration:oauth.failedDescription', 'Có lỗi xảy ra trong quá trình xác thực.')}
            </Typography>
            <Button variant="primary" onClick={handleRetry}>
              {window.opener 
                ? t('common:close', 'Đóng')
                : t('integration:oauth.backToIntegration', 'Quay lại trang tích hợp')
              }
            </Button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <div className="p-6">
          <div className="text-center mb-6">
            <Typography variant="h5">
              {t('integration:oauth.title', 'Xác thực OAuth2')}
            </Typography>
          </div>
          
          {renderContent()}
        </div>
      </Card>
    </div>
  );
};

export default OAuth2Callback;
