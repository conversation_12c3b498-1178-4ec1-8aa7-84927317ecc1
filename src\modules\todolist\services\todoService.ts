import { apiClient } from '@/shared/api/axios';
import { TaskDto, TaskStatus, TaskPriority } from '../types/task.types';

/**
 * Score Todo DTO
 */
export interface ScoreTodoDto {
  /**
   * Số sao được chấm (1-5)
   */
  score: number;
  
  /**
   * <PERSON>hi chú khi chấm điểm (tùy chọn)
   */
  note?: string;
}

/**
 * Update Task Description DTO
 */
export interface UpdateTaskDescriptionDto {
  description: string;
}

/**
 * Update Task Status DTO
 */
export interface UpdateTaskStatusDto {
  status: TaskStatus;
}

/**
 * Update Task Priority DTO
 */
export interface UpdateTaskPriorityDto {
  priority: TaskPriority;
}

/**
 * Update Task Assignee DTO
 */
export interface UpdateTaskAssigneeDto {
  assigneeId: number;
}

/**
 * API Response wrapper
 */
export interface ApiResponseDto<T> {
  success: boolean;
  message: string;
  data: T;
}

/**
 * Todo service for API calls
 */
export class TodoService {
  /**
   * Score a todo (chấm điểm sao)
   */
  static async scoreTodo(
    todoId: number,
    scoreData: ScoreTodoDto
  ): Promise<ApiResponseDto<TaskDto>> {
    const response = await apiClient.patch(`/api/todolists/todos/${todoId}/score`, scoreData);
    return response.data;
  }

  /**
   * Update task description
   */
  static async updateTaskDescription(
    taskId: number,
    data: UpdateTaskDescriptionDto
  ): Promise<ApiResponseDto<TaskDto>> {
    const response = await apiClient.patch(`/api/todolists/todos/${taskId}/description`, data);
    return response.data;
  }

  /**
   * Update task status
   */
  static async updateTaskStatus(
    taskId: number,
    data: UpdateTaskStatusDto
  ): Promise<ApiResponseDto<TaskDto>> {
    const response = await apiClient.patch(`/api/todolists/todos/${taskId}/status`, data);
    return response.data;
  }

  /**
   * Update task priority
   */
  static async updateTaskPriority(
    taskId: number,
    data: UpdateTaskPriorityDto
  ): Promise<ApiResponseDto<TaskDto>> {
    const response = await apiClient.patch(`/api/todolists/todos/${taskId}/priority`, data);
    return response.data;
  }

  /**
   * Update task assignee
   */
  static async updateTaskAssignee(
    taskId: number,
    data: UpdateTaskAssigneeDto
  ): Promise<ApiResponseDto<TaskDto>> {
    const response = await apiClient.patch(`/api/todolists/todos/${taskId}/assignee`, data);
    return response.data;
  }

  /**
   * Get task by ID
   */
  static async getTaskById(taskId: number): Promise<ApiResponseDto<TaskDto>> {
    const response = await apiClient.get(`/api/todolists/todos/${taskId}`);
    return response.data;
  }
}

export default TodoService;
