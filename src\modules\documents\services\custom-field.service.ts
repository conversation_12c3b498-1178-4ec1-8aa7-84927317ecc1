import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Interface cho trường tùy chỉnh
 */
export interface CustomField {
  id: number;
  component: string;
  configId: string;
  label: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  employeeId: number | null;
  userId: number | null;
  createAt: number;
  status: string;
}

/**
 * Interface cho danh sách trường tùy chỉnh
 */
export interface CustomFieldListItem {
  id: number;
  component: string;
  configId?: string;
  label: string;
  type: string;
  required: boolean;
  createAt: number;
  status: string;
}

/**
 * Interface cho chi tiết trường tùy chỉnh
 */
export interface CustomFieldDetail {
  id: number;
  component: string;
  configId: string;
  label: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  createAt: number;
  status: string;
  linkedGroups: {
    id: number;
    label: string;
  }[];
}

/**
 * Interface cho tham số truy vấn trường tùy chỉnh
 */
export interface CustomFieldQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: string;
  component?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface cho dữ liệu tạo trường tùy chỉnh
 */
export interface CreateCustomFieldData {
  component: string;
  config: {
    id?: string;
    label: string;
    type: string;
    required?: boolean;
    validation?: Record<string, unknown>;
    placeholder?: string;
    defaultValue?: unknown;
    [key: string]: unknown;
  };
  formGroupId?: number;
  grid?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}

/**
 * Interface cho dữ liệu cập nhật trường tùy chỉnh
 */
export interface UpdateCustomFieldData {
  component?: string;
  config?: {
    label?: string;
    type?: string;
    required?: boolean;
    validation?: Record<string, unknown>;
    placeholder?: string;
    defaultValue?: unknown;
    [key: string]: unknown;
  };
}

/**
 * Service xử lý API liên quan đến trường tùy chỉnh
 */
export const CustomFieldService = {
  /**
   * Lấy danh sách trường tùy chỉnh
   * @param params Tham số truy vấn
   * @returns Danh sách trường tùy chỉnh với phân trang
   */
  getCustomFields: async (params?: CustomFieldQueryParams): Promise<ApiResponseDto<PaginatedResult<CustomFieldListItem>>> => {
    return apiRequest.get('/user/custom-fields', { params });
  },

  /**
   * Lấy chi tiết trường tùy chỉnh theo ID
   * @param id ID của trường tùy chỉnh
   * @returns Chi tiết trường tùy chỉnh
   */
  getCustomFieldById: async (id: number): Promise<ApiResponseDto<CustomFieldDetail>> => {
    return apiRequest.get(`/user/custom-fields/${id}`);
  },

  /**
   * Tạo trường tùy chỉnh mới
   * @param data Dữ liệu tạo trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh đã tạo
   */
  createCustomField: async (data: CreateCustomFieldData): Promise<ApiResponseDto<CustomField>> => {
    return apiRequest.post('/user/custom-fields', data);
  },

  /**
   * Cập nhật trường tùy chỉnh
   * @param id ID của trường tùy chỉnh
   * @param data Dữ liệu cập nhật trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh đã cập nhật
   */
  updateCustomField: async (id: number, data: UpdateCustomFieldData): Promise<ApiResponseDto<CustomField>> => {
    return apiRequest.put(`/user/custom-fields/${id}`, data);
  },

  /**
   * Xóa trường tùy chỉnh
   * @param id ID của trường tùy chỉnh
   * @returns Thông báo xóa thành công
   */
  deleteCustomField: async (id: number): Promise<ApiResponseDto<null>> => {
    return apiRequest.delete(`/user/custom-fields/${id}`);
  },

  /**
   * Xóa nhiều trường tùy chỉnh
   * @param customFieldIds Danh sách ID của các trường tùy chỉnh cần xóa
   * @returns Kết quả xóa
   */
  deleteMultipleCustomFields: async (customFieldIds: number[]): Promise<ApiResponseDto<void>> => {
    return apiRequest.delete('/user/custom-fields/bulk', {
      data: { customFieldIds }
    });
  },
};
