import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState, useCallback } from 'react';
import {
  CustomerImportBusinessService,
} from '../services';
import {
  ImportState,
  ExcelData,
  ColumnMapping,
  MappingTemplate,
  ImportSourceType,
} from '../types/customer-import.types';
import {
  CUSTOMER_IMPORT_QUERY_KEYS,
  DEFAULT_VALIDATION_RULES,
  DEFAULT_IMPORT_CONFIG,
} from '../constants/customer-import.constants';

/**
 * Initial import state
 */
const initialImportState: ImportState = {
  step: 'upload',
  excelData: null,
  mappings: [],
  validationResult: null,
  importProgress: 0,
  importedCount: 0,
  errorCount: 0,
  selectedSource: null,
  selectedTemplate: null,
  autoMappingSuggestions: [],
  dataQualityMetrics: null,
  validationRules: DEFAULT_VALIDATION_RULES,
  batchConfig: DEFAULT_IMPORT_CONFIG,
  currentJob: null,
};

/**
 * Hook chính cho Customer Import functionality
 */
export const useCustomerImport = () => {
  const queryClient = useQueryClient();
  const [importState, setImportState] = useState<ImportState>(initialImportState);

  // Update import state
  const updateImportState = useCallback((updates: Partial<ImportState>) => {
    setImportState(prev => ({ ...prev, ...updates }));
  }, []);

  // Reset import state
  const resetImportState = useCallback(() => {
    setImportState(initialImportState);
  }, []);

  // Upload file mutation
  const uploadFileMutation = useMutation({
    mutationFn: async ({ file, hasHeader }: { file: File; hasHeader: boolean }) => {
      return CustomerImportBusinessService.uploadAndProcessFile(file, hasHeader);
    },
    onSuccess: (data) => {
      updateImportState({
        step: 'mapping',
        excelData: data.excelData,
        autoMappingSuggestions: data.autoMappingSuggestions,
        dataQualityMetrics: data.dataQualityMetrics,
      });
    },
    onError: (error) => {
      console.error('Upload failed:', error);
    },
  });

  // Generate smart mappings mutation
  const generateMappingsMutation = useMutation({
    mutationFn: async (excelData: ExcelData) => {
      return CustomerImportBusinessService.generateSmartMappings(
        excelData,
        importState.mappings
      );
    },
    onSuccess: (mappings) => {
      updateImportState({ mappings });
    },
  });

  // Validate data mutation
  const validateDataMutation = useMutation({
    mutationFn: async () => {
      if (!importState.excelData) {
        throw new Error('No data to validate');
      }
      return CustomerImportBusinessService.validateMappingsAndData(
        importState.excelData,
        importState.mappings,
        importState.validationRules
      );
    },
    onSuccess: (validationResult) => {
      updateImportState({
        step: 'preview',
        validationResult,
      });
    },
  });

  // Start import mutation
  const startImportMutation = useMutation({
    mutationFn: async () => {
      if (!importState.excelData) {
        throw new Error('No data to import');
      }
      return CustomerImportBusinessService.startImportProcess(
        importState.excelData,
        importState.mappings,
        importState.batchConfig
      );
    },
    onSuccess: (job) => {
      updateImportState({
        step: 'importing',
        currentJob: job,
      });
      // Invalidate jobs query to refresh list
      queryClient.invalidateQueries({ queryKey: CUSTOMER_IMPORT_QUERY_KEYS.JOBS() });
    },
  });

  // Helper functions
  const uploadFile = useCallback((file: File, hasHeader: boolean = true) => {
    uploadFileMutation.mutate({ file, hasHeader });
  }, [uploadFileMutation]);

  const generateSmartMappings = useCallback(() => {
    if (importState.excelData) {
      generateMappingsMutation.mutate(importState.excelData);
    }
  }, [generateMappingsMutation, importState.excelData]);

  const updateMapping = useCallback((index: number, mapping: Partial<ColumnMapping>) => {
    const newMappings = [...importState.mappings];
    newMappings[index] = { ...newMappings[index], ...mapping };
    updateImportState({ mappings: newMappings });
  }, [importState.mappings, updateImportState]);

  const validateData = useCallback(() => {
    validateDataMutation.mutate();
  }, [validateDataMutation]);

  const startImport = useCallback(() => {
    startImportMutation.mutate();
  }, [startImportMutation]);

  const goToStep = useCallback((step: ImportState['step']) => {
    updateImportState({ step });
  }, [updateImportState]);

  const nextStep = useCallback(() => {
    const steps: ImportState['step'][] = ['upload', 'mapping', 'preview', 'importing', 'complete'];
    const currentIndex = steps.indexOf(importState.step);
    if (currentIndex < steps.length - 1) {
      updateImportState({ step: steps[currentIndex + 1] });
    }
  }, [importState.step, updateImportState]);

  const previousStep = useCallback(() => {
    const steps: ImportState['step'][] = ['upload', 'mapping', 'preview', 'importing', 'complete'];
    const currentIndex = steps.indexOf(importState.step);
    if (currentIndex > 0) {
      updateImportState({ step: steps[currentIndex - 1] });
    }
  }, [importState.step, updateImportState]);

  const setSelectedSource = useCallback((sourceType: ImportSourceType) => {
    const config = CustomerImportBusinessService.getImportSourceConfig(sourceType);
    updateImportState({ 
      selectedSource: {
        type: sourceType,
        name: sourceType,
        icon: 'file',
        description: '',
        ...config,
      }
    });
  }, [updateImportState]);

  const applyTemplate = useCallback((template: MappingTemplate) => {
    if (importState.excelData) {
      const mappings = CustomerImportBusinessService.applyTemplate(
        template,
        importState.excelData.headers
      );
      updateImportState({
        selectedTemplate: template,
        mappings,
      });
    }
  }, [importState.excelData, updateImportState]);

  return {
    // State
    importState,
    
    // Actions
    uploadFile,
    generateSmartMappings,
    updateMapping,
    validateData,
    startImport,
    resetImportState,
    updateImportState,
    
    // Navigation
    goToStep,
    nextStep,
    previousStep,
    
    // Template & Source
    setSelectedSource,
    applyTemplate,
    
    // Mutation states
    isUploading: uploadFileMutation.isPending,
    isGeneratingMappings: generateMappingsMutation.isPending,
    isValidating: validateDataMutation.isPending,
    isStartingImport: startImportMutation.isPending,
    
    // Errors
    uploadError: uploadFileMutation.error,
    mappingError: generateMappingsMutation.error,
    validationError: validateDataMutation.error,
    importError: startImportMutation.error,
  };
};

export default useCustomerImport;
