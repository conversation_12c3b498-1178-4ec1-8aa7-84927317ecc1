import {
  format,
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  addDays,
  addMonths,
  subMonths,
  addYears,
  subYears,
  isSameDay,
  isSameMonth,
  isToday,
  isAfter,
  isBefore,
  isWithinInterval,
  parse,
  getYear,
  getMonth,
  setMonth,
  setYear,
} from 'date-fns';
import { vi, enUS } from 'date-fns/locale';

// Re-export functions from date-fns that are used in other files
export { getYear, getMonth, setMonth, setYear, addDays };

/**
 * Kiểm tra và xác thực giá trị ngày tháng
 * @param date Giá trị ngày cần kiểm tra
 * @returns Đối tượng Date hợp lệ hoặc null nếu không hợp lệ
 */
export const validateDate = (date: unknown): Date | null => {
  if (!date) return null;

  try {
    // Xử lý các kiểu dữ liệu khác nhau
    let parsedDate: Date;

    if (date instanceof Date) {
      parsedDate = date;
    } else if (typeof date === 'string' || typeof date === 'number') {
      parsedDate = new Date(date);
    } else {
      console.warn('Unsupported date type:', typeof date);
      return null;
    }

    if (isNaN(parsedDate.getTime())) {
      console.warn('Invalid date value:', date);
      return null;
    }
    return parsedDate;
  } catch (error) {
    console.warn('Error parsing date:', error);
    return null;
  }
};

/**
 * Định dạng ngày theo format
 * @param date Ngày cần định dạng
 * @param formatStr Format string
 * @param locale Locale
 * @returns Chuỗi ngày đã định dạng
 */
export const formatDate = (
  date: Date | null | undefined,
  formatStr: string = 'dd/MM/yyyy',
  locale: string = 'vi'
): string => {
  if (!date) return '';
  try {
    return format(date, formatStr, { locale: locale === 'vi' ? vi : enUS });
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Parse chuỗi ngày thành đối tượng Date
 * @param dateStr Chuỗi ngày
 * @param formatStr Format string
 * @param locale Locale
 * @returns Đối tượng Date hoặc null nếu không parse được
 */
export const parseDate = (
  dateStr: string,
  formatStr: string = 'dd/MM/yyyy',
  locale: string = 'vi'
): Date | null => {
  if (!dateStr) return null;
  try {
    return parse(dateStr, formatStr, new Date(), { locale: locale === 'vi' ? vi : enUS });
  } catch (error) {
    console.error('Error parsing date:', error);
    return null;
  }
};

/**
 * Lấy mảng các ngày trong tháng để hiển thị trên calendar
 * @param month Tháng hiển thị
 * @param firstDayOfWeek Ngày đầu tiên của tuần (0: Chủ nhật, 1: Thứ 2, ...)
 * @returns Mảng các ngày
 */
export const getCalendarDays = (
  month: Date,
  firstDayOfWeek: 0 | 1 | 2 | 3 | 4 | 5 | 6 = 1
): Date[] => {
  const start = startOfWeek(startOfMonth(month), { weekStartsOn: firstDayOfWeek });
  const end = endOfWeek(endOfMonth(month), { weekStartsOn: firstDayOfWeek });

  const days: Date[] = [];
  let day = start;

  while (day <= end) {
    days.push(day);
    day = addDays(day, 1);
  }

  return days;
};

/**
 * Kiểm tra ngày có bị disabled không
 * @param date Ngày cần kiểm tra
 * @param disabledDates Các ngày bị disabled
 * @param minDate Ngày tối thiểu
 * @param maxDate Ngày tối đa
 * @returns true nếu ngày bị disabled
 */
export const isDateDisabled = (
  date: Date,
  disabledDates?: Date[] | ((date: Date) => boolean),
  minDate?: Date,
  maxDate?: Date
): boolean => {
  // Kiểm tra minDate và maxDate
  if (minDate && isBefore(date, minDate)) return true;
  if (maxDate && isAfter(date, maxDate)) return true;

  // Kiểm tra disabledDates
  if (disabledDates) {
    if (typeof disabledDates === 'function') {
      return disabledDates(date);
    } else if (Array.isArray(disabledDates)) {
      return disabledDates.some(disabledDate => isSameDay(date, disabledDate));
    }
  }

  return false;
};

/**
 * Kiểm tra ngày có nằm trong range không
 * @param date Ngày cần kiểm tra
 * @param startDate Ngày bắt đầu
 * @param endDate Ngày kết thúc
 * @returns true nếu ngày nằm trong range
 */
export const isDateInRange = (
  date: Date,
  startDate: Date | null | undefined,
  endDate: Date | null | undefined
): boolean => {
  if (!startDate || !endDate) return false;
  return isWithinInterval(date, { start: startDate, end: endDate });
};

/**
 * Lấy tên các ngày trong tuần
 * @param locale Locale
 * @param firstDayOfWeek Ngày đầu tiên của tuần (0: Chủ nhật, 1: Thứ 2, ...)
 * @param format Format (short, long)
 * @returns Mảng tên các ngày trong tuần
 */
export const getWeekDayNames = (
  locale: string = 'vi',
  firstDayOfWeek: 0 | 1 | 2 | 3 | 4 | 5 | 6 = 1,
  format: 'short' | 'long' = 'short'
): string[] => {
  const today = new Date();
  const weekStart = startOfWeek(today, { weekStartsOn: firstDayOfWeek });

  return Array.from({ length: 7 }).map((_, i) => {
    const day = addDays(weekStart, i);
    return format === 'short'
      ? formatDate(day, 'EEEEEE', locale) // 2 chữ cái đầu
      : formatDate(day, 'EEEE', locale); // Tên đầy đủ
  });
};

/**
 * Lấy tên các tháng trong năm
 * @param locale Locale
 * @param format Format (short, long)
 * @returns Mảng tên các tháng trong năm
 */
export const getMonthNames = (
  locale: string = 'vi',
  format: 'short' | 'long' = 'long'
): string[] => {
  const today = new Date();
  const year = getYear(today);

  return Array.from({ length: 12 }).map((_, i) => {
    const month = new Date(year, i, 1);
    return format === 'short'
      ? formatDate(month, 'MMM', locale) // Tên viết tắt
      : formatDate(month, 'MMMM', locale); // Tên đầy đủ
  });
};

/**
 * Tăng tháng
 * @param date Ngày hiện tại
 * @param amount Số tháng tăng
 * @returns Ngày mới
 */
export const incrementMonth = (date: Date, amount: number = 1): Date => {
  return addMonths(date, amount);
};

/**
 * Giảm tháng
 * @param date Ngày hiện tại
 * @param amount Số tháng giảm
 * @returns Ngày mới
 */
export const decrementMonth = (date: Date, amount: number = 1): Date => {
  return subMonths(date, amount);
};

/**
 * Tăng năm
 * @param date Ngày hiện tại
 * @param amount Số năm tăng
 * @returns Ngày mới
 */
export const incrementYear = (date: Date, amount: number = 1): Date => {
  return addYears(date, amount);
};

/**
 * Giảm năm
 * @param date Ngày hiện tại
 * @param amount Số năm giảm
 * @returns Ngày mới
 */
export const decrementYear = (date: Date, amount: number = 1): Date => {
  return subYears(date, amount);
};

/**
 * Thay đổi tháng
 * @param date Ngày hiện tại
 * @param newMonth Tháng mới (0-11)
 * @returns Ngày mới
 */
export const changeMonth = (date: Date, newMonth: number): Date => {
  return setMonth(date, newMonth);
};

/**
 * Thay đổi năm
 * @param date Ngày hiện tại
 * @param newYear Năm mới
 * @returns Ngày mới
 */
export const changeYear = (date: Date, newYear: number): Date => {
  return setYear(date, newYear);
};

/**
 * Lấy số tuần trong tháng
 * @param month Tháng hiển thị
 * @param firstDayOfWeek Ngày đầu tiên của tuần (0: Chủ nhật, 1: Thứ 2, ...)
 * @returns Số tuần
 */
export const getWeeksInMonth = (
  month: Date,
  firstDayOfWeek: 0 | 1 | 2 | 3 | 4 | 5 | 6 = 1
): number => {
  const days = getCalendarDays(month, firstDayOfWeek);
  return Math.ceil(days.length / 7);
};

/**
 * Lấy số ngày giữa hai ngày
 * @param startDate Ngày bắt đầu
 * @param endDate Ngày kết thúc
 * @returns Số ngày
 */
export const getDaysBetween = (startDate: Date, endDate: Date): number => {
  const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 để tính cả ngày đầu và cuối
};

/**
 * Kiểm tra hai ngày có cùng ngày không
 * @param date1 Ngày thứ nhất
 * @param date2 Ngày thứ hai
 * @returns true nếu hai ngày cùng ngày
 */
export const isSameDayFn = (
  date1: Date | null | undefined,
  date2: Date | null | undefined
): boolean => {
  if (!date1 || !date2) return false;
  return isSameDay(date1, date2);
};

/**
 * Kiểm tra hai ngày có cùng tháng không
 * @param date1 Ngày thứ nhất
 * @param date2 Ngày thứ hai
 * @returns true nếu hai ngày cùng tháng
 */
export const isSameMonthFn = (
  date1: Date | null | undefined,
  date2: Date | null | undefined
): boolean => {
  if (!date1 || !date2) return false;
  return isSameMonth(date1, date2);
};

/**
 * Kiểm tra ngày có phải là ngày hôm nay không
 * @param date Ngày cần kiểm tra
 * @returns true nếu là ngày hôm nay
 */
export const isTodayFn = (date: Date): boolean => {
  return isToday(date);
};

/**
 * Chuyển đổi Date thành string theo local timezone (không bị lệch múi giờ)
 * @param date Ngày cần chuyển đổi
 * @returns Chuỗi ngày theo format YYYY-MM-DD (local timezone)
 */
export const formatDateToLocalString = (date: Date): string => {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return '';
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

/**
 * Parse chuỗi ngày từ local string (YYYY-MM-DD) thành Date object
 * @param dateStr Chuỗi ngày theo format YYYY-MM-DD
 * @returns Date object hoặc null nếu không hợp lệ
 */
export const parseDateFromLocalString = (dateStr: string): Date | null => {
  if (!dateStr || typeof dateStr !== 'string') {
    return null;
  }

  // Kiểm tra format YYYY-MM-DD
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(dateStr)) {
    return null;
  }

  const [year, month, day] = dateStr.split('-').map(Number);

  // Kiểm tra tính hợp lệ của ngày tháng
  if (year < 1900 || year > 2100 || month < 1 || month > 12 || day < 1 || day > 31) {
    return null;
  }

  // Tạo Date object với local timezone
  const date = new Date(year, month - 1, day);

  // Kiểm tra ngày có hợp lệ không (ví dụ: 31/02 sẽ không hợp lệ)
  if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
    return null;
  }

  return date;
};
