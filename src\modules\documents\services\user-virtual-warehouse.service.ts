
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';
import { apiClient } from '@/shared/api/axios';

// Types
export interface VirtualWarehouseResponseDto {
  warehouseId: number;
  name: string;
  description: string | null;
  type: 'VIRTUAL';
  associatedSystem: string | null;
  purpose: string | null;
  customFields?: WarehouseCustomFieldResponseDto[];
}

export interface WarehouseCustomFieldResponseDto {
  warehouseId: number;
  fieldId: number;
  value: { value: unknown };
}

export interface QueryVirtualWarehouseDto extends QueryDto {
  userId?: number;
  type?: 'VIRTUAL';
  sortBy?: 'warehouseId' | 'name' | 'type';
}

export interface CreateVirtualWarehouseDto {
  userId?: number;
  associatedSystem?: string;
  purpose?: string;
}

export interface UpdateVirtualWarehouseDto {
  associatedSystem?: string;
  purpose?: string;
}

/**
 * Service xử lý API cho kho ảo của người dùng
 */
export class UserVirtualWarehouseService {
  private static readonly BASE_URL = '/user/virtual-warehouses';

  /**
   * Lấy danh sách kho ảo với phân trang và lọc
   */
  static async getVirtualWarehouses(
    params: QueryVirtualWarehouseDto
  ): Promise<PaginatedResult<VirtualWarehouseResponseDto>> {
    const response = await apiClient.get<PaginatedResult<VirtualWarehouseResponseDto>>(
      this.BASE_URL,
      { params }
    );
    return response.result;
  }

  /**
   * Lấy thông tin kho ảo theo ID
   */
  static async getVirtualWarehouseById(warehouseId: number): Promise<VirtualWarehouseResponseDto> {
    const response = await apiClient.get<VirtualWarehouseResponseDto>(
      `${this.BASE_URL}/${warehouseId}`
    );
    return response.result;
  }

  /**
   * Tạo mới kho ảo
   */
  static async createVirtualWarehouse(
    warehouseId: number,
    data: CreateVirtualWarehouseDto
  ): Promise<VirtualWarehouseResponseDto> {
    const response = await apiClient.post<VirtualWarehouseResponseDto>(
      `${this.BASE_URL}/${warehouseId}`,
      data
    );
    return response.result;
  }

  /**
   * Cập nhật kho ảo
   */
  static async updateVirtualWarehouse(
    warehouseId: number,
    data: UpdateVirtualWarehouseDto
  ): Promise<VirtualWarehouseResponseDto> {
    const response = await apiClient.put<VirtualWarehouseResponseDto>(
      `${this.BASE_URL}/${warehouseId}`,
      data
    );
    return response.result;
  }

  /**
   * Xóa kho ảo
   */
  static async deleteVirtualWarehouse(warehouseId: number): Promise<void> {
    await apiClient.delete(`${this.BASE_URL}/${warehouseId}`);
  }
}
