import { Injectable, Logger } from '@nestjs/common';
import { RAGService } from './rag.service';
import { ToolRegistryService } from './tool-registry.service';
import { EnhancedOpenAiService } from './enhanced-openai.service';
import { BusinessToolsProvider } from '../tools/business-tools';
// import { PromptManagerService } from './prompt-manager.service';
import { ChatConversation } from '../entities/chat-conversation.entity';
import { ChatMessage } from '../entities/chat-message.entity';
import { ToolContext, ToolExecutionResult } from '../interfaces/tool.interface';

/**
 * Interface cho AI Response
 */
interface AIResponse {
  text: string;
  intent: string;
  confidence: number;
  context: any;
  entities: any[];
  quickReplies?: Array<{ title: string; payload: string }>;
  requiresHumanHandoff?: boolean;
}

/**
 * Interface cho conversation context
 */
interface ConversationContext {
  conversationId: number;
  userId: string;
  userName: string;
  messageHistory: ChatMessage[];
  currentIntent?: string;
  sessionData?: any;
}

/**
 * Service điều phối AI processing cho chat
 */
@Injectable()
export class AIOrchestatorService {
  private readonly logger = new Logger(AIOrchestatorService.name);

  constructor(
    private readonly ragService: RAGService,
    private readonly toolRegistry: ToolRegistryService,
    private readonly enhancedOpenAi: EnhancedOpenAiService,
    private readonly businessTools: BusinessToolsProvider,
    // private readonly promptManager: PromptManagerService,
  ) {
    this.initializeTools();
  }

  /**
   * Khởi tạo và đăng ký tools
   */
  private initializeTools(): void {
    try {
      // Đăng ký business tools
      const businessTools = this.businessTools.getAllBusinessTools();
      for (const tool of businessTools) {
        this.toolRegistry.registerTool(tool);
      }

      this.logger.log(`Registered ${businessTools.length} business tools`);
    } catch (error) {
      this.logger.error(`Failed to initialize tools: ${error.message}`);
    }
  }

  /**
   * Xử lý tin nhắn với AI
   */
  async processMessage(
    conversation: ChatConversation,
    message: ChatMessage,
    tenantId: number,
  ): Promise<AIResponse> {
    try {
      // 1. Xây dựng context cuộc hội thoại
      const context = await this.buildConversationContext(
        conversation,
        message,
      );

      // 2. Phân tích intent và entities
      const analysis = await this.analyzeMessage(
        message.content || '',
        context,
      );

      // 3. Xử lý theo intent
      const response = await this.processIntent(
        analysis.intent,
        message.content || '',
        context,
        tenantId,
      );

      // 4. Kiểm tra có cần chuyển cho human không
      const requiresHandoff = await this.shouldHandoffToHuman(
        response,
        context,
      );

      return {
        ...response,
        requiresHumanHandoff: requiresHandoff,
      };
    } catch (error) {
      this.logger.error(`AI orchestration error: ${error.message}`);
      return this.getFallbackResponse();
    }
  }

  /**
   * Xử lý tin nhắn với AI sử dụng Tool Calling
   * Method chính để xử lý tin nhắn với khả năng gọi tools động
   */
  async processMessageWithTools(
    conversation: ChatConversation,
    message: ChatMessage,
    tenantId: number,
  ): Promise<AIResponse> {
    try {
      // Bước 1: Xây dựng context cuộc hội thoại (lịch sử, user info, etc.)
      const context = await this.buildConversationContext(
        conversation,
        message,
      );

      // Bước 2: Lấy danh sách tools có sẵn từ registry
      const availableTools = this.toolRegistry.getAvailableTools();

      // Bước 3: Phân tích có cần function calling không bằng AI
      const analysisResult = await this.enhancedOpenAi.analyzeForFunctionCalling(
        message.content || '',
        availableTools
      );

      // Nếu không cần function calling hoặc confidence thấp
      if (!analysisResult.needsFunctionCall || analysisResult.confidence < 0.6) {
        // Fallback: Xử lý bằng RAG thông thường
        return this.processWithRAG(message.content || '', context, tenantId);
      }

      // 4. Xử lý với function calling
      const toolContext: ToolContext = {
        tenantId,
        userId: parseInt(conversation.facebookUserId),
        conversationId: conversation.id,
        userContext: context,
        timestamp: Date.now(),
      };

      const systemPrompt = this.enhancedOpenAi.generateFunctionCallingSystemPrompt(availableTools);

      const functionResult = await this.enhancedOpenAi.processWithFunctions(
        message.content || '',
        availableTools,
        systemPrompt,
        'gpt-4'
      );

      // 5. Thực thi các function calls
      const executedResults: ToolExecutionResult[] = [];
      for (const functionCall of functionResult.functionCalls) {
        try {
          const result = await this.toolRegistry.executeTool(
            functionCall.name,
            functionCall.parameters,
            toolContext
          );
          executedResults.push(result);
          functionCall.result = result.data;
        } catch (error) {
          this.logger.error(`Tool execution failed: ${functionCall.name} - ${error.message}`);
          executedResults.push({
            success: false,
            error: error.message,
            toolName: functionCall.name,
            parameters: functionCall.parameters,
            startTime: Date.now(),
            endTime: Date.now(),
            executionTime: 0,
            context: toolContext,
          });
        }
      }

      // 6. Tạo final response với kết quả tools
      let finalMessage = functionResult.finalMessage;

      // Nếu có tool results, tạo response tổng hợp
      if (executedResults.length > 0) {
        finalMessage = await this.generateResponseWithToolResults(
          message.content || '',
          executedResults,
          context
        );
      }

      // 7. Kiểm tra có cần chuyển cho human không
      const requiresHandoff = await this.shouldHandoffToHuman(
        { text: finalMessage, confidence: 0.9 } as AIResponse,
        context,
      );

      return {
        text: finalMessage,
        intent: 'function_call_processed',
        confidence: 0.9,
        context: {
          toolsUsed: executedResults.map(r => r.toolName),
          executionResults: executedResults,
        },
        entities: [],
        requiresHumanHandoff: requiresHandoff,
      };

    } catch (error) {
      this.logger.error(`AI processing with tools error: ${error.message}`);
      return this.getFallbackResponse();
    }
  }

  /**
   * Xử lý với RAG thông thường (fallback)
   */
  private async processWithRAG(
    content: string,
    context: ConversationContext,
    tenantId: number,
  ): Promise<AIResponse> {
    const ragResponse = await this.ragService.processQuery(
      content,
      tenantId,
      context,
    );

    return {
      text: ragResponse.answer,
      intent: 'general_question',
      confidence: ragResponse.confidence,
      context: ragResponse.context,
      entities: [],
    };
  }

  /**
   * Tạo response tổng hợp từ tool results
   */
  private async generateResponseWithToolResults(
    originalQuery: string,
    toolResults: ToolExecutionResult[],
    context: ConversationContext,
  ): Promise<string> {
    try {
      const successfulResults = toolResults.filter(r => r.success);
      const failedResults = toolResults.filter(r => !r.success);

      let responsePrompt = `Dựa trên câu hỏi: "${originalQuery}"\n\n`;

      if (successfulResults.length > 0) {
        responsePrompt += 'Kết quả từ các tools:\n';
        successfulResults.forEach((result, index) => {
          responsePrompt += `${index + 1}. Tool "${result.toolName}":\n`;
          responsePrompt += `   Kết quả: ${JSON.stringify(result.data)}\n`;
          if (result.message) {
            responsePrompt += `   Thông báo: ${result.message}\n`;
          }
        });
      }

      if (failedResults.length > 0) {
        responsePrompt += '\nCác tools gặp lỗi:\n';
        failedResults.forEach((result, index) => {
          responsePrompt += `${index + 1}. Tool "${result.toolName}": ${result.error}\n`;
        });
      }

      responsePrompt += '\nHãy tạo câu trả lời tự nhiên, hữu ích bằng tiếng Việt dựa trên kết quả trên.';

      const response = await this.ragService.processQuery(
        responsePrompt,
        context.conversationId, // Use conversationId as tenantId placeholder
        context,
      );

      return response.answer;
    } catch (error) {
      this.logger.error(`Error generating response with tool results: ${error.message}`);
      return 'Tôi đã thực hiện một số tác vụ nhưng gặp khó khăn trong việc tổng hợp kết quả. Vui lòng thử lại.';
    }
  }

  /**
   * Xây dựng context cuộc hội thoại
   */
  private async buildConversationContext(
    conversation: ChatConversation,
    currentMessage: ChatMessage,
  ): Promise<ConversationContext> {
    // Lấy lịch sử tin nhắn gần đây (10 tin nhắn cuối)
    const messageHistory = await this.getRecentMessages(conversation.id, 10);

    return {
      conversationId: conversation.id,
      userId: conversation.facebookUserId,
      userName: conversation.userName || 'Người dùng',
      messageHistory,
      currentIntent: this.extractCurrentIntent(messageHistory),
      sessionData: conversation.metadata?.sessionData || {},
    };
  }

  /**
   * Phân tích tin nhắn để xác định intent
   */
  private async analyzeMessage(
    content: string,
    context: ConversationContext,
  ): Promise<{ intent: string; entities: any[]; confidence: number }> {
    // TODO: Implement PromptManagerService
    const analysisPrompt = `Phân tích intent của tin nhắn: "${content}"`;

    try {
      const result = await this.ragService.processQuery(
        analysisPrompt,
        1, // tenantId sẽ được truyền từ context thực tế
      );

      // Parse kết quả phân tích
      return this.parseIntentAnalysis(result.answer);
    } catch (error) {
      this.logger.error(`Intent analysis error: ${error.message}`);
      return {
        intent: 'general_question',
        entities: [],
        confidence: 0.5,
      };
    }
  }

  /**
   * Xử lý theo intent cụ thể
   */
  private async processIntent(
    intent: string,
    content: string,
    context: ConversationContext,
    tenantId: number,
  ): Promise<AIResponse> {
    switch (intent) {
      case 'greeting':
        return this.handleGreeting(context);

      case 'get_todo_statistics':
        return this.handleTodoStatistics(content, context, tenantId);

      case 'get_employee_info':
        return this.handleEmployeeInfo(content, context, tenantId);

      case 'get_late_employees':
        return this.handleLateEmployees(content, context, tenantId);

      case 'get_overdue_tasks':
        return this.handleOverdueTasks(content, context, tenantId);

      case 'general_question':
        return this.handleGeneralQuestion(content, context, tenantId);

      default:
        return this.handleUnknownIntent(content, context);
    }
  }

  /**
   * Xử lý lời chào
   */
  private async handleGreeting(
    context: ConversationContext,
  ): Promise<AIResponse> {
    const greetingText = `Xin chào ${context.userName}! 👋\n\nTôi là trợ lý AI của hệ thống ERP. Tôi có thể giúp bạn:`;

    const quickReplies = [
      { title: '📊 Thống kê công việc', payload: 'get_todo_stats' },
      { title: '👥 Thông tin nhân viên', payload: 'get_employee_info' },
      { title: '⏰ Nhân viên đi muộn', payload: 'get_late_employees' },
      { title: '🚨 Công việc chậm deadline', payload: 'get_overdue_tasks' },
    ];

    return {
      text: greetingText,
      intent: 'greeting',
      confidence: 1.0,
      context: {},
      entities: [],
      quickReplies,
    };
  }

  /**
   * Xử lý thống kê công việc
   */
  private async handleTodoStatistics(
    content: string,
    context: ConversationContext,
    tenantId: number,
  ): Promise<AIResponse> {
    const ragResponse = await this.ragService.processQuery(
      `Thống kê công việc: ${content}`,
      tenantId,
      context,
    );

    return {
      text: ragResponse.answer,
      intent: 'get_todo_statistics',
      confidence: ragResponse.confidence,
      context: ragResponse.context,
      entities: [],
    };
  }

  /**
   * Xử lý thông tin nhân viên
   */
  private async handleEmployeeInfo(
    content: string,
    context: ConversationContext,
    tenantId: number,
  ): Promise<AIResponse> {
    const ragResponse = await this.ragService.processQuery(
      `Thông tin nhân viên: ${content}`,
      tenantId,
      context,
    );

    return {
      text: ragResponse.answer,
      intent: 'get_employee_info',
      confidence: ragResponse.confidence,
      context: ragResponse.context,
      entities: [],
    };
  }

  /**
   * Xử lý nhân viên đi muộn
   */
  private async handleLateEmployees(
    content: string,
    context: ConversationContext,
    tenantId: number,
  ): Promise<AIResponse> {
    const ragResponse = await this.ragService.processQuery(
      `Nhân viên đi muộn trong tháng: ${content}`,
      tenantId,
      context,
    );

    return {
      text: ragResponse.answer,
      intent: 'get_late_employees',
      confidence: ragResponse.confidence,
      context: ragResponse.context,
      entities: [],
    };
  }

  /**
   * Xử lý công việc chậm deadline
   */
  private async handleOverdueTasks(
    content: string,
    context: ConversationContext,
    tenantId: number,
  ): Promise<AIResponse> {
    const ragResponse = await this.ragService.processQuery(
      `Công việc chậm deadline: ${content}`,
      tenantId,
      context,
    );

    return {
      text: ragResponse.answer,
      intent: 'get_overdue_tasks',
      confidence: ragResponse.confidence,
      context: ragResponse.context,
      entities: [],
    };
  }

  /**
   * Xử lý câu hỏi chung
   */
  private async handleGeneralQuestion(
    content: string,
    context: ConversationContext,
    tenantId: number,
  ): Promise<AIResponse> {
    const ragResponse = await this.ragService.processQuery(
      content,
      tenantId,
      context,
    );

    return {
      text: ragResponse.answer,
      intent: 'general_question',
      confidence: ragResponse.confidence,
      context: ragResponse.context,
      entities: [],
    };
  }

  /**
   * Xử lý intent không xác định
   */
  private async handleUnknownIntent(
    content: string,
    context: ConversationContext,
  ): Promise<AIResponse> {
    return {
      text: 'Xin lỗi, tôi chưa hiểu câu hỏi của bạn. Bạn có thể hỏi về thống kê công việc, thông tin nhân viên, hoặc các vấn đề khác trong hệ thống ERP.',
      intent: 'unknown',
      confidence: 0.3,
      context: {},
      entities: [],
      quickReplies: [
        { title: '📊 Thống kê công việc', payload: 'get_todo_stats' },
        { title: '👥 Thông tin nhân viên', payload: 'get_employee_info' },
        { title: '💬 Nói chuyện với nhân viên', payload: 'handoff_human' },
      ],
    };
  }

  /**
   * Kiểm tra có cần chuyển cho human không
   */
  private async shouldHandoffToHuman(
    response: AIResponse,
    context: ConversationContext,
  ): Promise<boolean> {
    // Chuyển cho human nếu confidence thấp
    if (response.confidence < 0.6) return true;

    // Chuyển cho human nếu user yêu cầu
    if (context.sessionData?.requestHuman) return true;

    // Chuyển cho human nếu là vấn đề phức tạp
    if (response.intent === 'complex_issue') return true;

    return false;
  }

  /**
   * Lấy tin nhắn gần đây
   */
  private async getRecentMessages(
    conversationId: number,
    limit: number,
  ): Promise<ChatMessage[]> {
    // Placeholder - cần implement với ChatService
    return [];
  }

  /**
   * Trích xuất intent hiện tại từ lịch sử
   */
  private extractCurrentIntent(
    messageHistory: ChatMessage[],
  ): string | undefined {
    const lastAIMessage = messageHistory.filter((m) => m.isAiGenerated).pop();

    return lastAIMessage?.detectedIntent || undefined;
  }

  /**
   * Parse kết quả phân tích intent
   */
  private parseIntentAnalysis(analysisResult: string): {
    intent: string;
    entities: any[];
    confidence: number;
  } {
    try {
      return JSON.parse(analysisResult);
    } catch {
      return {
        intent: 'general_question',
        entities: [],
        confidence: 0.5,
      };
    }
  }

  /**
   * Phản hồi fallback khi có lỗi
   */
  private getFallbackResponse(): AIResponse {
    return {
      text: 'Xin lỗi, tôi đang gặp sự cố kỹ thuật. Vui lòng thử lại sau hoặc liên hệ với nhân viên hỗ trợ.',
      intent: 'error',
      confidence: 0.0,
      context: {},
      entities: [],
      requiresHumanHandoff: true,
    };
  }
}
