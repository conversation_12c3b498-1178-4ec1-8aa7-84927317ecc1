import React from 'react';
import CustomerDetailView from './CustomerDetailView';
import { CustomerDetailData } from './sections/types';

/**
 * Demo component để test CustomerDetailView với dữ liệu mẫu
 */
const CustomerDetailViewDemo: React.FC = () => {
  // Dữ liệu mẫu cho customer
  const mockCustomer: CustomerDetailData = {
    id: 'CUST001',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+84 901 234 567',
    address: '123 Đường Lê Lợi, Quận 1, TP.HCM',
    avatar: 'https://i.pravatar.cc/150?img=1',
    tags: ['VIP', '<PERSON>h<PERSON>ch hàng thân thiết', '<PERSON><PERSON> nhiều'],
    status: 'active',
    totalOrders: 15,
    totalSpent: 25000000,
    averageOrderValue: 1666667,
    lastOrderDate: '2024-01-15T10:30:00Z',
    customerSince: '2023-06-15T08:00:00Z',
    // Overview data
    flowCount: 8,
    campaignCount: 12,
    sequenceCount: 5,
    topChannels: [
      { id: '1', name: 'Email Marketing', count: 45, percentage: 35, icon: 'mail' },
      { id: '2', name: 'Facebook Messenger', count: 38, percentage: 30, icon: 'facebook' },
      { id: '3', name: 'Zalo', count: 25, percentage: 20, icon: 'message-circle' },
      { id: '4', name: 'SMS', count: 19, percentage: 15, icon: 'message-square' },
    ],
    topDevices: [
      { id: '1', name: 'Mobile Phone', count: 85, percentage: 68, icon: 'smartphone' },
      { id: '2', name: 'Desktop', count: 25, percentage: 20, icon: 'monitor' },
      { id: '3', name: 'Tablet', count: 15, percentage: 12, icon: 'tablet' },
    ],
    socialProfiles: {
      facebook: 'https://facebook.com/nguyenvanan',
      twitter: 'https://twitter.com/nguyenvanan',
      linkedin: 'https://linkedin.com/in/nguyenvanan',
      zalo: '0901234567',
      website: 'https://nguyenvanan.dev',
    },
    customFields: {
      'Nghề nghiệp': 'Kỹ sư phần mềm',
      'Tuổi': 28,
      'Sở thích': ['Công nghệ', 'Du lịch', 'Đọc sách'],
      'Ngày sinh': '1995-03-15',
      'Website': 'https://nguyenvanan.dev',
      'Đã kết hôn': true,
      'Ghi chú': 'Khách hàng rất quan tâm đến sản phẩm công nghệ mới',
    },
    interactions: [
      {
        id: 'INT001',
        type: 'email',
        channel: 'Email Marketing',
        title: 'Gửi email khuyến mãi tháng 1',
        description: 'Email giới thiệu sản phẩm mới và ưu đãi đặc biệt',
        date: '2024-01-10T14:30:00Z',
        status: 'completed',
      },
      {
        id: 'INT002',
        type: 'phone',
        channel: 'Hotline',
        title: 'Tư vấn sản phẩm',
        description: 'Khách hàng gọi để tư vấn về sản phẩm laptop gaming',
        date: '2024-01-08T09:15:00Z',
        status: 'completed',
      },
      {
        id: 'INT003',
        type: 'chat',
        channel: 'Website Chat',
        title: 'Hỗ trợ kỹ thuật',
        description: 'Hỗ trợ cài đặt phần mềm sau khi mua',
        date: '2024-01-05T16:45:00Z',
        status: 'completed',
      },
      {
        id: 'INT004',
        type: 'social',
        channel: 'Facebook',
        title: 'Phản hồi bình luận',
        description: 'Khách hàng bình luận về sản phẩm trên Facebook',
        date: '2024-01-03T11:20:00Z',
        status: 'completed',
      },
    ],
    orders: [
      {
        id: 'ORD001',
        orderCode: 'DH240115001',
        date: '2024-01-15T10:30:00Z',
        status: 'delivered',
        paymentMethod: 'Chuyển khoản',
        paymentStatus: 'paid',
        totalAmount: 15000000,
        items: 2,
        shippingAddress: '123 Đường Lê Lợi, Quận 1, TP.HCM',
      },
      {
        id: 'ORD002',
        orderCode: 'DH240110002',
        date: '2024-01-10T14:20:00Z',
        status: 'shipped',
        paymentMethod: 'COD',
        paymentStatus: 'pending',
        totalAmount: 8500000,
        items: 1,
        shippingAddress: '123 Đường Lê Lợi, Quận 1, TP.HCM',
      },
      {
        id: 'ORD003',
        orderCode: 'DH240105003',
        date: '2024-01-05T09:15:00Z',
        status: 'processing',
        paymentMethod: 'Thẻ tín dụng',
        paymentStatus: 'paid',
        totalAmount: 1500000,
        items: 3,
        shippingAddress: '123 Đường Lê Lợi, Quận 1, TP.HCM',
      },
    ],
    activities: [
      {
        id: 'ACT001',
        type: 'order',
        title: 'Đặt hàng thành công',
        description: 'Đặt hàng laptop gaming MSI với giá trị 15.000.000đ',
        date: '2024-01-15T10:30:00Z',
        metadata: {
          'Mã đơn hàng': 'DH240115001',
          'Giá trị': '15.000.000đ',
          'Sản phẩm': 'Laptop MSI Gaming',
        },
      },
      {
        id: 'ACT002',
        type: 'login',
        title: 'Đăng nhập vào hệ thống',
        description: 'Đăng nhập từ thiết bị di động',
        date: '2024-01-14T08:45:00Z',
        metadata: {
          'Thiết bị': 'Mobile',
          'IP': '*************',
          'Trình duyệt': 'Chrome Mobile',
        },
      },
      {
        id: 'ACT003',
        type: 'profile_update',
        title: 'Cập nhật thông tin cá nhân',
        description: 'Thay đổi số điện thoại liên hệ',
        date: '2024-01-12T15:20:00Z',
        metadata: {
          'Trường thay đổi': 'Số điện thoại',
          'Giá trị cũ': '+84 901 234 566',
          'Giá trị mới': '+84 901 234 567',
        },
      },
      {
        id: 'ACT004',
        type: 'support',
        title: 'Yêu cầu hỗ trợ',
        description: 'Gửi yêu cầu hỗ trợ cài đặt phần mềm',
        date: '2024-01-10T11:30:00Z',
        metadata: {
          'Loại hỗ trợ': 'Kỹ thuật',
          'Mức độ': 'Trung bình',
          'Trạng thái': 'Đã giải quyết',
        },
      },
      {
        id: 'ACT005',
        type: 'review',
        title: 'Đánh giá sản phẩm',
        description: 'Đánh giá 5 sao cho laptop đã mua',
        date: '2024-01-08T16:45:00Z',
        metadata: {
          'Sản phẩm': 'Laptop MSI Gaming',
          'Điểm đánh giá': '5/5',
          'Bình luận': 'Sản phẩm rất tốt, giao hàng nhanh',
        },
      },
      {
        id: 'ACT006',
        type: 'payment',
        title: 'Thanh toán thành công',
        description: 'Thanh toán đơn hàng DH240105003',
        date: '2024-01-05T09:20:00Z',
        metadata: {
          'Mã đơn hàng': 'DH240105003',
          'Số tiền': '1.500.000đ',
          'Phương thức': 'Thẻ tín dụng',
        },
      },
    ],
  };

  const handleClose = () => {
    console.log('Close customer detail view');
  };

  const handleEdit = () => {
    console.log('Edit customer');
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <CustomerDetailView
        customer={mockCustomer}
        onClose={handleClose}
        onEdit={handleEdit}
      />
    </div>
  );
};

export default CustomerDetailViewDemo;
