import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CustomFieldService } from '../services/custom-field.service';
import { CustomGroupFormService } from '../services/custom-group-form.service';
import { CustomFieldSearchItem } from './useCustomFieldSearch';

/**
 * Interface cho filter options
 */
export interface FilterOption {
  id: string;
  label: string;
  value: string | number;
}

/**
 * Interface cho filter state
 */
export interface FilterState {
  search: string;
  groupId: string | null;
  sortBy: string;
  sortDirection: 'ASC' | 'DESC';
}

/**
 * Interface cho hook parameters
 */
export interface UseCustomFieldWithFilterParams {
  pageSize?: number;
  initialFilters?: Partial<FilterState>;
}

/**
 * Hook quản lý custom fields với filter nâng cao
 */
export const useCustomFieldWithFilter = (params: UseCustomFieldWithFilterParams = {}) => {
  const { pageSize = 20, initialFilters = {} } = params;
  const { t } = useTranslation(['business', 'common']);

  // State management
  const [items, setItems] = useState<CustomFieldSearchItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [groupOptions, setGroupOptions] = useState<FilterOption[]>([]);
  const [groupsLoading, setGroupsLoading] = useState(false);

  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    groupId: null,
    sortBy: 'label',
    sortDirection: 'ASC',
    ...initialFilters,
  });

  // Load custom field groups for filter
  const loadGroupOptions = useCallback(async () => {
    try {
      setGroupsLoading(true);
      const response = await CustomGroupFormService.getCustomGroupForms({
        page: 1,
        limit: 100, // Load all groups for filter
      });

      const options: FilterOption[] = [
        { id: 'all', label: t('common:all'), value: '' },
        ...response.result.items.map(group => ({
          id: group.id.toString(),
          label: group.label,
          value: group.id,
        })),
      ];

      setGroupOptions(options);
    } catch (error) {
      console.error('Error loading group options:', error);
      setGroupOptions([{ id: 'all', label: t('common:all'), value: '' }]);
    } finally {
      setGroupsLoading(false);
    }
  }, [t]);

  // Load custom fields with filters
  const loadCustomFields = useCallback(async (page: number = 1, reset: boolean = false) => {
    try {
      setLoading(true);

      const params: Record<string, unknown> = {
        page,
        limit: pageSize,
        sortBy: filters.sortBy,
        sortDirection: filters.sortDirection,
      };

      // Add search filter
      if (filters.search.trim()) {
        params.search = filters.search.trim();
      }

      // Add group filter
      if (filters.groupId && filters.groupId !== 'all') {
        params.groupId = filters.groupId;
      }

      const response = await CustomFieldService.getCustomFields(params);
      const newItems = response.result.items.map(item => ({
        id: item.id,
        label: item.label,
        component: item.component,
        configId: item.configId,
        type: item.type,
        required: item.required,
      }));

      if (reset || page === 1) {
        setItems(newItems);
        setCurrentPage(1);
      } else {
        setItems(prev => [...prev, ...newItems]);
      }

      setCurrentPage(page);
      setHasMore(newItems.length === pageSize);
    } catch (error) {
      console.error('Error loading custom fields:', error);
      if (reset || page === 1) {
        setItems([]);
      }
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [filters, pageSize]);

  // Search function
  const search = useCallback((searchTerm: string) => {
    setFilters(prev => ({ ...prev, search: searchTerm }));
  }, []);

  // Filter by group
  const filterByGroup = useCallback((groupId: string) => {
    setFilters(prev => ({ ...prev, groupId: groupId === 'all' ? null : groupId }));
  }, []);

  // Sort function
  const sort = useCallback((sortBy: string, sortDirection: 'ASC' | 'DESC' = 'ASC') => {
    setFilters(prev => ({ ...prev, sortBy, sortDirection }));
  }, []);

  // Load more function
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      loadCustomFields(currentPage + 1, false);
    }
  }, [loading, hasMore, currentPage, loadCustomFields]);

  // Initial load
  const initialLoad = useCallback(() => {
    if (items.length === 0 && !loading) {
      loadCustomFields(1, true);
    }
  }, [items.length, loading, loadCustomFields]);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setFilters({
      search: '',
      groupId: null,
      sortBy: 'label',
      sortDirection: 'ASC',
    });
  }, []);

  // Effect to reload data when filters change
  useEffect(() => {
    loadCustomFields(1, true);
  }, [filters.search, filters.groupId, filters.sortBy, filters.sortDirection, loadCustomFields]);

  // Load group options on mount
  useEffect(() => {
    loadGroupOptions();
  }, [loadGroupOptions]);

  return {
    // Data
    items,
    loading,
    hasMore,
    groupOptions,
    groupsLoading,

    // Filter state
    filters,

    // Actions
    search,
    filterByGroup,
    sort,
    loadMore,
    initialLoad,
    clearFilters,

    // Helper functions
    getActiveFiltersCount: () => {
      let count = 0;
      if (filters.search.trim()) count++;
      if (filters.groupId && filters.groupId !== 'all') count++;
      if (filters.sortBy !== 'label' || filters.sortDirection !== 'ASC') count++;
      return count;
    },

    getFilterLabel: (filterId: string) => {
      const group = groupOptions.find(g => g.id === filterId);
      return group?.label || '';
    },
  };
};
