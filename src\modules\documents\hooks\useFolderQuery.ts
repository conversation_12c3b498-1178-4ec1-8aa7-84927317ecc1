import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import {
  UserFolderService,
  QueryFolderDto,
  CreateFolderDto,
  UpdateFolderDto
} from '../services/user-folder.service';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Query keys cho folder
 */
export const FOLDER_QUERY_KEYS = {
  ALL: ['folders'] as const,
  LIST: (params: QueryFolderDto) => [...FOLDER_QUERY_KEYS.ALL, 'list', params] as const,
  DETAIL: (id: number) => [...FOLDER_QUERY_KEYS.ALL, 'detail', id] as const,
  ROOT: () => [...FOLDER_QUERY_KEYS.ALL, 'root'] as const,
  CHILDREN: (parentId: number) => [...FOLDER_QUERY_KEYS.ALL, 'children', parentId] as const,
};

/**
 * Hook lấy danh sách folder với phân trang
 */
export const useFolders = (params: QueryFolderDto) => {
  return useQuery({
    queryKey: FOLDER_QUERY_KEYS.LIST(params),
    queryFn: () => UserFolderService.getFolders(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook lấy danh sách folder gốc
 */
export const useRootFolders = () => {
  return useQuery({
    queryKey: FOLDER_QUERY_KEYS.ROOT(),
    queryFn: () => UserFolderService.getRootFolders(),
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook lấy danh sách folder con
 */
export const useChildFolders = (parentId: number) => {
  return useQuery({
    queryKey: FOLDER_QUERY_KEYS.CHILDREN(parentId),
    queryFn: () => UserFolderService.getChildFolders(parentId),
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook lấy chi tiết folder
 */
export const useFolderDetail = (id: number) => {
  return useQuery({
    queryKey: FOLDER_QUERY_KEYS.DETAIL(id),
    queryFn: () => UserFolderService.getFolderById(id),
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook tạo folder mới
 */
export const useCreateFolder = () => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateFolderDto) => UserFolderService.createFolder(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: FOLDER_QUERY_KEYS.ALL });
      NotificationUtil.success({
        message: t('business:folder.messages.createSuccess', 'Tạo thư mục thành công')
      });
    },
    onError: () => {
      NotificationUtil.error({
        message: t('business:folder.messages.createError', 'Có lỗi xảy ra khi tạo thư mục')
      });
    },
  });
};

/**
 * Hook cập nhật folder
 */
export const useUpdateFolder = () => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateFolderDto }) =>
      UserFolderService.updateFolder(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: FOLDER_QUERY_KEYS.ALL });
      queryClient.invalidateQueries({ queryKey: FOLDER_QUERY_KEYS.DETAIL(id) });
      NotificationUtil.success({
        message: t('business:folder.messages.updateSuccess', 'Cập nhật thư mục thành công')
      });
    },
    onError: () => {
      NotificationUtil.error({
        message: t('business:folder.messages.updateError', 'Có lỗi xảy ra khi cập nhật thư mục')
      });
    },
  });
};

/**
 * Hook xóa folder
 */
export const useDeleteFolder = () => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => UserFolderService.deleteFolder(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: FOLDER_QUERY_KEYS.ALL });
      NotificationUtil.success({
        message: t('business:folder.messages.deleteSuccess', 'Xóa thư mục thành công')
      });
    },
    onError: () => {
      NotificationUtil.error({
        message: t('business:folder.messages.deleteError', 'Có lỗi xảy ra khi xóa thư mục')
      });
    },
  });
};
