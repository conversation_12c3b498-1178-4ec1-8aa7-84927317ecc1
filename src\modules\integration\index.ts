// Export components
export { default as LinkedAccountsCard } from './components/LinkedAccountsCard';
export { SocialIcon, SocialNetworkIcon } from './components/Social';

// Export pages
export * from './pages';

// Export routes
export { default as integrationRoutes } from './routes/integrationRoutes';

// Export hooks
export {
  useLinkedAccounts,
  useAddLinkedAccount,
  useRemoveLinkedAccount,
} from './hooks/useAccountQuery';

// Export types
export type { BankAccount, BankAccountsResponse, BankAccountsParams } from './types/account';
export type { SocialNetwork, SocialNetworksResponse, SocialNetworksParams } from './types/social';

// Export API functions
export {
  fetchSocialNetworks,
  fetchSocialNetworkById,
  updateSocialNetworkUrl,
  updateSocialNetworkStatus,
} from './api/socialMockData';
