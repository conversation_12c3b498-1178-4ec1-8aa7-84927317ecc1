/**
 * Types for social network integration
 */

/**
 * Social network information
 */
export interface SocialNetwork {
  /**
   * Unique identifier for the social network
   */
  id: string;

  /**
   * Display name of the social network
   */
  name: string;

  /**
   * Code used for icon identification
   */
  code: string;

  /**
   * URL of the social network profile
   */
  url: string;

  /**
   * Brand color of the social network
   */
  color: string;

  /**
   * Display order in the list
   */
  order?: number;

  /**
   * Whether the social network is active
   */
  active: boolean;
}

/**
 * Parameters for fetching social networks
 */
export interface SocialNetworksParams {
  /**
   * Whether to return only active social networks
   */
  activeOnly?: boolean;

  /**
   * Whether to sort by order
   */
  sortByOrder?: boolean;
}

/**
 * Response for social networks list
 */
export interface SocialNetworksResponse {
  /**
   * List of social networks
   */
  items: SocialNetwork[];

  /**
   * Total count of social networks
   */
  total: number;
}
