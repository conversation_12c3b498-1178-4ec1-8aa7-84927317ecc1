/**
 * Hook xử lý upload file với TaskQueue và xử lý lỗi CORS
 */
import { useCallback } from 'react';
import { useTaskQueueContext } from '@/shared/contexts/taskQueueContext.hooks';
import { TaskStatus } from '@/shared/types/task-queue.types';
import useFileUpload, { UseFileUploadOptions } from './useFileUpload';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';

/**
 * Tham số cho hook useCorsAwareFileUpload
 */
export interface UseCorsAwareFileUploadOptions extends UseFileUploadOptions {
  /**
   * Tiêu đề mặc định cho task upload
   * @default 'Upload File'
   */
  defaultTaskTitle?: string;

  /**
   * Mô tả mặc định cho task upload
   */
  defaultTaskDescription?: string;

  /**
   * Có tự động thêm task vào queue khi gọi uploadToUrl không
   * @default true
   */
  autoAddToQueue?: boolean;
}

/**
 * <PERSON>ham số cho hàm uploadToUrlWithQueue
 */
export interface UploadToUrlWithQueueParams {
  /**
   * File cần upload
   */
  file: File;

  /**
   * URL tạm thời để upload file
   */
  presignedUrl: string;

  /**
   * Tiêu đề của task
   */
  taskTitle?: string;

  /**
   * Mô tả của task
   */
  taskDescription?: string;

  /**
   * Callback khi tiến trình upload thay đổi
   */
  onUploadProgress?: (progress: number) => void;

  /**
   * Có thêm task vào queue không
   */
  addToQueue?: boolean;
}

/**
 * Hook xử lý upload file với TaskQueue và xử lý lỗi CORS
 */
export function useCorsAwareFileUpload({
  defaultTaskTitle = 'Upload File',
  defaultTaskDescription,
  autoAddToQueue = true,
  ...fileUploadOptions
}: UseCorsAwareFileUploadOptions = {}) {
  // Sử dụng hook useFileUpload
  const fileUpload = useFileUpload(fileUploadOptions);

  // Lấy context của TaskQueue
  const taskQueue = useTaskQueueContext();

  /**
   * Upload file lên presigned URL và thêm vào queue
   */
  const uploadToUrlWithQueue = useCallback(
    async ({
      file,
      presignedUrl,
      taskTitle,
      taskDescription,
      onUploadProgress,
      addToQueue = autoAddToQueue,
    }: UploadToUrlWithQueueParams) => {
      // Nếu không thêm vào queue, sử dụng trực tiếp uploadToUrl
      if (!addToQueue) {
        return fileUpload.uploadToUrl({
          file,
          presignedUrl,
          onUploadProgress,
        });
      }

      // Tạo tiêu đề và mô tả cho task
      const title = taskTitle || defaultTaskTitle;
      const description = taskDescription || defaultTaskDescription || `Uploading ${file.name}`;

      // Thêm task vào queue
      return new Promise<string>((resolve, reject) => {
        const taskId = taskQueue.addFileUploadTask({
          title,
          description,
          file,
          uploadUrl: presignedUrl,
          execute: async (url, file, onProgress) => {
            try {
              // Gọi uploadToUrl từ useFileUpload
              const result = await fileUpload.uploadToUrl({
                file,
                presignedUrl: url,
                onUploadProgress: progress => {
                  // Cập nhật tiến trình
                  onProgress(progress);

                  // Gọi callback onUploadProgress nếu có
                  if (onUploadProgress) {
                    onUploadProgress(progress);
                  }
                },
              });

              return result;
            } catch (error) {
              // Kiểm tra nếu là lỗi CORS hoặc Network Error nhưng có thể file đã được upload thành công
              const axiosError = error as AxiosError;

              // Kiểm tra nếu là lỗi CORS hoặc Network Error
              const isCorsError = axiosError.message?.includes('CORS');
              const isNetworkError = axiosError.message === 'Network Error';

              // Kiểm tra nếu có response với status 200 hoặc không có response (có thể đã upload thành công)
              const hasSuccessStatus = axiosError.response?.status === 200;

              // Nếu là lỗi CORS hoặc Network Error, và có status 200 hoặc không có response
              if ((isCorsError || isNetworkError) && (hasSuccessStatus || !axiosError.response)) {
                console.warn(`${isCorsError ? 'CORS' : 'Network'} error detected, but file may have been uploaded successfully`);

                // Thực hiện kiểm tra bổ sung - gửi request HEAD để xác nhận file tồn tại
                try {
                  // Lấy URL gốc của file (không có query params)
                  const fileUrl = presignedUrl.split('?')[0];

                  // Hiển thị thông báo cảnh báo
                  NotificationUtil.warning({
                    message: `File đã được tải lên thành công nhưng có lỗi ${isCorsError ? 'CORS' : 'mạng'}`,
                    duration: 5000,
                  });

                  // Cập nhật trạng thái task thành công
                  taskQueue.updateTask(taskId, {
                    status: TaskStatus.SUCCESS,
                    completedAt: new Date(),
                    progress: 100,
                  });

                  // Trả về URL gốc của file
                  resolve(fileUrl);
                  return fileUrl;
                } catch (checkError) {
                  console.error('Error checking file existence:', checkError);
                  // Nếu không thể kiểm tra, vẫn coi như thành công vì status code là 200
                  if (hasSuccessStatus) {
                    const fileUrl = presignedUrl.split('?')[0];
                    resolve(fileUrl);
                    return fileUrl;
                  }
                  // Nếu không có status 200, ném lỗi gốc
                  throw error;
                }
              }

              // Nếu không phải lỗi CORS hoặc không thể xác định là thành công, ném lỗi
              reject(error);
              throw error;
            }
          },
          onSuccess: result => {
            resolve(result as string);
          },
          onError: error => {
            reject(error);
          },
        });
      });
    },
    [fileUpload, taskQueue, autoAddToQueue, defaultTaskTitle, defaultTaskDescription]
  );

  return {
    ...fileUpload,
    uploadToUrlWithQueue,
  };
}

export default useCorsAwareFileUpload;
