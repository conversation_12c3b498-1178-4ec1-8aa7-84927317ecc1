/**
 * OAuth2 Service for Email Providers
 */

import { apiClient } from '@/shared/api/axios';
import { OAuthTokens } from '../types/providers';

export interface OAuth2Config {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scope: string[];
}

export interface OAuth2AuthorizationResponse {
  authorizationUrl: string;
  state: string;
}

export interface OAuth2TokenResponse {
  accessToken: string;
  refreshToken: string;
  expiresAt: string;
  tokenType: string;
  scope?: string;
}

/**
 * Gmail OAuth2 Service
 */
export class GmailOAuth2Service {
  private static readonly GMAIL_SCOPES = [
    'https://www.googleapis.com/auth/gmail.send',
    'https://www.googleapis.com/auth/gmail.readonly'
  ];

  /**
   * Start Gmail OAuth2 authorization flow
   */
  static async startAuthorization(redirectUri: string): Promise<OAuth2AuthorizationResponse> {
    const response = await apiClient.post<OAuth2AuthorizationResponse>('/integration/email/oauth/gmail/authorize', {
      redirectUri,
      scope: this.GMAIL_SCOPES
    });
    return response.result;
  }

  /**
   * Exchange authorization code for tokens
   */
  static async exchangeCodeForTokens(
    code: string,
    state: string,
    redirectUri: string
  ): Promise<OAuth2TokenResponse> {
    const response = await apiClient.post<OAuth2TokenResponse>('/integration/email/oauth/gmail/token', {
      code,
      state,
      redirectUri
    });
    return response.result;
  }

  /**
   * Refresh access token
   */
  static async refreshToken(refreshToken: string): Promise<OAuth2TokenResponse> {
    const response = await apiClient.post<OAuth2TokenResponse>('/integration/email/oauth/gmail/refresh', {
      refreshToken
    });
    return response.result;
  }

  /**
   * Validate Gmail OAuth2 tokens
   */
  static async validateTokens(tokens: OAuthTokens): Promise<boolean> {
    try {
      const response = await apiClient.post<{ valid: boolean }>('/integration/email/oauth/gmail/validate', {
        accessToken: tokens.accessToken
      });
      return response.result.valid;
    } catch {
      return false;
    }
  }
}

/**
 * Outlook OAuth2 Service
 */
export class OutlookOAuth2Service {
  private static readonly OUTLOOK_SCOPES = [
    'https://graph.microsoft.com/Mail.Send',
    'https://graph.microsoft.com/Mail.Read'
  ];

  /**
   * Start Outlook OAuth2 authorization flow
   */
  static async startAuthorization(redirectUri: string): Promise<OAuth2AuthorizationResponse> {
    const response = await apiClient.post<OAuth2AuthorizationResponse>('/integration/email/oauth/outlook/authorize', {
      redirectUri,
      scope: this.OUTLOOK_SCOPES
    });
    return response.result;
  }

  /**
   * Exchange authorization code for tokens
   */
  static async exchangeCodeForTokens(
    code: string,
    state: string,
    redirectUri: string
  ): Promise<OAuth2TokenResponse> {
    const response = await apiClient.post<OAuth2TokenResponse>('/integration/email/oauth/outlook/token', {
      code,
      state,
      redirectUri
    });
    return response.result;
  }

  /**
   * Refresh access token
   */
  static async refreshToken(refreshToken: string): Promise<OAuth2TokenResponse> {
    const response = await apiClient.post<OAuth2TokenResponse>('/integration/email/oauth/outlook/refresh', {
      refreshToken
    });
    return response.result;
  }

  /**
   * Validate Outlook OAuth2 tokens
   */
  static async validateTokens(tokens: OAuthTokens): Promise<boolean> {
    try {
      const response = await apiClient.post<{ valid: boolean }>('/integration/email/oauth/outlook/validate', {
        accessToken: tokens.accessToken
      });
      return response.result.valid;
    } catch {
      return false;
    }
  }
}

/**
 * Generic OAuth2 Service Factory
 */
export class OAuth2ServiceFactory {
  static getService(providerId: string) {
    switch (providerId) {
      case 'gmail':
        return GmailOAuth2Service;
      case 'outlook':
        return OutlookOAuth2Service;
      default:
        throw new Error(`OAuth2 not supported for provider: ${providerId}`);
    }
  }

  static isOAuth2Supported(providerId: string): boolean {
    return ['gmail', 'outlook'].includes(providerId);
  }
}

/**
 * OAuth2 Token Manager
 */
export class OAuth2TokenManager {
  private static readonly TOKEN_STORAGE_KEY = 'email_oauth_tokens';

  /**
   * Store OAuth2 tokens securely
   */
  static storeTokens(providerId: string, tokens: OAuthTokens): void {
    const allTokens = this.getAllTokens();
    allTokens[providerId] = {
      ...tokens,
      storedAt: new Date().toISOString()
    };
    
    // Store in secure storage (localStorage for now, should be encrypted in production)
    localStorage.setItem(this.TOKEN_STORAGE_KEY, JSON.stringify(allTokens));
  }

  /**
   * Get stored OAuth2 tokens
   */
  static getTokens(providerId: string): OAuthTokens | null {
    const allTokens = this.getAllTokens();
    return allTokens[providerId] || null;
  }

  /**
   * Remove stored tokens
   */
  static removeTokens(providerId: string): void {
    const allTokens = this.getAllTokens();
    delete allTokens[providerId];
    localStorage.setItem(this.TOKEN_STORAGE_KEY, JSON.stringify(allTokens));
  }

  /**
   * Check if tokens are expired
   */
  static isTokenExpired(tokens: OAuthTokens): boolean {
    if (!tokens.expiresAt) return false;
    return new Date(tokens.expiresAt) <= new Date();
  }

  /**
   * Auto-refresh tokens if needed
   */
  static async ensureValidTokens(providerId: string): Promise<OAuthTokens | null> {
    const tokens = this.getTokens(providerId);
    if (!tokens) return null;

    if (this.isTokenExpired(tokens) && tokens.refreshToken) {
      try {
        const service = OAuth2ServiceFactory.getService(providerId);
        const newTokens = await service.refreshToken(tokens.refreshToken);
        
        const updatedTokens: OAuthTokens = {
          accessToken: newTokens.accessToken,
          refreshToken: newTokens.refreshToken || tokens.refreshToken,
          expiresAt: newTokens.expiresAt,
          tokenType: newTokens.tokenType,
          scope: newTokens.scope
        };

        this.storeTokens(providerId, updatedTokens);
        return updatedTokens;
      } catch (error) {
        console.error('Failed to refresh tokens:', error);
        this.removeTokens(providerId);
        return null;
      }
    }

    return tokens;
  }

  private static getAllTokens(): Record<string, OAuthTokens & { storedAt?: string }> {
    try {
      const stored = localStorage.getItem(this.TOKEN_STORAGE_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  }
}
