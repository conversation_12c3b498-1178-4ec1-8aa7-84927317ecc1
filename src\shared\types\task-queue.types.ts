/**
 * C<PERSON><PERSON> kiểu dữ liệu cho hệ thống Task Queue
 */

/**
 * Trạng thái của task
 */
export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  SUCCESS = 'success',
  ERROR = 'error',
  CANCELLED = 'cancelled',
}

/**
 * Loại task
 */
export enum TaskType {
  API_CALL = 'api_call',
  FILE_UPLOAD = 'file_upload',
  CUSTOM = 'custom',
}

/**
 * Thông tin cơ bản của task
 */
export interface BaseTask {
  /**
   * ID của task
   */
  id: string;

  /**
   * Tiêu đề của task
   */
  title: string;

  /**
   * Mô tả của task
   */
  description?: string;

  /**
   * Loại task
   */
  type: TaskType;

  /**
   * Trạng thái hiện tại của task
   */
  status: TaskStatus;

  /**
   * Tiến trình của task (0-100)
   */
  progress: number;

  /**
   * Thời gian tạo task
   */
  createdAt: Date;

  /**
   * Thời gian bắt đầu thực thi task
   */
  startedAt?: Date;

  /**
   * Thời gian hoàn thành task
   */
  completedAt?: Date;

  /**
   * Lỗi nếu task thất bại
   */
  error?: Error | string;

  /**
   * Kết quả của task
   */
  result?: unknown;

  /**
   * Có thể hủy task hay không
   */
  cancellable: boolean;

  /**
   * Có thể thử lại task hay không
   */
  retryable: boolean;

  /**
   * Số lần đã thử lại
   */
  retryCount: number;

  /**
   * Số lần thử lại tối đa
   */
  maxRetries: number;

  /**
   * Metadata bổ sung
   */
  metadata?: Record<string, unknown>;
}

/**
 * Task gọi API
 */
export interface ApiCallTask extends BaseTask {
  type: TaskType.API_CALL;

  /**
   * Hàm thực thi API call
   */
  execute: () => Promise<unknown>;

  /**
   * Callback khi task thành công
   */
  onSuccess?: (result: unknown) => void;

  /**
   * Callback khi task thất bại
   */
  onError?: (error: Error) => void;
}

/**
 * Task upload file
 */
export interface FileUploadTask extends BaseTask {
  type: TaskType.FILE_UPLOAD;

  /**
   * File cần upload
   */
  file: File;

  /**
   * URL để upload file
   */
  uploadUrl?: string;

  /**
   * Hàm để lấy URL upload
   */
  getUploadUrl?: () => Promise<string>;

  /**
   * Hàm thực thi upload
   */
  execute: (url: string, file: File, onProgress: (progress: number) => void) => Promise<unknown>;

  /**
   * Callback khi task thành công
   */
  onSuccess?: (result: unknown) => void;

  /**
   * Callback khi task thất bại
   */
  onError?: (error: Error) => void;

  /**
   * Callback khi tiến trình thay đổi
   */
  onProgress?: (progress: number) => void;

  /**
   * Thumbnail của file (nếu là ảnh)
   */
  thumbnail?: string;
}

/**
 * Task tùy chỉnh
 */
export interface CustomTask extends BaseTask {
  type: TaskType.CUSTOM;

  /**
   * Hàm thực thi task
   */
  execute: (onProgress: (progress: number) => void) => Promise<unknown>;

  /**
   * Callback khi task thành công
   */
  onSuccess?: (result: unknown) => void;

  /**
   * Callback khi task thất bại
   */
  onError?: (error: Error) => void;

  /**
   * Callback khi tiến trình thay đổi
   */
  onProgress?: (progress: number) => void;
}

/**
 * Union type cho tất cả các loại task
 */
export type Task = ApiCallTask | FileUploadTask | CustomTask;

/**
 * Tham số để tạo task API call
 */
export interface CreateApiCallTaskParams {
  title: string;
  description?: string;
  execute: () => Promise<unknown>;
  onSuccess?: (result: unknown) => void;
  onError?: (error: Error) => void;
  cancellable?: boolean;
  retryable?: boolean;
  maxRetries?: number;
  metadata?: Record<string, unknown>;
}

/**
 * Tham số để tạo task upload file
 */
export interface CreateFileUploadTaskParams {
  title: string;
  description?: string;
  file: File;
  uploadUrl?: string;
  getUploadUrl?: () => Promise<string>;
  execute: (url: string, file: File, onProgress: (progress: number) => void) => Promise<unknown>;
  onSuccess?: (result: unknown) => void;
  onError?: (error: Error) => void;
  onProgress?: (progress: number) => void;
  cancellable?: boolean;
  retryable?: boolean;
  maxRetries?: number;
  metadata?: Record<string, unknown>;
}

/**
 * Tham số để tạo task tùy chỉnh
 */
export interface CreateCustomTaskParams {
  title: string;
  description?: string;
  execute: (onProgress: (progress: number) => void) => Promise<unknown>;
  onSuccess?: (result: unknown) => void;
  onError?: (error: Error) => void;
  onProgress?: (progress: number) => void;
  cancellable?: boolean;
  retryable?: boolean;
  maxRetries?: number;
  metadata?: Record<string, unknown>;
}
