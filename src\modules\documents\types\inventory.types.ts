import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Interface cho thông tin sản phẩm trong kho
 */
export interface InventoryItemDto {
  /**
   * ID bản ghi tồn kho
   */
  id: number;

  /**
   * ID sản phẩm
   */
  productId: number;

  /**
   * ID kho chứa sản phẩm
   */
  warehouseId: number;

  /**
   * Số lượng hiện tại trong kho
   */
  currentQuantity: number;

  /**
   * Số lượng có sẵn
   */
  availableQuantity?: number;

  /**
   * Số lượng đã đặt trước
   */
  reservedQuantity?: number;

  /**
   * Số lượng hỏng
   */
  defectiveQuantity?: number;

  /**
   * Tổng số lượng
   */
  totalQuantity?: number;

  /**
   * Thông tin sản phẩm
   */
  product?: {
    id: number;
    name: string;
    code?: string;
    description?: string;
    images?: Array<{
      key: string;
      url: string;
      position: number;
    }>;
  };

  /**
   * Thông tin kho
   */
  warehouse?: {
    warehouseId: number;
    name: string;
    description?: string;
    type?: string;
  };

  /**
   * Thời gian tạo
   */
  createdAt?: string;

  /**
   * Thời gian cập nhật
   */
  updatedAt?: string;
}

/**
 * Interface cho tham số truy vấn inventory
 */
export interface InventoryQueryParams extends QueryDto {
  /**
   * ID sản phẩm để lọc
   */
  productId?: number;

  /**
   * ID kho để lọc
   */
  warehouseId?: number;

  /**
   * ID người dùng
   */
  userId?: number;
}

/**
 * Interface cho việc cập nhật số lượng sản phẩm trong kho
 */
export interface UpdateInventoryQuantityDto {
  /**
   * Số lượng có sẵn
   */
  availableQuantity?: number;

  /**
   * Số lượng đã đặt trước
   */
  reservedQuantity?: number;

  /**
   * Số lượng hỏng
   */
  defectiveQuantity?: number;

  /**
   * ID kho (nếu muốn chuyển kho)
   */
  warehouseId?: number;
}

/**
 * Interface cho việc tạo mới inventory
 */
export interface CreateInventoryDto {
  /**
   * ID sản phẩm
   */
  productId: number;

  /**
   * ID kho
   */
  warehouseId: number;

  /**
   * Số lượng có sẵn
   */
  availableQuantity?: number;

  /**
   * Số lượng đã đặt trước
   */
  reservedQuantity?: number;

  /**
   * Số lượng hỏng
   */
  defectiveQuantity?: number;

  /**
   * ID người dùng
   */
  userId?: number;
}

/**
 * Enum cho trạng thái inventory
 */
export enum InventoryStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}
