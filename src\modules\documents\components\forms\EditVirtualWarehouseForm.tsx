import React, { useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Card,
  Typography,
  Loading,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';

import {
  EditVirtualWarehouseFormValues,
} from '../../types/virtual-warehouse.types';
import {
  useVirtualWarehouse,
  useUpdateVirtualWarehouse,
} from '../../hooks/useVirtualWarehouseQuery';

interface EditVirtualWarehouseFormProps {
  warehouseId: number;
  onSubmit: (values: EditVirtualWarehouseFormValues) => void;
  onCancel: () => void;
}

/**
 * Form chỉnh sửa kho ảo
 */
const EditVirtualWarehouseForm: React.FC<EditVirtualWarehouseFormProps> = ({
  warehouseId,
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const formRef = useRef<FormRef<EditVirtualWarehouseFormValues>>(null);

  const { data: warehouse, isLoading } = useVirtualWarehouse(warehouseId);
  const { mutateAsync: updateVirtualWarehouse, isPending } = useUpdateVirtualWarehouse();

  // Set form values khi data được load
  useEffect(() => {
    if (warehouse && formRef.current && formRef.current.reset) {
      formRef.current.reset({
        associatedSystem: warehouse.associatedSystem || '',
        purpose: warehouse.purpose || '',
      });
    }
  }, [warehouse]);

  const handleSubmit = async (values: EditVirtualWarehouseFormValues) => {
    try {
      await updateVirtualWarehouse({
        warehouseId: warehouseId,
        data: {
          associatedSystem: values.associatedSystem,
          purpose: values.purpose,
        },
      });
      onSubmit(values);
    } catch (error) {
      console.error('Update virtual warehouse error:', error);
    }
  };

  if (isLoading) {
    return (
      <Card className="w-full">
        <div className="p-6 flex justify-center">
          <Loading />
        </div>
      </Card>
    );
  }



  return (
    <Card className="w-full">
      <div className="p-6">
        <Typography variant="h5" className="mb-6">
          {t('business:virtualWarehouse.form.editTitle')}
        </Typography>

        <Form
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          ref={formRef as any}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onSubmit={handleSubmit as any}
          className="space-y-6"
        >

          <FormItem
            name="associatedSystem"
            label={t('business:virtualWarehouse.associatedSystem')}
          >
            <Input
              placeholder={t('business:virtualWarehouse.form.associatedSystemPlaceholder')}
              disabled={isPending}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="purpose"
            label={t('business:virtualWarehouse.purpose')}
          >
            <Textarea
              placeholder={t('business:virtualWarehouse.form.purposePlaceholder')}
              rows={3}
              disabled={isPending}
            />
          </FormItem>



          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isPending}
            >
              {t('common:cancel')}
            </Button>
            <Button
              type="submit"
              variant="primary"
              isLoading={isPending}
            >
              {t('business:virtualWarehouse.form.update')}
            </Button>
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default EditVirtualWarehouseForm;
export type { EditVirtualWarehouseFormValues };
