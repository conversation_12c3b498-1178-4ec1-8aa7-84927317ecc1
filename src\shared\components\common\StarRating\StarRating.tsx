import React from 'react';
import { cn } from '@/shared/utils/cn';

interface StarRatingProps {
  /**
   * Current rating value (1-5)
   */
  value: number;

  /**
   * Maximum number of stars
   */
  maxStars?: number;

  /**
   * Whether the rating is interactive
   */
  interactive?: boolean;

  /**
   * Size of the stars
   */
  size?: 'sm' | 'md' | 'lg' | 'xl';

  /**
   * Callback when rating changes
   */
  onChange?: (rating: number) => void;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Star Rating Component
 */
const StarRating: React.FC<StarRatingProps> = ({
  value,
  maxStars = 5,
  interactive = false,
  size = 'md',
  onChange,
  className = '',
}) => {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-2xl',
    xl: 'text-3xl',
  };

  const handleStarClick = (rating: number) => {
    if (interactive && onChange) {
      onChange(rating);
    }
  };

  return (
    <div className={cn('flex items-center gap-1', className)}>
      {Array.from({ length: maxStars }).map((_, index) => {
        const starValue = index + 1;
        const isFilled = starValue <= value;

        return (
          <button
            key={index}
            type="button"
            className={cn(
              sizeClasses[size],
              'border-0 bg-transparent p-0 leading-none',
              isFilled ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600',
              interactive && 'cursor-pointer hover:text-yellow-300 transition-colors focus:outline-none focus:ring-2 focus:ring-primary/30 rounded',
              !interactive && 'cursor-default'
            )}
            onClick={() => handleStarClick(starValue)}
            disabled={!interactive}
            aria-label={`${starValue} star${starValue > 1 ? 's' : ''}`}
          >
            ★
          </button>
        );
      })}
    </div>
  );
};

export default StarRating;
