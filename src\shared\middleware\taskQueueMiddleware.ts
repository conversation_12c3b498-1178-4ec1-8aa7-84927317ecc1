/**
 * Middleware để tích hợp Task Queue với TanStack Query
 */
import { QueryClient } from '@tanstack/react-query';
import { TaskQueueContextValue } from '@/shared/contexts/taskQueueContext.types';
import { TaskStatus } from '@/shared/types/task-queue.types';
import { Mutation } from '@tanstack/react-query';

/**
 * Tham số cho hàm createTaskQueueMiddleware
 */
export interface CreateTaskQueueMiddlewareParams {
  /**
   * QueryClient của TanStack Query
   */
  queryClient: QueryClient;

  /**
   * Context của Task Queue
   */
  taskQueue: TaskQueueContextValue;

  /**
   * Có tự động thêm task cho tất cả các mutation không
   * @default false
   */
  autoAddAllMutations?: boolean;

  /**
   * Danh sách mutation key cần theo dõi (chỉ áp dụng khi autoAddAllMutations = false)
   */
  mutationKeys?: string[];

  /**
   * Hàm tùy chỉnh để lấy tiêu đề task từ mutation
   */
  getTaskTitle?: (mutationKey: readonly unknown[]) => string;

  /**
   * Hàm tùy chỉnh để lấy mô tả task từ mutation
   */
  getTaskDescription?: (mutationKey: readonly unknown[]) => string;

  /**
   * Hàm tùy chỉnh để quyết định có thêm task cho mutation không
   */
  shouldAddTask?: () => boolean;
}

/**
 * Interface mở rộng cho Mutation của TanStack Query để sử dụng trong middleware
 */
interface MutationWithId extends Mutation<unknown, unknown, unknown, unknown> {
  // Định nghĩa các thuộc tính mà chúng ta cần sử dụng
  mutationId: number;
}

// Định nghĩa lại kiểu trạng thái mutation của TanStack Query
type MutationStatus = 'idle' | 'loading' | 'success' | 'error';

/**
 * Tạo middleware để tích hợp Task Queue với TanStack Query
 */
export const createTaskQueueMiddleware = ({
  queryClient,
  taskQueue,
  autoAddAllMutations = false,
  mutationKeys = [],
  getTaskTitle = defaultGetTaskTitle,
  getTaskDescription = defaultGetTaskDescription,
  shouldAddTask = defaultShouldAddTask,
}: CreateTaskQueueMiddlewareParams) => {
  // Lưu trữ mapping giữa mutation ID và task ID
  const mutationToTaskMap = new Map<number, string>();

  // Đăng ký listener cho các sự kiện mutation
  const unsubscribe = queryClient.getMutationCache().subscribe(event => {
    if (!event.mutation) return;

    const mutation = event.mutation as MutationWithId;
    const mutationKey = mutation.options.mutationKey;

    // Nếu không có mutation key, bỏ qua
    if (!mutationKey) return;

    // Kiểm tra xem có nên thêm task cho mutation này không
    const shouldAdd =
      autoAddAllMutations ||
      (mutationKeys.length > 0 && isMatchingMutationKey(mutationKey, mutationKeys)) ||
      shouldAddTask();

    if (!shouldAdd) return;

    // Lấy ID của mutation
    const mutationId = mutation.mutationId;

    // Xử lý các sự kiện mutation
    switch (event.type) {
      case 'added': {
        // Tạo task mới khi mutation được thêm vào
        const taskId = taskQueue.addApiCallTask({
          title: getTaskTitle(mutationKey),
          description: getTaskDescription(mutationKey),
          execute: async () => {
            // Task này không thực sự thực thi API call, nó chỉ theo dõi mutation
            // API call được thực hiện bởi TanStack Query
            return new Promise((resolve, reject) => {
              // Thay vì sử dụng subscribe, chúng ta sẽ truy vấn trạng thái của mutation
              const checkMutationState = () => {
                const state = mutation.state;
                if (state.status === 'success') {
                  resolve(state.data);
                } else if (state.status === 'error') {
                  reject(state.error);
                } else {
                  // Nếu chưa hoàn thành, kiểm tra lại sau 100ms
                  setTimeout(checkMutationState, 100);
                }
              };

              checkMutationState();
            });
          },
          onSuccess: () => {
            // Không cần làm gì, mutation đã xử lý callback onSuccess
          },
          onError: () => {
            // Không cần làm gì, mutation đã xử lý callback onError
          },
        });

        // Lưu mapping giữa mutation ID và task ID
        mutationToTaskMap.set(mutationId, taskId);
        break;
      }

      case 'updated': {
        // Cập nhật trạng thái task dựa vào trạng thái hiện tại của mutation
        const taskId = mutationToTaskMap.get(mutationId);
        if (!taskId) return;

        const state = mutation.state;
        const status = state.status as MutationStatus;

        if (status === 'loading') {
          // Mutation đang chạy
          taskQueue.updateTask(taskId, {
            status: TaskStatus.RUNNING,
            startedAt: new Date(),
            progress: 10,
          });
        } else if (status === 'success') {
          // Mutation thành công
          taskQueue.updateTask(taskId, {
            status: TaskStatus.SUCCESS,
            completedAt: new Date(),
            progress: 100,
            result: state.data as string,
          });

          // Xóa mapping sau khi hoàn thành
          mutationToTaskMap.delete(mutationId);
        } else if (status === 'error') {
          // Mutation lỗi
          taskQueue.updateTask(taskId, {
            status: TaskStatus.ERROR,
            completedAt: new Date(),
            error: state.error as Error,
          });

          // Xóa mapping sau khi hoàn thành
          mutationToTaskMap.delete(mutationId);
        }
        break;
      }

      case 'removed': {
        // Xóa task khi mutation bị xóa
        const taskId = mutationToTaskMap.get(mutationId);
        if (taskId) {
          const task = taskQueue.tasks.find(
            (t: { id: string; status: TaskStatus }) => t.id === taskId
          );
          if (task && task.status === TaskStatus.PENDING) {
            taskQueue.cancelTask(taskId);
          }

          // Xóa mapping
          mutationToTaskMap.delete(mutationId);
        }
        break;
      }
    }
  });

  return {
    /**
     * Hủy đăng ký listener
     */
    unsubscribe,

    /**
     * Thêm task cho một mutation cụ thể
     */
    addTaskForMutation: (
      mutation: { mutationId: number; mutationKey: readonly unknown[] },
      options?: {
        title?: string;
        description?: string;
      }
    ) => {
      const { mutationId, mutationKey } = mutation;

      // Kiểm tra xem mutation đã có task chưa
      if (mutationToTaskMap.has(mutationId)) {
        return mutationToTaskMap.get(mutationId)!;
      }

      // Tạo task mới
      const taskId = taskQueue.addApiCallTask({
        title: options?.title || getTaskTitle(mutationKey),
        description: options?.description || getTaskDescription(mutationKey),
        execute: async () => {
          // Task này không thực sự thực thi API call, nó chỉ theo dõi mutation
          return Promise.resolve();
        },
      });

      // Lưu mapping
      mutationToTaskMap.set(mutationId, taskId);

      return taskId;
    },

    /**
     * Thêm task upload file
     */
    addFileUploadTask: (
      file: File,
      uploadFn: (file: File, onProgress: (progress: number) => void) => Promise<unknown>,
      options?: {
        title?: string;
        description?: string;
        onSuccess?: (result: unknown) => void;
        onError?: (error: Error) => void;
      }
    ) => {
      return taskQueue.addFileUploadTask({
        title: options?.title || `Upload ${file.name}`,
        description: options?.description,
        file,
        execute: async (_url: string, file: File, onProgress: (progress: number) => void) => {
          return uploadFn(file, onProgress);
        },
        onSuccess: options?.onSuccess,
        onError: options?.onError,
      });
    },
  };
};

/**
 * Hàm mặc định để lấy tiêu đề task từ mutation
 */
const defaultGetTaskTitle = (mutationKey: readonly unknown[]): string => {
  // Cố gắng lấy tên từ mutation key
  if (Array.isArray(mutationKey) && mutationKey.length > 0) {
    const lastKey = mutationKey[mutationKey.length - 1];

    if (typeof lastKey === 'string') {
      // Chuyển đổi camelCase hoặc snake_case thành Title Case
      return lastKey
        .replace(/([A-Z])/g, ' $1')
        .replace(/_/g, ' ')
        .replace(/^\w/, c => c.toUpperCase());
    }
  }

  return 'API Request';
};

/**
 * Hàm mặc định để lấy mô tả task từ mutation
 */
const defaultGetTaskDescription = (mutationKey: readonly unknown[]): string => {
  // Cố gắng tạo mô tả từ mutation key
  if (Array.isArray(mutationKey)) {
    return mutationKey.filter(k => typeof k === 'string').join(' / ');
  }

  return '';
};

/**
 * Hàm mặc định để quyết định có thêm task cho mutation không
 */
const defaultShouldAddTask = (): boolean => {
  return true;
};

/**
 * Kiểm tra xem mutation key có khớp với danh sách key cần theo dõi không
 */
const isMatchingMutationKey = (mutationKey: readonly unknown[], keys: string[]): boolean => {
  if (!Array.isArray(mutationKey)) return false;

  // Chuyển đổi mutation key thành string để so sánh
  const keyString = mutationKey.filter(k => typeof k === 'string').join('.');

  // Kiểm tra xem có key nào khớp không
  return keys.some(key => keyString.includes(key));
};

export default createTaskQueueMiddleware;
