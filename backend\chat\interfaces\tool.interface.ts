/**
 * Interface định nghĩa cấu trúc của một tool/function
 * Đ<PERSON><PERSON> là blueprint cho tất cả các tools trong hệ thống
 */
export interface ToolDefinition {
  /** Tên unique của tool - dùng để identify và gọi tool */
  name: string;

  /** <PERSON><PERSON> tả chức năng của tool - AI sẽ dùng để hiểu khi nào cần gọi tool này */
  description: string;

  /** Schema định nghĩa parameters theo JSON Schema format - validate input */
  parameters: {
    type: 'object';
    properties: Record<string, ToolParameter>; // Danh sách các tham số
    required: string[]; // Tham số bắt buộc
  };

  /** Function handler thực thi tool - logic chính của tool */
  handler: (params: any, context: ToolContext) => Promise<ToolResult>;

  /** Phân loại tool - để organize và filter tools */
  category: ToolCategory;

  /** Tool có cần tenant isolation không - b<PERSON><PERSON> mật multi-tenant */
  tenantIsolated: boolean;
  
  /** <PERSON>uyền cần thiết để sử dụng tool */
  requiredPermissions?: string[];
  
  /** Rate limit cho tool (calls per minute) */
  rateLimit?: number;
  
  /** Timeout cho tool execution (milliseconds) */
  timeout?: number;
  
  /** Tool có cache được kết quả không */
  cacheable?: boolean;
  
  /** Thời gian cache (seconds) */
  cacheTtl?: number;
}

/**
 * Định nghĩa parameter của tool
 */
export interface ToolParameter {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  enum?: any[];
  minimum?: number;
  maximum?: number;
  pattern?: string;
  items?: ToolParameter;
  properties?: Record<string, ToolParameter>;
  default?: any;
}

/**
 * Context được truyền vào tool khi thực thi
 * Chứa tất cả thông tin cần thiết để tool hoạt động đúng
 */
export interface ToolContext {
  /** ID tenant hiện tại - để đảm bảo tenant isolation */
  tenantId: number;

  /** ID người dùng hiện tại - để biết ai đang gọi tool */
  userId: number;

  /** ID cuộc hội thoại - để track conversation */
  conversationId: number;

  /** Thông tin chi tiết người dùng - nếu cần thêm data */
  user?: {
    id: number;
    username: string;
    email: string;
    roles: string[]; // Quyền của user
  };

  /** Context bổ sung từ cuộc hội thoại - lịch sử, preferences */
  userContext?: any;

  /** Metadata bổ sung - thông tin debug, tracking */
  metadata?: Record<string, any>;

  /** Thời gian thực thi - để tracking performance */
  timestamp: number;
}

/**
 * Kết quả trả về từ tool execution
 */
export interface ToolResult {
  /** Kết quả thành công hay không */
  success: boolean;
  
  /** Dữ liệu trả về */
  data?: any;
  
  /** Thông báo lỗi nếu có */
  error?: string;
  
  /** Thông báo cho người dùng */
  message?: string;
  
  /** Metadata bổ sung */
  metadata?: {
    executionTime?: number;
    fromCache?: boolean;
    rowCount?: number;
    [key: string]: any;
  };
  
  /** Gợi ý actions tiếp theo */
  suggestions?: Array<{
    title: string;
    action: string;
    parameters?: any;
  }>;
}

/**
 * Phân loại tool
 */
export enum ToolCategory {
  /** Tools liên quan đến database */
  DATABASE = 'database',
  
  /** Tools liên quan đến business logic */
  BUSINESS = 'business',
  
  /** Tools tiện ích */
  UTILITY = 'utility',
  
  /** Tools gọi external services */
  EXTERNAL = 'external',
  
  /** Tools liên quan đến file/document */
  FILE = 'file',
  
  /** Tools liên quan đến notification */
  NOTIFICATION = 'notification',
  
  /** Tools liên quan đến analytics */
  ANALYTICS = 'analytics'
}

/**
 * Kết quả thực thi tool với thông tin bổ sung
 */
export interface ToolExecutionResult extends ToolResult {
  /** Tên tool đã thực thi */
  toolName: string;
  
  /** Parameters đã sử dụng */
  parameters: any;
  
  /** Thời gian bắt đầu thực thi */
  startTime: number;
  
  /** Thời gian kết thúc thực thi */
  endTime: number;
  
  /** Thời gian thực thi (ms) */
  executionTime: number;
  
  /** Tool context */
  context: ToolContext;
}

/**
 * Thống kê performance của tool
 */
export interface ToolStats {
  /** Tên tool */
  toolName: string;
  
  /** Tổng số lần gọi */
  totalCalls: number;
  
  /** Số lần thành công */
  successCalls: number;
  
  /** Số lần thất bại */
  failedCalls: number;
  
  /** Tỷ lệ thành công */
  successRate: number;
  
  /** Thời gian thực thi trung bình (ms) */
  averageExecutionTime: number;
  
  /** Thời gian thực thi tối đa (ms) */
  maxExecutionTime: number;
  
  /** Thời gian thực thi tối thiểu (ms) */
  minExecutionTime: number;
  
  /** Số lần hit cache */
  cacheHits: number;
  
  /** Tỷ lệ hit cache */
  cacheHitRate: number;
  
  /** Thời gian cập nhật cuối */
  lastUpdated: number;
}

/**
 * Cấu hình tool registry
 */
export interface ToolRegistryConfig {
  /** Có enable caching không */
  enableCaching: boolean;
  
  /** Default cache TTL (seconds) */
  defaultCacheTtl: number;
  
  /** Default timeout (milliseconds) */
  defaultTimeout: number;
  
  /** Default rate limit (calls per minute) */
  defaultRateLimit: number;
  
  /** Có enable monitoring không */
  enableMonitoring: boolean;
  
  /** Có enable audit logging không */
  enableAuditLogging: boolean;
}

/**
 * Tool validation error
 */
export interface ToolValidationError {
  /** Field bị lỗi */
  field: string;
  
  /** Thông báo lỗi */
  message: string;
  
  /** Giá trị hiện tại */
  value: any;
  
  /** Giá trị mong đợi */
  expected?: any;
}

/**
 * Tool execution options
 */
export interface ToolExecutionOptions {
  /** Có sử dụng cache không */
  useCache?: boolean;
  
  /** Custom timeout */
  timeout?: number;
  
  /** Có retry khi thất bại không */
  retry?: boolean;
  
  /** Số lần retry tối đa */
  maxRetries?: number;
  
  /** Có log execution không */
  logExecution?: boolean;
  
  /** Priority của execution */
  priority?: 'low' | 'normal' | 'high';
}

/**
 * Tool anomaly detection
 */
export interface ToolAnomaly {
  /** Tên tool */
  toolName: string;
  
  /** Loại anomaly */
  type: 'high_failure_rate' | 'slow_execution' | 'unusual_usage' | 'error_spike';
  
  /** Mô tả anomaly */
  description: string;
  
  /** Severity level */
  severity: 'low' | 'medium' | 'high' | 'critical';
  
  /** Thời gian phát hiện */
  detectedAt: number;
  
  /** Dữ liệu liên quan */
  data: any;
  
  /** Gợi ý khắc phục */
  recommendations?: string[];
}
