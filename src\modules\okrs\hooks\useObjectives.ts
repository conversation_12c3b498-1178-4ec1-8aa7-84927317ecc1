import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { ObjectiveService } from '../services/objective.service';
import {
  ObjectiveQueryDto,
  CreateObjectiveDto,
  UpdateObjectiveDto,
} from '../types/objective.types';

// Key cho React Query
const OBJECTIVES_QUERY_KEY = 'objectives';

/**
 * Hook để lấy danh sách mục tiêu
 * @param params Tham số truy vấn
 * @returns Query result với danh sách mục tiêu
 */
export const useObjectives = (params?: ObjectiveQueryDto) => {
  return useQuery({
    queryKey: [OBJECTIVES_QUERY_KEY, params],
    queryFn: () => ObjectiveService.getObjectives(params),
    select: data => data.result,
  });
};

/**
 * Hook để lấy chi tiết mục tiêu
 * @param id ID mục tiêu
 * @returns Query result với chi tiết mục tiêu
 */
export const useObjective = (id: number) => {
  return useQuery({
    queryKey: [OBJECTIVES_QUERY_KEY, id],
    queryFn: () => ObjectiveService.getObjective(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook để tạo mục tiêu mới
 * @returns Mutation result cho việc tạo mục tiêu
 */
export const useCreateObjective = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateObjectiveDto) => ObjectiveService.createObjective(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [OBJECTIVES_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật mục tiêu
 * @returns Mutation result cho việc cập nhật mục tiêu
 */
export const useUpdateObjective = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateObjectiveDto }) =>
      ObjectiveService.updateObjective(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [OBJECTIVES_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [OBJECTIVES_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hook để xóa mục tiêu
 * @returns Mutation result cho việc xóa mục tiêu
 */
export const useDeleteObjective = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => ObjectiveService.deleteObjective(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [OBJECTIVES_QUERY_KEY] });
    },
  });
};

/**
 * Hook để xóa nhiều mục tiêu
 * @returns Mutation result cho việc xóa nhiều mục tiêu
 */
export const useBulkDeleteObjectives = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: number[]) => ObjectiveService.bulkDeleteObjectives({ ids }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [OBJECTIVES_QUERY_KEY] });
    },
  });
};
