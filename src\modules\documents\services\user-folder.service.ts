
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';
import { apiClient } from '@/shared/api/axios';

// Types
export interface FolderResponseDto {
  id: number;
  name: string;
  parentId: number | null;
  userId: number;
  path: string | null;
  root: number | null;
  createdAt: number;
  updatedAt: number;
}

export interface FolderDetailResponseDto extends FolderResponseDto {
  user: FolderUserDto | null;
  virtualWarehouse: VirtualWarehouseDto | null;
}

export interface FolderUserDto {
  id: number;
  fullName: string;
  email: string;
  phoneNumber: string;
}

export interface VirtualWarehouseDto {
  warehouseId: number;
  associatedSystem: string | null;
  purpose: string | null;
}

export interface QueryFolderDto extends QueryDto {
  userId?: number;
  parentId?: number | null;
  warehouseId?: number;
  sortBy?: 'id' | 'name' | 'createdAt' | 'updatedAt';
}

export interface CreateFolderDto {
  /**
   * Tên thư mục
   */
  name: string;

  /**
   * ID thư mục cha (null nếu là thư mục gốc)
   */
  parentId?: number | null;

  /**
   * ID kho ảo gốc
   */
  root?: number | null;
}

export interface UpdateFolderDto {
  name?: string;
  parentId?: number | null;
}

/**
 * Service xử lý API cho thư mục của người dùng
 */
export class UserFolderService {
  private static readonly BASE_URL = '/user/folders';

  /**
   * Lấy danh sách thư mục với phân trang và lọc
   */
  static async getFolders(params: QueryFolderDto): Promise<PaginatedResult<FolderResponseDto>> {
    const response = await apiClient.get<PaginatedResult<FolderResponseDto>>(
      this.BASE_URL,
      { params }
    );
    return response.result;
  }

  /**
   * Lấy danh sách thư mục gốc (không có thư mục cha)
   */
  static async getRootFolders(): Promise<FolderResponseDto[]> {
    const response = await apiClient.get<FolderResponseDto[]>(
      `${this.BASE_URL}/root`
    );
    return response.result;
  }

  /**
   * Lấy danh sách thư mục con của một thư mục
   */
  static async getChildFolders(parentId: number): Promise<FolderResponseDto[]> {
    const response = await apiClient.get<FolderResponseDto[]>(
      `${this.BASE_URL}/children/${parentId}`
    );
    return response.result;
  }

  /**
   * Lấy thông tin thư mục theo ID
   */
  static async getFolderById(id: number): Promise<FolderDetailResponseDto> {
    const response = await apiClient.get<FolderDetailResponseDto>(
      `${this.BASE_URL}/${id}`
    );
    return response.result;
  }

  /**
   * Tạo mới thư mục
   */
  static async createFolder(data: CreateFolderDto): Promise<FolderResponseDto> {
    const response = await apiClient.post<FolderResponseDto>(
      this.BASE_URL,
      data
    );
    return response.result;
  }

  /**
   * Cập nhật thư mục
   */
  static async updateFolder(id: number, data: UpdateFolderDto): Promise<FolderResponseDto> {
    const response = await apiClient.put<FolderResponseDto>(
      `${this.BASE_URL}/${id}`,
      data
    );
    return response.result;
  }

  /**
   * Xóa thư mục
   */
  static async deleteFolder(id: number): Promise<void> {
    await apiClient.delete(`${this.BASE_URL}/${id}`);
  }
}
