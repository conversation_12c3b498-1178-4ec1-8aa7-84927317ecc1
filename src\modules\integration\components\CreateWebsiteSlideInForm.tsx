import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Typography,
  Button,
  Icon,
  Input,
  FormItem,
  Card,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm/SlideInForm';
import { useCreateWebsite } from '../website/hooks/useWebsite';
import { createWebsiteSchema, CreateWebsiteFormData } from '../schemas/website.schema';
import { NotificationUtil } from '@/shared/utils/notification';

interface CreateWebsiteSlideInFormProps {
  isVisible: boolean;
  onClose: () => void;
}

/**
 * Component SlideInForm để tạo mới Website
 */
const CreateWebsiteSlideInForm: React.FC<CreateWebsiteSlideInFormProps> = ({
  isVisible,
  onClose,
}) => {
  const { t } = useTranslation();

  // API hooks
  const createWebsiteMutation = useCreateWebsite();

  // Form setup với react-hook-form và zod validation
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isValid, isDirty },
  } = useForm<CreateWebsiteFormData>({
    resolver: zodResolver(createWebsiteSchema),
    mode: 'onChange',
    defaultValues: {
      websiteName: '',
      host: '',
    },
  });

  // Xử lý submit form
  const onSubmit = useCallback(async (data: CreateWebsiteFormData) => {
    try {
      await createWebsiteMutation.mutateAsync(data);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        title: t('integration.website.createSuccess', 'Tạo website thành công!'),
        message: t('integration.website.createSuccessDesc', 'Website đã được thêm vào danh sách.'),
        duration: 3000,
      });

      // Reset form
      reset();

      // Đóng form
      onClose();

      // Note: onSuccess callback không được gọi vì API response
      // giờ là CreateWebsiteResponseDto thay vì WebsiteDto
      // Parent component sẽ tự refresh data thông qua React Query invalidation
    } catch (error) {
      console.error('Error creating website:', error);

      // Hiển thị thông báo lỗi
      NotificationUtil.error({
        title: t('integration.website.createError', 'Tạo website thất bại!'),
        message: t('integration.website.createErrorDesc', 'Vui lòng thử lại sau.'),
        duration: 5000,
      });
    }
  }, [createWebsiteMutation, reset, onClose, t]);

  // Xử lý đóng form
  const handleClose = useCallback(() => {
    if (isDirty && !createWebsiteMutation.isPending) {
      const confirmed = window.confirm(
        t('common.unsavedChanges', 'Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn đóng?')
      );
      if (!confirmed) return;
    }

    reset();
    onClose();
  }, [isDirty, createWebsiteMutation.isPending, reset, onClose, t]);

  // Xử lý hủy
  const handleCancel = useCallback(() => {
    reset();
    onClose();
  }, [reset, onClose]);

  return (
    <>
      {/* Overlay */}
      {isVisible && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
          onClick={handleClose}
        >
          <SlideInForm isVisible={isVisible}>
            <Card
              className="w-full max-w-lg relative"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="flex justify-between items-center mb-6">
                <Typography variant="h5" className="font-semibold">
                  {t('integration.website.createTitle', 'Thêm Website Mới')}
                </Typography>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClose}
                  disabled={createWebsiteMutation.isPending}
                  className="flex items-center space-x-1"
                >
                  <Icon name="x" size="sm" />
                  <span>{t('common.close', 'Đóng')}</span>
                </Button>
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                {/* Website Name Field */}
                <FormItem
                  label={t('integration.website.form.websiteName', 'Tên Website')}
                  required
                >
                  <Controller
                    name="websiteName"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder={t('integration.website.form.websiteNamePlaceholder', 'Nhập tên website (ví dụ: Website của tôi)')}
                        disabled={createWebsiteMutation.isPending}
                        fullWidth
                        leftIcon={<Icon name="globe" size="sm" />}
                        error={errors.websiteName?.message}
                      />
                    )}
                  />
                </FormItem>

                {/* Host Field */}
                <FormItem
                  label={t('integration.website.form.host', 'Host/Domain')}
                  required
                >
                  <div className="space-y-1">
                    <Controller
                      name="host"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder={t('integration.website.form.hostPlaceholder', 'example.com')}
                          disabled={createWebsiteMutation.isPending}
                          fullWidth
                          leftIcon={<Icon name="link" size="sm" />}
                          error={errors.host?.message}
                        />
                      )}
                    />
                    <Typography variant="caption" color="muted">
                      {t('integration.website.form.hostDescription', 'Nhập domain không có http:// (ví dụ: example.com)')}
                    </Typography>
                  </div>
                </FormItem>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={createWebsiteMutation.isPending}
                  >
                    {t('common.cancel', 'Hủy')}
                  </Button>
                  <Button
                    type="submit"
                    variant="primary"
                    isLoading={createWebsiteMutation.isPending}
                    disabled={!isValid || createWebsiteMutation.isPending}
                    className="min-w-[120px]"
                  >
                    {createWebsiteMutation.isPending
                      ? t('integration.website.creating', 'Đang tạo...')
                      : t('integration.website.create', 'Tạo Website')
                    }
                  </Button>
                </div>
              </form>

              {/* Loading Overlay */}
              {createWebsiteMutation.isPending && (
                <div className="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center rounded-lg">
                  <div className="flex items-center space-x-2">
                    <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                    <Typography variant="body2" color="muted">
                      {t('integration.website.processing', 'Đang xử lý...')}
                    </Typography>
                  </div>
                </div>
              )}
            </Card>
          </SlideInForm>
        </div>
      )}
    </>
  );
};

export default CreateWebsiteSlideInForm;
