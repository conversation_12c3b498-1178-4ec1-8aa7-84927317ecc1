import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import { apiClient } from '@/shared/api';
import {
  BankAccountDto,
  BankAccountQueryDto,
  CreateBankAccountDto,
  UpdateBankAccountDto,
  OTPVerificationDto,
  CreateVirtualAccountDto,
  BankAccountStatsDto,
} from '../types/bank-account.types';

/**
 * Bank Account API Services
 * Các service để gọi API liên quan đến tài khoản ngân hàng
 */

const BASE_URL = '/integration/bank-accounts';

/**
 * L<PERSON>y danh sách tài khoản ngân hàng
 */
export const getBankAccounts = async (
  params: BankAccountQueryDto = {}
): Promise<ApiResponseDto<PaginatedResult<BankAccountDto>>> => {
  const response = await apiClient.get<PaginatedResult<BankAccountDto>>(BASE_URL, {
    params,
  });
  return response;
};

/**
 * L<PERSON>y chi tiết tài khoản ngân hàng
 */
export const getBankAccountDetail = async (
  id: string
): Promise<ApiResponseDto<BankAccountDto>> => {
  const response = await apiClient.get<BankAccountDto>(`${BASE_URL}/${id}`);
  return response;
};

/**
 * Tạo tài khoản ngân hàng mới
 */
export const createBankAccount = async (
  data: CreateBankAccountDto
): Promise<ApiResponseDto<BankAccountDto>> => {
  const response = await apiClient.post<BankAccountDto>(BASE_URL, data);
  return response;
};

/**
 * Cập nhật tài khoản ngân hàng
 */
export const updateBankAccount = async (
  id: string,
  data: UpdateBankAccountDto
): Promise<ApiResponseDto<BankAccountDto>> => {
  const response = await apiClient.put<BankAccountDto>(`${BASE_URL}/${id}`, data);
  return response;
};

/**
 * Xóa tài khoản ngân hàng
 */
export const deleteBankAccount = async (id: string): Promise<ApiResponseDto<void>> => {
  const response = await apiClient.delete<void>(`${BASE_URL}/${id}`);
  return response;
};

/**
 * Xóa nhiều tài khoản ngân hàng
 */
export const deleteManyBankAccounts = async (ids: string[]): Promise<ApiResponseDto<void>> => {
  const response = await apiClient.delete<void>(`${BASE_URL}/bulk`, {
    data: { ids },
  });
  return response;
};

/**
 * Gửi OTP để xác thực tài khoản ngân hàng
 */
export const sendOTP = async (bankAccountId: string): Promise<ApiResponseDto<{ message: string }>> => {
  const response = await apiClient.post<{ message: string }>(`${BASE_URL}/${bankAccountId}/send-otp`);
  return response;
};

/**
 * Xác thực OTP
 */
export const verifyOTP = async (
  data: OTPVerificationDto
): Promise<ApiResponseDto<BankAccountDto>> => {
  const response = await apiClient.post<BankAccountDto>(`${BASE_URL}/verify-otp`, data);
  return response;
};

/**
 * Tạo tài khoản ảo (Virtual Account) - chỉ dành cho OCB
 */
export const createVirtualAccount = async (
  data: CreateVirtualAccountDto
): Promise<ApiResponseDto<BankAccountDto>> => {
  const response = await apiClient.post<BankAccountDto>(`${BASE_URL}/create-virtual-account`, data);
  return response;
};

/**
 * Lấy thống kê tài khoản ngân hàng
 */
export const getBankAccountStats = async (): Promise<ApiResponseDto<BankAccountStatsDto>> => {
  const response = await apiClient.get<BankAccountStatsDto>(`${BASE_URL}/stats`);
  return response;
};

/**
 * Kích hoạt tài khoản ngân hàng
 */
export const activateBankAccount = async (id: string): Promise<ApiResponseDto<BankAccountDto>> => {
  const response = await apiClient.post<BankAccountDto>(`${BASE_URL}/${id}/activate`);
  return response;
};

/**
 * Vô hiệu hóa tài khoản ngân hàng
 */
export const deactivateBankAccount = async (id: string): Promise<ApiResponseDto<BankAccountDto>> => {
  const response = await apiClient.post<BankAccountDto>(`${BASE_URL}/${id}/deactivate`);
  return response;
};

/**
 * Kiểm tra trạng thái kết nối tài khoản ngân hàng
 */
export const checkBankAccountConnection = async (
  id: string
): Promise<ApiResponseDto<{ isConnected: boolean; lastChecked: string }>> => {
  const response = await apiClient.get<{ isConnected: boolean; lastChecked: string }>(
    `${BASE_URL}/${id}/check-connection`
  );
  return response;
};

/**
 * Đồng bộ thông tin tài khoản ngân hàng
 */
export const syncBankAccount = async (id: string): Promise<ApiResponseDto<BankAccountDto>> => {
  const response = await apiClient.post<BankAccountDto>(`${BASE_URL}/${id}/sync`);
  return response;
};
