import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { useAuth } from '@/modules/auth';
import { ObjectiveService } from '@/modules/okrs/services/objective.service';
import { OkrCycleService } from '@/modules/okrs/services/okr-cycle.service';
import { CreateObjectiveDto, ObjectiveType } from '@/modules/okrs/types/objective.types';
import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { NotificationUtil } from '@/shared/utils/notification';

export interface ObjectiveFormProps {
  /**
   * Callback khi form được submit thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi form bị đóng
   */
  onCancel?: () => void;

  /**
   * ID của objective cần chỉnh sửa (nếu có)
   */
  objectiveId?: string;

  /**
   * ID của chu kỳ OKR mặc định (nếu có)
   */
  defaultCycleId?: string;
}

/**
 * Form tạo mới hoặc chỉnh sửa Objective
 */
const ObjectiveForm: React.FC<ObjectiveFormProps> = ({ onSuccess, onCancel, defaultCycleId }) => {
  const { t } = useTranslation();
  const formRef = useRef<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Tạo schema validation cho form cá nhân
  const objectiveSchema = z.object({
    title: z
      .string()
      .min(1, t('okrs:objective.form.validation.titleRequired', 'Tiêu đề không được để trống'))
      .max(
        255,
        t('okrs:objective.form.validation.titleMaxLength', 'Tiêu đề không được vượt quá 255 ký tự')
      ),
    description: z
      .string()
      .max(
        1000,
        t(
          'okrs:objective.form.validation.descriptionMaxLength',
          'Mô tả không được vượt quá 1000 ký tự'
        )
      )
      .optional(),
    cycleId: z.union([z.string(), z.number()], {
      required_error: t(
        'okrs:objective.form.validation.cycleRequired',
        'Chu kỳ OKR không được để trống'
      ),
    }),


  });

  // Load options function cho AsyncSelectWithPagination
  const loadCycleOptions = async (params: { search?: string; page?: number; limit?: number }) => {
    try {
      const response = await OkrCycleService.getOkrCycles({
        search: params.search,
        page: params.page || 1,
        limit: params.limit || 20,
      });

      return {
        items: response.result.items.map(cycle => ({
          value: cycle.id,
          label: cycle.name,
        })),
        totalItems: response.result.meta.totalItems,
        totalPages: response.result.meta.totalPages,
        currentPage: response.result.meta.currentPage,
      };
    } catch (error) {
      console.error('Error loading OKR cycles:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }
  };



  // Lấy thông tin người dùng hiện tại
  const { user } = useAuth();

  // Xử lý khi submit form
  const handleSubmit = async (values: unknown) => {
    try {
      setIsSubmitting(true);
      const formValues = values as {
        title: string;
        description?: string;
        cycleId: string | number;
      };

      // Chuẩn bị dữ liệu cho API (mặc định là INDIVIDUAL)
      const createObjectiveData: CreateObjectiveDto = {
        title: formValues.title,
        description: formValues.description || '',
        type: ObjectiveType.INDIVIDUAL,
        cycleId: Number(formValues.cycleId),
        ownerId: Number(user?.id) || 0, // Lấy ID người dùng hiện tại


      };

      // Gọi API tạo mới objective
      await ObjectiveService.createObjective(createObjectiveData);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('okrs:objective.form.createSuccess', 'Tạo mục tiêu thành công'),
      });

      // Gọi callback onSuccess nếu có
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      // Xử lý lỗi
      console.error('Error creating objective:', error);
      NotificationUtil.error({
        message: t('okrs:objective.form.createError', 'Có lỗi xảy ra khi tạo mục tiêu'),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý khi nhấn nút hủy
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  // Thiết lập giá trị mặc định cho form
  useEffect(() => {
    if (formRef.current && defaultCycleId) {
      formRef.current.setValues({ cycleId: defaultCycleId });
    }
  }, [defaultCycleId, formRef]);

  return (
    <Card title={t('okrs:objective.form.individualTitle', 'OKR mới')} className="mx-auto">
      <Form ref={formRef} schema={objectiveSchema} onSubmit={handleSubmit} className="space-y-6">
        <FormItem name="title" label={t('okrs:objective.form.title', 'Tiêu đề')} required>
          <Input
            type="text"
            placeholder={t('okrs:objective.form.titlePlaceholder', 'Thông tin')}
            fullWidth
          />
        </FormItem>

        <FormItem name="description" label={t('okrs:objective.form.description', 'Mô tả')}>
          <Input
            type="textarea"
            placeholder={t('okrs:objective.form.descriptionPlaceholder', 'Thông tin')}
            fullWidth
          />
        </FormItem>

        <FormItem name="cycleId" label={t('okrs:objective.form.cycle', 'Chu kỳ OKR')} required>
          <AsyncSelectWithPagination
            onChange={value => {
              if (formRef.current) {
                formRef.current.setValues({ cycleId: value });
              }
            }}
            loadOptions={loadCycleOptions}
            placeholder={t('okrs:objective.form.cyclePlaceholder', 'Chọn chu kỳ OKR')}
            fullWidth
            debounceTime={300}
            itemsPerPage={20}
            noOptionsMessage={t('okrs:objective.form.noCycles', 'Không tìm thấy chu kỳ OKR')}
            loadingMessage={t('common:loading', 'Đang tải...')}
            autoLoadInitial={true}
          />
        </FormItem>





        <div className="flex justify-end space-x-3 pt-4">
          <Button variant="outline" onClick={handleCancel} disabled={isSubmitting}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button type="submit" isLoading={isSubmitting}>
            {t('okrs:objective.form.submit', 'Tạo mục tiêu')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default ObjectiveForm;
