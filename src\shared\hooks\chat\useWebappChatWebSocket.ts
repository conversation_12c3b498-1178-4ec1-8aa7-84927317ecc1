/**
 * Hook cho Webapp Chat WebSocket
 * Chuyên dụng cho chat với AI agent
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  WebappChatWebSocketService,
  WebappChatConfig,
  WebappChatMessage,
  ConnectionStatus,
  ConversationInfo,
  TypingStatus,
  StreamChunk,
  initializeWebappChatWebSocket,
  getWebappChatWebSocketService
} from '@/shared/services/webapp-chat-websocket.service';

// Hook options
export interface UseWebappChatWebSocketOptions {
  config?: WebappChatConfig;
  conversationId?: number;
  autoJoinConversation?: boolean;
  enableTypingIndicator?: boolean;
  typingTimeout?: number;
}

// Hook return type
export interface UseWebappChatWebSocketReturn {
  // Connection
  isConnected: boolean;
  connectionStatus: ConnectionStatus;
  connect: () => Promise<void>;
  disconnect: () => void;
  
  // Conversation management
  joinConversation: (conversationId?: number) => Promise<void>;
  leaveConversation: (conversationId: number) => Promise<void>;
  currentConversationId: number | null;
  
  // Messages
  messages: WebappChatMessage[];
  sendMessage: (content: string) => Promise<void>;
  clearMessages: () => void;
  
  // Streaming
  streamingMessage: WebappChatMessage | null;
  isStreaming: boolean;
  
  // Typing indicators
  isAITyping: boolean;
  startTyping: () => void;
  stopTyping: () => void;
  
  // Error handling
  lastError: string | null;
  clearError: () => void;
}

/**
 * Main Webapp Chat WebSocket Hook
 */
export function useWebappChatWebSocket(options: UseWebappChatWebSocketOptions = {}): UseWebappChatWebSocketReturn {
  const {
    config,
    conversationId,
    autoJoinConversation = true,
    enableTypingIndicator = true,
    typingTimeout = 3000,
  } = options;

  // State
  const [service, setService] = useState<WebappChatWebSocketService | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [currentConversationId, setCurrentConversationId] = useState<number | null>(null);
  const [messages, setMessages] = useState<WebappChatMessage[]>([]);
  const [streamingMessage, setStreamingMessage] = useState<WebappChatMessage | null>(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isAITyping, setIsAITyping] = useState(false);
  const [lastError, setLastError] = useState<string | null>(null);

  // Refs
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isTypingRef = useRef(false);

  // Initialize service
  useEffect(() => {
    if (config) {
      const newService = initializeWebappChatWebSocket(config);
      setService(newService);
    } else {
      try {
        const existingService = getWebappChatWebSocketService();
        setService(existingService);
      } catch (error) {
        console.warn('[useWebappChatWebSocket] No service available and no config provided');
      }
    }
  }, [config]);

  // Setup event listeners
  useEffect(() => {
    if (!service) return;

    const unsubscribers: (() => void)[] = [];

    // Connection status
    unsubscribers.push(
      service.on('connection_status_changed', (data: { status: ConnectionStatus }) => {
        setConnectionStatus(data.status);
        setIsConnected(data.status === 'connected');
      })
    );

    // Conversation events
    unsubscribers.push(
      service.on('conversation_joined', (data: ConversationInfo) => {
        setCurrentConversationId(data.conversationId);
        setLastError(null);
      })
    );

    unsubscribers.push(
      service.on('conversation_left', (data: ConversationInfo) => {
        if (currentConversationId === data.conversationId) {
          setCurrentConversationId(null);
          setMessages([]);
          setStreamingMessage(null);
          setIsStreaming(false);
          setIsAITyping(false);
        }
      })
    );

    // Message events
    unsubscribers.push(
      service.on('message_received', (data: any) => {
        // User message confirmation - không cần xử lý gì đặc biệt
        console.log('[useWebappChatWebSocket] Message received confirmation:', data);
      })
    );

    unsubscribers.push(
      service.on('ai_response', (data: WebappChatMessage) => {
        // AI response hoàn chỉnh (không streaming)
        setMessages(prev => [...prev, data]);
        setIsAITyping(false);
      })
    );

    // Streaming events
    unsubscribers.push(
      service.on('ai_stream_chunk', (data: StreamChunk) => {
        setIsStreaming(true);
        setIsAITyping(false);
        
        setStreamingMessage(prev => {
          if (!prev) {
            // Tạo message mới cho streaming
            return {
              id: `stream_${data.messageId}`,
              content: data.chunk,
              sender: 'ai',
              timestamp: new Date(data.timestamp).toISOString(),
              conversationId: data.conversationId,
              messageId: data.messageId,
              isStreaming: true,
              streamComplete: false,
            };
          } else {
            // Append chunk vào message hiện tại
            return {
              ...prev,
              content: prev.content + data.chunk,
            };
          }
        });
      })
    );

    unsubscribers.push(
      service.on('ai_stream_end', (data: any) => {
        setIsStreaming(false);
        
        // Chuyển streaming message thành message hoàn chỉnh
        if (streamingMessage) {
          const completedMessage: WebappChatMessage = {
            ...streamingMessage,
            content: data.fullContent || streamingMessage.content,
            isStreaming: false,
            streamComplete: true,
          };
          
          setMessages(prev => [...prev, completedMessage]);
          setStreamingMessage(null);
        }
      })
    );

    // Typing events
    unsubscribers.push(
      service.on('ai_typing', (data: TypingStatus) => {
        setIsAITyping(data.isTyping);
      })
    );

    // Error events
    unsubscribers.push(
      service.on('error', (data: any) => {
        setLastError(data.message || data.error || 'Unknown error');
        setIsAITyping(false);
        setIsStreaming(false);
      })
    );

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
    };
  }, [service, currentConversationId, streamingMessage]);

  // Auto-join conversation
  useEffect(() => {
    if (service && isConnected && autoJoinConversation && !currentConversationId) {
      // Tự động join conversation (tạo mới nếu chưa có)
      const autoJoin = async () => {
        try {
          await joinConversation(conversationId);
        } catch (error) {
          console.error('[useWebappChatWebSocket] Failed to auto-join conversation:', error);
        }
      };

      autoJoin();
    }
  }, [service, isConnected, autoJoinConversation, currentConversationId, conversationId]);

  // Connection methods
  const connect = useCallback(async () => {
    if (service) {
      await service.connect();
    }
  }, [service]);

  const disconnect = useCallback(() => {
    if (service) {
      service.disconnect();
    }
  }, [service]);

  // Conversation methods
  const joinConversation = useCallback(async (targetConversationId?: number) => {
    if (service) {
      await service.joinConversation(targetConversationId);
    }
  }, [service]);

  const leaveConversation = useCallback(async (targetConversationId: number) => {
    if (service) {
      await service.leaveConversation(targetConversationId);
    }
  }, [service]);

  // Message methods
  const sendMessage = useCallback(async (content: string) => {
    if (!service) {
      throw new Error('Service not available');
    }

    if (!isConnected) {
      throw new Error('Not connected to server');
    }

    // Add user message immediately
    const userMessage: WebappChatMessage = {
      id: `user_${Date.now()}`,
      content,
      sender: 'user',
      timestamp: new Date().toISOString(),
      conversationId: currentConversationId || 0,
    };

    setMessages(prev => [...prev, userMessage]);
    setLastError(null);

    try {
      // Send to server
      await service.sendMessage(content);
    } catch (error) {
      // Don't remove user message on timeout - message might have been sent successfully
      // Only remove if it's a real connection error
      if (error.message.includes('Not connected') || error.message.includes('Service not available')) {
        setMessages(prev => prev.filter(msg => msg.id !== userMessage.id));
      } else {
        // For timeout errors, keep the message but log the error
        console.warn('[useWebappChatWebSocket] Send message timeout, but keeping user message:', error.message);
      }
      throw error;
    }
  }, [service, currentConversationId, isConnected]);

  const clearMessages = useCallback(() => {
    setMessages([]);
    setStreamingMessage(null);
    setIsStreaming(false);
    setIsAITyping(false);
  }, []);

  // Typing methods
  const startTyping = useCallback(() => {
    if (!service || !enableTypingIndicator) return;

    service.startTyping();
    isTypingRef.current = true;

    // Auto stop typing after timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      stopTyping();
    }, typingTimeout);
  }, [service, enableTypingIndicator, typingTimeout]);

  const stopTyping = useCallback(() => {
    if (!service || !isTypingRef.current) return;

    service.stopTyping();
    isTypingRef.current = false;

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
  }, [service]);

  // Error methods
  const clearError = useCallback(() => {
    setLastError(null);
  }, []);

  // Cleanup
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  return {
    // Connection
    isConnected,
    connectionStatus,
    connect,
    disconnect,
    
    // Conversation management
    joinConversation,
    leaveConversation,
    currentConversationId,
    
    // Messages
    messages,
    sendMessage,
    clearMessages,
    
    // Streaming
    streamingMessage,
    isStreaming,
    
    // Typing indicators
    isAITyping,
    startTyping,
    stopTyping,
    
    // Error handling
    lastError,
    clearError,
  };
}
