import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, CollapsibleCard, IconCard } from '@/shared/components/common';
import { CustomerDetailData } from './types';
import SimpleCustomFieldSelector from '../../SimpleCustomFieldSelector';
import CustomFieldRenderer from '../../CustomFieldRenderer';
import { useUpdateCustomerCustomFields } from '../../../hooks/useCustomerQuery';
import { MetadataFieldDto } from '../../../services/customer.service';

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number; // ID của custom field từ mockAvailableFields
  fieldId: number; // Alias cho id để tương thích với CustomFieldRenderer
  configId: string; // configId để gửi lên API
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}



interface CustomerCustomFieldsProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị trường tùy chỉnh của khách hàng
 */
const CustomerCustomFields: React.FC<CustomerCustomFieldsProps> = ({ customer }) => {
  const { t } = useTranslation('business');

  // Hooks
  const updateCustomFieldsMutation = useUpdateCustomerCustomFields();

  // State
  const [customerCustomFields, setCustomerCustomFields] = useState<SelectedCustomField[]>([]);
  const [hasChanges, setHasChanges] = useState(false);

  // Derived state
  const isSaving = updateCustomFieldsMutation.isPending;

  // Thêm trường tùy chỉnh vào khách hàng
  const handleToggleCustomFieldToCustomer = useCallback(
    (fieldId: number, fieldData: Record<string, unknown>) => {
      setCustomerCustomFields(prev => {
        // Kiểm tra xem field đã tồn tại chưa
        const existingIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingIndex >= 0) {
          // Nếu đã tồn tại, xóa nó (toggle off)
          setHasChanges(true);
          return prev.filter(field => field.fieldId !== fieldId);
        }

        // Thêm trường mới với thông tin đầy đủ
        const newField: SelectedCustomField = {
          id: Date.now(), // ID tạm thời
          fieldId,
          configId: (fieldData?.configId as string) || fieldId.toString(),
          label: (fieldData?.label as string) || `Field ${fieldId}`,
          component: (fieldData?.component as string) || (fieldData?.type as string) || 'text',
          type: (fieldData?.type as string) || 'text',
          required: (fieldData?.required as boolean) || false,
          configJson: (fieldData?.configJson as Record<string, unknown>) || {},
          value: { value: '' }, // Giá trị mặc định
        };

        setHasChanges(true);
        return [...prev, newField];
      });
    },
    []
  );



  // Cập nhật giá trị trường tùy chỉnh trong khách hàng
  const handleUpdateCustomFieldInCustomer = useCallback((customFieldId: number, value: string) => {
    setCustomerCustomFields(prev => prev.map(field =>
      field.id === customFieldId
        ? { ...field, value: { value } }
        : field
    ));
    setHasChanges(true);
  }, []);

  // Xóa trường tùy chỉnh khỏi khách hàng
  const handleRemoveCustomFieldFromCustomer = useCallback((customFieldId: number) => {
    setCustomerCustomFields(prev => prev.filter(field => field.id !== customFieldId));
    setHasChanges(true);
  }, []);

  // Handle save custom fields
  const handleSaveCustomFields = useCallback(async () => {
    try {
      // Convert to API format
      const metadata: MetadataFieldDto[] = customerCustomFields.map(field => ({
        configId: field.configId || field.id.toString(),
        value: field.value.value
      }));

      // Call API
      await updateCustomFieldsMutation.mutateAsync({
        id: parseInt(customer.id),
        data: { metadata }
      });

      // Reset changes flag
      setHasChanges(false);

    } catch (error) {
      console.error('Error saving custom fields:', error);
    }
  }, [customerCustomFields, customer.id, updateCustomFieldsMutation]);



  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="text-foreground">
          {t('business:customer.detail.customFields', 'Trường tùy chỉnh')}
        </Typography>
      }
      defaultOpen={false}
    >
      <div className="space-y-4">
        {/* SimpleCustomFieldSelector thay thế cho table */}
        <SimpleCustomFieldSelector
          onFieldSelect={fieldData => {
            handleToggleCustomFieldToCustomer(
              fieldData.id,
              fieldData as unknown as Record<string, unknown>
            );
          }}
          selectedFieldIds={customerCustomFields.map(f => f.fieldId)}
          placeholder={t(
            'business:customer.form.customFields.searchPlaceholder',
            'Nhập từ khóa và nhấn Enter để tìm trường tùy chỉnh...'
          )}
        />

        {customerCustomFields.length > 0 && (
          <div className="space-y-3">
            {customerCustomFields.map((field) => (
              <CustomFieldRenderer
                key={field.id}
                field={field}
                value={field.value.value as string || ''}
                onChange={(value) => handleUpdateCustomFieldInCustomer(field.id, value as string)}
                onRemove={() => handleRemoveCustomFieldFromCustomer(field.id)}
              />
            ))}
          </div>
        )}

        {customerCustomFields.length === 0 && (
          <div className="text-center py-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
              {t('business:customer.form.customFields.noFields', 'Chưa có trường tùy chỉnh nào. Sử dụng ô tìm kiếm trên để thêm trường.')}
            </Typography>
          </div>
        )}

        {/* Save button - only show if there are changes */}
        {hasChanges && customerCustomFields.length > 0 && (
          <div className="flex justify-end pt-4 border-t border-border">
            <IconCard
              icon="check"
              onClick={handleSaveCustomFields}
              disabled={isSaving}
              variant="primary"
              title={t('common:save')}
              size="md"
              isLoading={isSaving}
            />
          </div>
        )}
      </div>
    </CollapsibleCard>
  );
};

export default CustomerCustomFields;
