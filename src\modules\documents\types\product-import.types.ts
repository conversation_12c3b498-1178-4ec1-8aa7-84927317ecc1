// Product Import Types
export interface ProductImportData {
  name: string;
  price: {
    listPrice: number;
    salePrice: number;
    currency: string;
  } | {
    priceDescription: string;
  } | null;
  typePrice: 'HAS_PRICE' | 'STRING_PRICE' | 'NO_PRICE';
  description?: string;
  imagesMediaTypes?: string[];
  tags?: string[];
  customFields?: Array<{
    customFieldId: number;
    value: {
      value: string;
    };
  }>;
  shipmentConfig?: {
    widthCm?: number;
    heightCm?: number;
    lengthCm?: number;
    weightGram?: number;
  };
  classifications?: Array<{
    type: string;
    price: {
      listPrice: number;
      salePrice: number;
      currency: string;
    };
    customFields: Array<{
      label: string;
      type: string;
      required: boolean;
      value: {
        value: string;
      };
    }>;
  }>;
}

export interface ProductField {
  key: string;
  label: string;
  type: 'text' | 'number' | 'select' | 'textarea' | 'boolean';
  required: boolean;
  options?: string[]; // For select fields
}

export interface ProductColumnMapping {
  excelColumn: string;
  productField: string;
}

export interface ProductExcelData {
  headers: string[];
  rows: (string | number | boolean | null)[][];
  fileName: string;
}

export interface ProductValidationResult {
  totalRows: number;
  validRows: number;
  invalidRows: number;
  errors: ProductValidationError[];
  validData: ProductImportData[];
}

export interface ProductValidationError {
  row: number;
  field: string;
  message: string;
  value: unknown;
}

export interface ProductImportOptions {
  skipInvalidRows: boolean;
  updateExisting: boolean; // Update existing products based on SKU
  sendNotification: boolean;
}

export interface ProductImportProgress {
  jobId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number; // 0-100
  processedRows: number;
  totalRows: number;
  successCount: number;
  errorCount: number;
  errors: ProductValidationError[];
  startTime: string;
  endTime?: string;
}

export interface ProductImportState {
  step: 'upload' | 'mapping' | 'preview' | 'importing';
  excelData?: ProductExcelData;
  mappings: ProductColumnMapping[];
  validationResult?: ProductValidationResult;
  importOptions: ProductImportOptions;
  importProgress?: ProductImportProgress;
}

export interface ProductImportUrlData {
  url: string;
  hasHeader: boolean;
}

// API Request/Response Types
export interface ProductImportUploadRequest {
  file: File;
  hasHeader: boolean;
}

export interface ProductImportUploadResponse {
  jobId: string;
  excelData: ProductExcelData;
}

export interface ProductImportValidateRequest {
  jobId: string;
  mappings: ProductColumnMapping[];
  options: ProductImportOptions;
}

export interface ProductImportValidateResponse {
  validationResult: ProductValidationResult;
}

export interface ProductImportStartRequest {
  jobId: string;
  mappings: ProductColumnMapping[];
  options: ProductImportOptions;
}

export interface ProductImportStartResponse {
  jobId: string;
  status: string;
}

export interface ProductImportStatusResponse {
  progress: ProductImportProgress;
}

// Constants
export const PRODUCT_IMPORT_STEPS = {
  UPLOAD: 'upload',
  MAPPING: 'mapping', 
  PREVIEW: 'preview',
  IMPORTING: 'importing',
} as const;

export const PRODUCT_IMPORT_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const;

// Batch Create Product Types
export interface BatchCreateProductDto {
  products: ProductImportData[];
}

export interface BatchCreateProductResponse {
  successProducts: Array<{
    id: number;
    name: string;
    uploadUrls?: {
      imagesUploadUrls?: Array<{
        url: string;
        key: string;
        index: number;
      }>;
    };
  }>;
  failedProducts: Array<{
    index: number;
    productName: string;
    error: string;
  }>;
  totalProducts: number;
  successCount: number;
  failedCount: number;
}

export type ProductImportStep = typeof PRODUCT_IMPORT_STEPS[keyof typeof PRODUCT_IMPORT_STEPS];
export type ProductImportStatus = typeof PRODUCT_IMPORT_STATUS[keyof typeof PRODUCT_IMPORT_STATUS];
