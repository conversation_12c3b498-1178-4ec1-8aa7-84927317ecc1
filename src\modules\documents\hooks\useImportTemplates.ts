import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { CustomerImportService, CustomerImportBusinessService } from '../services';
import { MappingTemplate, ColumnMapping } from '../types/customer-import.types';
import { CUSTOMER_IMPORT_QUERY_KEYS } from '../constants/customer-import.constants';

/**
 * Interface cho templates query parameters
 */
export interface TemplatesQueryParams extends Record<string, unknown> {
  page?: number;
  limit?: number;
  search?: string;
  isPublic?: boolean;
}

/**
 * Interface cho create template data
 */
export interface CreateTemplateData {
  name: string;
  description?: string;
  mappings: ColumnMapping[];
  isPublic: boolean;
  tags?: string[];
}

/**
 * Hook để quản lý mapping templates
 */
export const useImportTemplates = (params?: TemplatesQueryParams) => {
  const queryClient = useQueryClient();

  // Query để lấy danh sách templates
  const {
    data: templatesData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: CUSTOMER_IMPORT_QUERY_KEYS.TEMPLATES(params),
    queryFn: () => CustomerImportService.getMappingTemplates(params),
    select: (response) => response.result,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Mutation để tạo template mới
  const createTemplateMutation = useMutation({
    mutationFn: (data: CreateTemplateData) => 
      CustomerImportService.createMappingTemplate(data),
    onSuccess: () => {
      // Invalidate templates query to refresh list
      queryClient.invalidateQueries({ queryKey: CUSTOMER_IMPORT_QUERY_KEYS.TEMPLATES() });
    },
  });

  // Mutation để cập nhật template
  const updateTemplateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<MappingTemplate> }) =>
      CustomerImportService.updateMappingTemplate(id, data),
    onSuccess: (_response, variables) => {
      // Invalidate both templates list and specific template
      queryClient.invalidateQueries({ queryKey: CUSTOMER_IMPORT_QUERY_KEYS.TEMPLATES() });
      queryClient.invalidateQueries({ queryKey: CUSTOMER_IMPORT_QUERY_KEYS.TEMPLATE(variables.id) });
    },
  });

  // Mutation để xóa template
  const deleteTemplateMutation = useMutation({
    mutationFn: (id: string) => CustomerImportService.deleteMappingTemplate(id),
    onSuccess: () => {
      // Invalidate templates query to refresh list
      queryClient.invalidateQueries({ queryKey: CUSTOMER_IMPORT_QUERY_KEYS.TEMPLATES() });
    },
  });

  const createTemplate = (data: CreateTemplateData) => {
    createTemplateMutation.mutate(data);
  };

  const updateTemplate = (id: string, data: Partial<MappingTemplate>) => {
    updateTemplateMutation.mutate({ id, data });
  };

  const deleteTemplate = (id: string) => {
    deleteTemplateMutation.mutate(id);
  };

  return {
    // Data
    templates: templatesData?.items || [],
    total: templatesData?.total || 0,
    page: templatesData?.page || 1,
    limit: templatesData?.limit || 10,
    
    // States
    isLoading,
    error,
    isCreating: createTemplateMutation.isPending,
    isUpdating: updateTemplateMutation.isPending,
    isDeleting: deleteTemplateMutation.isPending,
    
    // Errors
    createError: createTemplateMutation.error,
    updateError: updateTemplateMutation.error,
    deleteError: deleteTemplateMutation.error,
    
    // Actions
    refetch,
    createTemplate,
    updateTemplate,
    deleteTemplate,
  };
};

/**
 * Hook để lấy single template
 */
export const useImportTemplate = (templateId: string | null) => {
  const {
    data: templateData,
    isLoading,
    error,
  } = useQuery({
    queryKey: CUSTOMER_IMPORT_QUERY_KEYS.TEMPLATE(templateId || ''),
    queryFn: async () => {
      // Since we don't have a specific API for single template,
      // we'll get it from the templates list
      const response = await CustomerImportService.getMappingTemplates();
      const template = response.result?.items.find((t: { id: string }) => t.id === templateId);
      if (!template) {
        throw new Error('Template not found');
      }
      return { result: template };
    },
    select: (response) => response.result,
    enabled: !!templateId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    template: templateData,
    isLoading,
    error,
  };
};

/**
 * Hook để tạo template từ current mappings
 */
export const useCreateTemplateFromMappings = () => {
  const queryClient = useQueryClient();

  const createFromMappingsMutation = useMutation({
    mutationFn: async ({
      name,
      mappings,
      description,
      isPublic = false,
    }: {
      name: string;
      mappings: ColumnMapping[];
      description?: string;
      isPublic?: boolean;
    }) => {
      return CustomerImportBusinessService.createMappingTemplate(
        name,
        mappings,
        description,
        isPublic
      );
    },
    onSuccess: () => {
      // Invalidate templates query to refresh list
      queryClient.invalidateQueries({ queryKey: CUSTOMER_IMPORT_QUERY_KEYS.TEMPLATES() });
    },
  });

  const createFromMappings = (
    name: string,
    mappings: ColumnMapping[],
    description?: string,
    isPublic?: boolean
  ) => {
    createFromMappingsMutation.mutate({ name, mappings, description, isPublic });
  };

  return {
    createFromMappings,
    isCreating: createFromMappingsMutation.isPending,
    error: createFromMappingsMutation.error,
    createdTemplate: createFromMappingsMutation.data,
  };
};

/**
 * Hook để apply template
 */
export const useApplyTemplate = () => {
  const applyTemplate = (template: MappingTemplate, excelHeaders: string[]) => {
    return CustomerImportBusinessService.applyTemplate(template, excelHeaders);
  };

  return {
    applyTemplate,
  };
};

/**
 * Hook để search templates
 */
export const useSearchTemplates = (searchTerm: string) => {
  const { templates, isLoading } = useImportTemplates({
    search: searchTerm,
    limit: 50, // Get more results for search
  });

  const filteredTemplates = templates.filter((template: { name: string; description?: string; tags?: string[] }) =>
    template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.tags?.some((tag: string) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return {
    templates: filteredTemplates,
    isLoading,
  };
};

export default useImportTemplates;
