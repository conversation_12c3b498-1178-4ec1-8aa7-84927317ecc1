/**
 * Service cho thống kê nhân viên
 */
import { apiClient } from '@/shared/api/axios';
import { ApiResponse } from '@/shared/types/api.types';

/**
 * Interface cho phân bố phòng ban từ API
 */
export interface DepartmentDistributionResponse {
  totalEmployees: number;
  departments: {
    departmentId: number;
    departmentName: string;
    employeeCount: number;
    percentage: number;
  }[];
  unassignedEmployees: number;
  unassignedPercentage: number;
}

/**
 * Interface cho phân bố phòng ban (legacy - để tương thích)
 */
export interface DepartmentDistribution {
  departmentName: string;
  employeeCount: number;
  percentage?: number;
}

/**
 * Interface cho phân bố loại hợp đồng từ API
 */
export interface ContractDistributionResponse {
  totalEmployees: number;
  contracts: {
    contractType: string;
    contractTypeName: string;
    employeeCount: number;
    percentage: number;
  }[];
  noContractEmployees: number;
  noContractPercentage: number;
}

/**
 * Interface cho phân bố loại hợp đồng (legacy - để tương thích)
 */
export interface EmploymentTypeDistribution {
  type: string;
  count: number;
  percentage?: number;
}

/**
 * Interface cho thống kê nhân viên overview từ API
 */
export interface EmployeeOverview {
  totalEmployees: number;
  totalUserAccounts: number;
  activeEmployees: number;
  inactiveEmployees: number;
  newEmployeesThisMonth: number;
  probationEmployees: number;
  activeEmployeePercentage: number;
  accountCoveragePercentage: number;
}

/**
 * Interface cho thống kê nhân viên đầy đủ (bao gồm cả overview và các thống kê khác)
 */
export interface EmployeeStatistics extends EmployeeOverview {
  departmentDistribution: DepartmentDistribution[];
  employmentTypeDistribution: EmploymentTypeDistribution[];
  averageServiceYears: number;
  upcomingProbationEnds: number;
}

/**
 * Service cho thống kê nhân viên
 */
export class EmployeeStatisticsService {
  private static readonly BASE_URL = '/api/hrm/employees';

  /**
   * Lấy thống kê tổng quan nhân viên từ API
   */
  static async getEmployeeOverview(): Promise<EmployeeOverview> {
    const response = await apiClient.get<EmployeeOverview>(`${this.BASE_URL}/overview`);
    return response.result;
  }

  /**
   * Lấy phân bố nhân viên theo phòng ban từ API
   */
  static async getDepartmentDistribution(): Promise<DepartmentDistributionResponse> {
    const response = await apiClient.get<DepartmentDistributionResponse>(`${this.BASE_URL}/statistics/department-distribution`);
    return response.result;
  }

  /**
   * Lấy phân bố nhân viên theo loại hợp đồng từ API
   */
  static async getContractDistribution(): Promise<ContractDistributionResponse> {
    const response = await apiClient.get<ContractDistributionResponse>(`${this.BASE_URL}/statistics/contract-distribution`);
    return response.result;
  }

  /**
   * Lấy thống kê tổng quan nhân viên (kết hợp overview với dữ liệu thực từ API)
   */
  static async getEmployeeStatistics(): Promise<ApiResponse<EmployeeStatistics>> {
    try {
      // Lấy dữ liệu từ các API thực
      const [overview, departmentDist, contractDist] = await Promise.all([
        this.getEmployeeOverview(),
        this.getDepartmentDistribution(),
        this.getContractDistribution(),
      ]);

      // Validate overview data
      if (!overview || typeof overview !== 'object') {
        throw new Error('Invalid overview data received from API');
      }

      // Chuyển đổi dữ liệu department distribution từ API sang format cũ
      const departmentDistribution: DepartmentDistribution[] = [
        ...departmentDist.departments.map(dept => ({
          departmentName: dept.departmentName,
          employeeCount: dept.employeeCount,
          percentage: dept.percentage,
        })),
        // Thêm nhân viên chưa được phân phòng ban nếu có
        ...(departmentDist.unassignedEmployees > 0 ? [{
          departmentName: 'Chưa phân phòng ban',
          employeeCount: departmentDist.unassignedEmployees,
          percentage: departmentDist.unassignedPercentage,
        }] : []),
      ];

      // Chuyển đổi dữ liệu contract distribution từ API sang format cũ
      const employmentTypeDistribution: EmploymentTypeDistribution[] = [
        ...contractDist.contracts.map(contract => ({
          type: contract.contractType,
          count: contract.employeeCount,
          percentage: contract.percentage,
        })),
        // Thêm nhân viên chưa có hợp đồng nếu có
        ...(contractDist.noContractEmployees > 0 ? [{
          type: 'no_contract',
          count: contractDist.noContractEmployees,
          percentage: contractDist.noContractPercentage,
        }] : []),
      ];

      // Mock data cho các thống kê khác (sẽ được thay thế bằng API thực sau)
      const probationEmployees = overview.probationEmployees || 0;

      const additionalStats = {
        departmentDistribution,
        employmentTypeDistribution,
        averageServiceYears: 2.5, // TODO: Thay bằng API thực
        upcomingProbationEnds: Math.floor(probationEmployees * 0.4), // TODO: Thay bằng API thực
      };

      const combinedData: EmployeeStatistics = {
        ...overview,
        ...additionalStats,
      };

      return {
        success: true,
        result: combinedData,
        message: 'Success',
      };
    } catch (error) {
      console.error('Error fetching employee statistics:', error);
      throw error;
    }
  }

  /**
   * Lấy thống kê nhân viên theo khoảng thời gian
   */
  static async getEmployeeStatisticsByDateRange(
    startDate: string,
    endDate: string
  ): Promise<ApiResponse<EmployeeStatistics>> {
    const response = await apiClient.get<EmployeeStatistics>(
      `${this.BASE_URL}/date-range`,
      {
        params: { startDate, endDate },
      }
    );
    return response;
  }

  /**
   * Lấy thống kê nhân viên theo phòng ban
   */
  static async getEmployeeStatisticsByDepartment(
    departmentId: number
  ): Promise<ApiResponse<EmployeeStatistics>> {
    const response = await apiClient.get<EmployeeStatistics>(
      `${this.BASE_URL}/department/${departmentId}`
    );
    return response;
  }
}
