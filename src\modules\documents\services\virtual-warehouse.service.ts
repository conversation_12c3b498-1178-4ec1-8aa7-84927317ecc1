
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { apiClient } from '@/shared/api/axios';
import {
  VirtualWarehouseListItemDto,
  VirtualWarehouseDetailDto,
  CreateVirtualWarehouseDto,
  UpdateVirtualWarehouseDto,
  VirtualWarehouseQueryParams,
} from '../types/virtual-warehouse.types';

/**
 * Service cho quản lý kho ảo
 */
export class VirtualWarehouseService {
  private static readonly BASE_URL = '/user/virtual-warehouses';

  /**
   * L<PERSON>y danh sách kho ảo với phân trang
   */
  static async getVirtualWarehouses(
    params: VirtualWarehouseQueryParams
  ): Promise<PaginatedResult<VirtualWarehouseListItemDto>> {
    const response = await apiClient.get<PaginatedResult<VirtualWarehouseListItemDto>>(
      this.BASE_URL,
      { params }
    );
    return response.result;
  }

  /**
   * L<PERSON>y thông tin chi tiết kho ảo theo ID
   */
  static async getVirtualWarehouseById(warehouseId: number): Promise<VirtualWarehouseDetailDto> {
    const response = await apiClient.get<VirtualWarehouseDetailDto>(
      `${this.BASE_URL}/${warehouseId}`
    );
    return response.result;
  }

  /**
   * Tạo mới kho ảo
   * @param warehouseId ID của warehouse để tạo virtual warehouse
   * @param data Dữ liệu tạo virtual warehouse
   */
  static async createVirtualWarehouse(
    warehouseId: number,
    data: CreateVirtualWarehouseDto
  ): Promise<VirtualWarehouseDetailDto> {
    const response = await apiClient.post<VirtualWarehouseDetailDto>(
      `${this.BASE_URL}/${warehouseId}`,
      data
    );
    return response.result;
  }

  /**
   * Cập nhật kho ảo
   */
  static async updateVirtualWarehouse(
    warehouseId: number,
    data: UpdateVirtualWarehouseDto
  ): Promise<VirtualWarehouseDetailDto> {
    const response = await apiClient.put<VirtualWarehouseDetailDto>(
      `${this.BASE_URL}/${warehouseId}`,
      data
    );
    return response.result;
  }

  /**
   * Xóa kho ảo
   */
  static async deleteVirtualWarehouse(warehouseId: number): Promise<void> {
    await apiClient.delete(`${this.BASE_URL}/${warehouseId}`);
  }

  /**
   * Xóa nhiều kho ảo (thực hiện tuần tự)
   */
  static async deleteMultipleVirtualWarehouses(warehouseIds: number[]): Promise<void> {
    // Vì backend không có bulk delete, ta sẽ xóa tuần tự
    for (const warehouseId of warehouseIds) {
      await this.deleteVirtualWarehouse(warehouseId);
    }
  }
}
