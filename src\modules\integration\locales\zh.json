{"integration": {"common": {"title": "集成", "description": "管理与外部服务的集成", "back": "返回", "cancel": "取消", "delete": "删除", "confirm": "确认", "activate": "激活", "deactivate": "停用", "retry": "重试", "error": "发生错误"}, "sms": {"title": "短信集成", "description": "配置与短信提供商的集成", "configName": "配置名称", "configNameHelp": "例如：营销短信，通知短信", "configNamePlaceholder": "输入配置名称", "isActive": "激活", "provider": "短信提供商", "selectProvider": "选择提供商", "otherProvider": "其他", "fromPhone": "发送电话号码", "fromPhoneHelp": "发送短信时显示的电话号码", "apiKey": "API密钥", "apiSecret": "API密钥", "testConnection": "测试连接", "testResult": "连接测试结果", "testSuccess": "成功连接到短信提供商", "testError": "无法连接到短信提供商。请检查您的配置。", "saveSuccess": "短信配置保存成功", "saveError": "保存短信配置时出错"}, "email": {"title": "电子邮件集成", "description": "配置与电子邮件提供商的集成", "configName": "配置名称", "configNameHelp": "例如：公司电子邮件，个人Gmail", "configNamePlaceholder": "输入配置名称", "senderEmail": "发件人电子邮件", "senderName": "发件人姓名", "senderNameHelp": "收件人查看电子邮件时显示的名称", "senderNamePlaceholder": "输入发件人姓名", "smtpHost": "SMTP主机", "smtpHostHelp": "例如：smtp.gmail.com，smtp.office365.com", "smtpPort": "SMTP端口", "smtpPortHelp": "例如：TLS使用587，SSL使用465", "requireSSL": "需要SSL", "securityProtocol": "安全协议", "requireAuth": "需要认证", "username": "用户名", "password": "密码", "testConnection": "测试连接", "testResult": "连接测试结果", "testSuccess": "成功连接到电子邮件服务器", "testError": "无法连接到电子邮件服务器。请检查您的配置。", "saveSuccess": "电子邮件配置保存成功", "saveError": "保存电子邮件配置时出错"}, "social": {"title": "社交媒体集成", "description": "管理与社交媒体平台的集成", "facebook": "Facebook", "instagram": "Instagram", "twitter": "Twitter", "linkedin": "LinkedIn", "youtube": "YouTube", "tiktok": "TikTok", "connect": "连接", "disconnect": "断开连接", "connected": "已连接", "notConnected": "未连接", "connectSuccess": "成功连接到{{platform}}", "connectError": "连接到{{platform}}时出错", "disconnectSuccess": "成功从{{platform}}断开连接", "disconnectError": "从{{platform}}断开连接时出错", "networkAriaLabel": "{{name}}社交网络"}, "facebook": {"title": "Facebook集成", "description": "管理已链接的Facebook账户", "addPage": "添加Facebook页面", "connecting": "连接中...", "processing": "正在处理Facebook连接...", "search": "搜索Facebook页面...", "loadError": "无法加载Facebook页面列表", "noPages": "暂无Facebook页面", "noPagesDescription": "您还没有链接任何Facebook页面。添加Facebook页面以开始使用。", "confirmDelete": "您确定要删除此Facebook页面吗？", "connectAgent": "连接代理", "disconnectAgent": "断开代理连接", "status": {"label": "状态", "active": "活跃", "inactive": "非活跃", "error": "错误"}, "agent": {"label": "代理"}, "pageName": "页面名称", "personalName": "个人姓名", "pageId": "页面ID", "isActive": "是否活跃", "hasError": "是否有错误"}, "accounts": {"title": "关联账户", "description": "管理与系统关联的账户", "addAccount": "添加账户", "removeAccount": "删除账户", "accountName": "账户名称", "accountType": "账户类型", "linkedDate": "关联日期", "noAccounts": "尚未关联任何账户", "confirmRemove": "您确定要删除此账户吗？", "removeSuccess": "账户删除成功", "removeError": "删除账户时出错", "defaultAccount": "默认账户", "failedToLoad": "无法加载账户"}, "website": {"title": "网站集成", "description": "管理与网站的集成", "domain": "域名", "apiKey": "API密钥", "secretKey": "密钥", "webhookUrl": "Webhook网址", "generateKey": "生成新密钥", "copyKey": "复制", "keyCopied": "已复制到剪贴板", "saveSuccess": "网站配置保存成功", "saveError": "保存网站配置时出错", "confirmActivate": "您确定要激活此网站吗？", "confirmDeactivate": "您确定要停用此网站吗？", "host": "主机", "status": "状态", "verified": "已验证", "notVerified": "未验证", "connected": "已连接", "notConnected": "未连接", "agent": "代理", "noAgent": "未连接代理", "createdAt": "创建时间", "actions": "操作", "widgetScript": "小部件脚本", "widgetScriptDesc": "将此脚本复制并粘贴到您的网站中以集成聊天小部件。", "createTitle": "添加新网站", "createSuccess": "网站创建成功！", "createSuccessDesc": "网站已添加到列表中。", "createError": "创建网站失败！", "createErrorDesc": "请稍后重试。", "creating": "创建中...", "create": "创建网站", "deleteSuccess": "网站删除成功！", "deleteSuccessDesc": "网站已从列表中移除。", "deleteError": "删除网站失败！", "deleteErrorDesc": "请稍后重试。", "copySuccess": "已复制！", "copySuccessDesc": "脚本已复制到剪贴板。", "copyScript": "复制", "noWebsites": "暂无网站", "noWebsitesDescription": "您还没有添加任何网站。添加网站以开始使用。", "addWebsite": "添加网站", "noSearchResults": "未找到结果", "noSearchResultsDescription": "没有网站符合您的搜索条件。", "clearFilters": "清除筛选", "confirmDelete": "确认删除", "confirmDeleteDesc": "您确定要删除此网站吗？此操作无法撤销。", "deleting": "删除中...", "form": {"websiteName": "网站名称", "websiteNamePlaceholder": "输入网站名称", "host": "主机/域名", "hostPlaceholder": "redai.vn 或 https://www.redai.vn", "hostDescription": "输入域名或完整URL。系统将自动规范化。"}, "filter": {"websiteName": "网站名称", "host": "主机", "createdAt": "创建时间", "verify": "验证状态", "asc": "升序", "desc": "降序", "all": "全部", "verified": "已验证", "unverified": "未验证"}}, "bankAccount": {"title": "银行账户集成", "description": "管理与系统集成的银行账户", "createTitle": "添加银行账户", "createDescription": "输入银行账户信息以与系统集成", "bankName": "银行", "selectBank": "选择银行", "accountNumber": "账号", "accountNumberPlaceholder": "输入账号", "accountNumberHelp": "输入银行账号（6-20位数字）", "accountName": "账户名称", "idNumber": "身份证号", "idNumberPlaceholder": "输入身份证号", "idNumberHelp": "输入在银行注册的身份证号", "phoneNumber": "手机号码", "phoneNumberPlaceholder": "输入手机号码", "phoneNumberHelp": "在银行注册的手机号码，用于接收验证码", "storeName": "店铺名称", "storeNamePlaceholder": "输入店铺名称", "storeNameHelp": "您的店铺/商户名称", "storeAddress": "店铺地址", "storeAddressPlaceholder": "输入店铺地址", "storeAddressHelp": "您的店铺/商户详细地址", "create": "创建账户", "bankInfo": "银行信息", "status": "状态", "statusActive": "活跃", "statusPending": "待验证", "statusInactive": "不活跃", "statusSuspended": "已暂停", "statusExpired": "已过期", "virtualAccount": {"title": "虚拟账户", "yes": "是", "no": "否"}, "createVA": "创建虚拟账户", "createSuccess": "账户创建成功", "createSuccessDescription": "银行账户已成功创建", "createError": "创建账户失败", "createErrorDescription": "无法创建银行账户。请检查信息。", "createCompleteSuccess": "账户创建完成", "createCompleteSuccessDescription": "银行账户已成功创建并激活", "deleteSuccess": "删除成功", "deleteSuccessDescription": "银行账户已成功删除", "deleteError": "删除失败", "deleteErrorDescription": "无法删除银行账户。请重试。", "deleteConfirmTitle": "确认删除账户", "deleteConfirmMessage": "您确定要删除此银行账户吗？此操作无法撤销。", "bulkDeleteSuccess": "删除成功", "bulkDeleteSuccessDescription": "已删除{{count}}个银行账户", "bulkDeleteError": "删除失败", "bulkDeleteErrorDescription": "无法删除银行账户。请重试。", "bulkDeleteConfirmTitle": "确认批量删除账户", "bulkDeleteConfirmMessage": "您确定要删除{{count}}个选中的银行账户吗？此操作无法撤销。", "selectAccountsToDelete": "请至少选择一个账户进行删除", "createVASuccess": "虚拟账户创建成功", "createVASuccessDescription": "虚拟账户已成功创建", "createVAError": "创建虚拟账户失败", "createVAErrorDescription": "无法创建虚拟账户。请重试。", "activateSuccess": "激活成功", "activateSuccessDescription": "银行账户已成功激活", "otpVerification": "短信验证", "otpVerificationDescription": "输入发送到{{bankName}}注册手机号的验证码", "enterOtp": "输入验证码", "otpSent": "验证码已发送", "otpSentDescription": "请检查您手机上的短信", "otpSendError": "发送验证码失败", "otpSendErrorDescription": "无法发送验证码。请稍后重试。", "resendOtp": "重新发送验证码", "resendOtpCountdown": "{{countdown}}秒后重新发送", "verify": "验证", "otpVerifySuccess": "验证成功", "otpVerifySuccessDescription": "银行账户已成功激活", "otpVerifyError": "验证失败", "otpVerifyErrorDescription": "验证码错误或已过期。请重试。", "invalidOtpLength": "验证码长度错误", "invalidOtpLengthDescription": "验证码必须为{{length}}位"}, "banking": {"bankAccount": "银行账户", "bank": "银行", "connectionMethod": "连接方式", "estimatedBalance": "预估余额", "accountName": "账户名称", "accountNumber": "账号", "connectApi": "连接银行API"}, "ai": {"title": "AI提供商管理", "description": "描述", "id": "ID", "name": "提供商名称", "baseUrl": "基础URL", "apiVersion": "API版本", "models": "支持的模型", "maxTokens": "最大令牌数", "status": "状态", "actions": "操作", "active": "活跃", "inactive": "不活跃", "addProvider": "添加AI提供商", "editProvider": "编辑AI提供商", "providersList": "AI提供商列表", "namePlaceholder": "输入AI提供商名称", "baseUrlPlaceholder": "输入API基础URL", "descriptionPlaceholder": "输入描述", "apiVersionPlaceholder": "输入API版本", "noData": "没有可用数据", "searchPlaceholder": "搜索提供商...", "filterByStatus": "按状态筛选", "allStatuses": "所有状态", "enable": "启用", "disable": "禁用", "confirmDeleteMessage": "您确定要删除AI提供商{{name}}吗？"}, "apiKeys": {"title": "API密钥管理", "description": "描述", "id": "ID", "apiKey": "API密钥", "scope": "访问范围", "environment": "环境", "expiredAt": "过期日期", "status": "状态", "actions": "操作", "active": "活跃", "inactive": "不活跃", "addNew": "创建新API密钥", "createNew": "创建新API密钥", "list": "API密钥列表", "descriptionPlaceholder": "输入API密钥的描述", "selectDate": "选择过期日期", "noData": "没有可用数据", "searchPlaceholder": "按描述搜索...", "filterByStatus": "按状态筛选", "allStatuses": "所有状态", "enable": "启用", "disable": "禁用", "confirmDeleteMessage": "您确定要删除此API密钥吗？", "createSuccess": "已创建描述为{{description}}的新API密钥", "deleteSuccess": "已删除API密钥：{{apiKey}}", "toggleSuccess": "已{{action}}API密钥：{{apiKey}}"}, "externalAgents": {"title": "外部代理管理", "description": "通过MCP、REST API、WebSocket与外部代理集成", "overview": "概览", "protocolDistribution": "协议分布", "quickActions": "快速操作", "recentActivity": "最近活动", "gettingStarted": "开始使用", "gettingStartedDescription": "尚未配置任何外部代理。通过创建您的第一个代理开始使用。", "createDescription": "创建具有自定义协议配置的新外部代理", "manageDescription": "管理所有现有外部代理及其设置", "protocolsDescription": "查看和配置支持的协议", "analyticsDescription": "查看性能分析和使用统计", "noRecentActivity": "暂无最近活动", "activityWillAppear": "外部代理活动将在此处显示", "step1": "选择集成协议（MCP、Google Agent、REST API等）", "step2": "配置端点和身份验证信息", "step3": "测试连接并开始使用"}}}