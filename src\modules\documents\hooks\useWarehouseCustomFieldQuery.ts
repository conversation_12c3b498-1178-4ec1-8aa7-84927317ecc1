import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { WarehouseCustomFieldService } from '../services/warehouse-custom-field.service';
import {
  CreateWarehouseCustomFieldDto,
  UpdateWarehouseCustomFieldDto,
} from '../types/warehouse.types';

// Định nghĩa các query key
export const WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS = {
  all: ['warehouseCustomFields'] as const,
  lists: (warehouseId: number) => [...WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.all, 'list', warehouseId] as const,
  details: (warehouseId: number) => [...WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.all, 'detail', warehouseId] as const,
  detail: (warehouseId: number, fieldId: number) => [...WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.details(warehouseId), fieldId] as const,
};

/**
 * Hook để lấy danh sách trường tùy chỉnh của kho
 * @param warehouseId ID của kho
 * @returns Query object
 */
export const useWarehouseCustomFields = (warehouseId: number) => {
  return useQuery({
    queryKey: WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.lists(warehouseId),
    queryFn: () => WarehouseCustomFieldService.getCustomFields(warehouseId),
    enabled: !!warehouseId,
  });
};

/**
 * Hook để lấy chi tiết trường tùy chỉnh của kho theo ID
 * @param warehouseId ID của kho
 * @param fieldId ID của trường tùy chỉnh
 * @returns Query object
 */
export const useWarehouseCustomField = (warehouseId: number, fieldId: number) => {
  return useQuery({
    queryKey: WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.detail(warehouseId, fieldId),
    queryFn: () => WarehouseCustomFieldService.getCustomFieldById(warehouseId, fieldId),
    enabled: !!warehouseId && !!fieldId,
  });
};

/**
 * Hook để thêm trường tùy chỉnh cho kho
 * @returns Mutation object
 */
export const useAddWarehouseCustomField = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ warehouseId, data }: { warehouseId: number; data: CreateWarehouseCustomFieldDto }) => 
      WarehouseCustomFieldService.addCustomField(warehouseId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.lists(variables.warehouseId) });
    },
  });
};

/**
 * Hook để cập nhật trường tùy chỉnh của kho
 * @returns Mutation object
 */
export const useUpdateWarehouseCustomField = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ warehouseId, fieldId, data }: { warehouseId: number; fieldId: number; data: UpdateWarehouseCustomFieldDto }) => 
      WarehouseCustomFieldService.updateCustomField(warehouseId, fieldId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.detail(variables.warehouseId, variables.fieldId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.lists(variables.warehouseId) 
      });
    },
  });
};

/**
 * Hook để xóa trường tùy chỉnh của kho
 * @returns Mutation object
 */
export const useDeleteWarehouseCustomField = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ warehouseId, fieldId }: { warehouseId: number; fieldId: number }) => 
      WarehouseCustomFieldService.deleteCustomField(warehouseId, fieldId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: WAREHOUSE_CUSTOM_FIELD_QUERY_KEYS.lists(variables.warehouseId) 
      });
    },
  });
};
