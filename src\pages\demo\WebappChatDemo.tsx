/**
 * Demo page cho Webapp Chat với WebSocket
 */

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button } from '@/shared/components/common';
import { ChatPanel } from '@/shared/components/layout/chat-panel';
import { WebappChatConfig } from '@/shared/services/webapp-chat-websocket.service';

const WebappChatDemo: React.FC = () => {
  const { t } = useTranslation(['common', 'chat']);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [config, setConfig] = useState<WebappChatConfig>({
    url: import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:3001',
    namespace: 'webapp-chat',
    auth: {
      token: localStorage.getItem('access_token') || undefined,
    },
    autoConnect: true,
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    timeout: 20000,
  });

  const handleOpenChat = () => {
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
  };

  const handleKeywordDetected = (keyword: string) => {
    console.log('Keyword detected:', keyword);
  };

  const handleConfigChange = (field: keyof WebappChatConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="w-full bg-background text-foreground min-h-screen">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Typography variant="h1" className="mb-4">
            Webapp Chat WebSocket Demo
          </Typography>
          <Typography variant="body1" className="text-gray-600 dark:text-gray-400">
            Demo trang để test kết nối WebSocket với backend webapp chat
          </Typography>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Configuration Panel */}
          <div className="lg:col-span-1">
            <Card className="p-6">
              <Typography variant="h3" className="mb-4">
                Cấu hình WebSocket
              </Typography>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    WebSocket URL
                  </label>
                  <input
                    type="text"
                    value={config.url}
                    onChange={(e) => handleConfigChange('url', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
                    placeholder="ws://localhost:3001"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Namespace
                  </label>
                  <input
                    type="text"
                    value={config.namespace || ''}
                    onChange={(e) => handleConfigChange('namespace', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
                    placeholder="webapp-chat"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Auth Token
                  </label>
                  <input
                    type="text"
                    value={config.auth?.token || ''}
                    onChange={(e) => handleConfigChange('auth', { ...config.auth, token: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
                    placeholder="JWT Token"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="autoConnect"
                    checked={config.autoConnect || false}
                    onChange={(e) => handleConfigChange('autoConnect', e.target.checked)}
                    className="rounded"
                  />
                  <label htmlFor="autoConnect" className="text-sm">
                    Auto Connect
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="reconnection"
                    checked={config.reconnection || false}
                    onChange={(e) => handleConfigChange('reconnection', e.target.checked)}
                    className="rounded"
                  />
                  <label htmlFor="reconnection" className="text-sm">
                    Auto Reconnection
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Reconnection Attempts
                  </label>
                  <input
                    type="number"
                    value={config.reconnectionAttempts || 5}
                    onChange={(e) => handleConfigChange('reconnectionAttempts', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
                    min="1"
                    max="10"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Timeout (ms)
                  </label>
                  <input
                    type="number"
                    value={config.timeout || 20000}
                    onChange={(e) => handleConfigChange('timeout', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
                    min="5000"
                    max="60000"
                    step="1000"
                  />
                </div>
              </div>

              <div className="mt-6">
                <Button
                  onClick={handleOpenChat}
                  variant="primary"
                  className="w-full"
                  disabled={isChatOpen}
                >
                  {isChatOpen ? 'Chat đang mở' : 'Mở Chat'}
                </Button>
              </div>
            </Card>

            {/* Info Panel */}
            <Card className="p-6 mt-6">
              <Typography variant="h3" className="mb-4">
                Thông tin
              </Typography>
              
              <div className="space-y-2 text-sm">
                <div>
                  <strong>Backend URL:</strong> {config.url}/{config.namespace}
                </div>
                <div>
                  <strong>Events:</strong> webapp_chat:*
                </div>
                <div>
                  <strong>Auth:</strong> {config.auth?.token ? 'Có token' : 'Không có token'}
                </div>
              </div>

              <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-md">
                <Typography variant="body2" className="text-yellow-700 dark:text-yellow-300">
                  <strong>Lưu ý:</strong> Đảm bảo backend đang chạy và có thể kết nối được.
                </Typography>
              </div>
            </Card>
          </div>

          {/* Chat Panel */}
          <div className="lg:col-span-2">
            {isChatOpen ? (
              <Card className="h-[600px] overflow-hidden">
                <ChatPanel
                  onClose={handleCloseChat}
                  onKeywordDetected={handleKeywordDetected}
                  websocketConfig={config}
                  mode="websocket"
                />
              </Card>
            ) : (
              <Card className="p-8 text-center h-[600px] flex items-center justify-center">
                <div>
                  <Typography variant="h3" className="mb-4 text-gray-500 dark:text-gray-400">
                    Chat chưa được mở
                  </Typography>
                  <Typography variant="body1" className="text-gray-400 dark:text-gray-500 mb-6">
                    Nhấn "Mở Chat" để bắt đầu test WebSocket connection
                  </Typography>
                  <Button onClick={handleOpenChat} variant="primary">
                    Mở Chat
                  </Button>
                </div>
              </Card>
            )}
          </div>
        </div>

        {/* Instructions */}
        <Card className="p-6 mt-8">
          <Typography variant="h3" className="mb-4">
            Hướng dẫn sử dụng
          </Typography>
          
          <div className="space-y-4">
            <div>
              <Typography variant="h4" className="mb-2">1. Cấu hình kết nối</Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                Điều chỉnh URL WebSocket, namespace, và auth token phù hợp với backend của bạn.
              </Typography>
            </div>

            <div>
              <Typography variant="h4" className="mb-2">2. Mở chat</Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                Nhấn "Mở Chat" để khởi tạo kết nối WebSocket và bắt đầu chat.
              </Typography>
            </div>

            <div>
              <Typography variant="h4" className="mb-2">3. Test các tính năng</Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                - Gửi tin nhắn và nhận phản hồi từ AI<br/>
                - Test streaming response<br/>
                - Test typing indicators<br/>
                - Test reconnection khi mất kết nối
              </Typography>
            </div>

            <div>
              <Typography variant="h4" className="mb-2">4. Debug</Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                Mở Developer Console để xem logs WebSocket và debug các sự kiện.
              </Typography>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default WebappChatDemo;
