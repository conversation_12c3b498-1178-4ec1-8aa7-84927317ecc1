import React from 'react';
import { CalendarCellProps } from './types';
import { isSameDayFn } from './utils';
import { useTheme } from '@/shared/contexts/theme';
import { useCalendarResponsive } from './hooks';

/**
 * Component hiển thị một ô ngày trong calendar
 *
 * Sử dụng React.memo để tránh re-render không cần thiết khi các props không thay đổi.
 * Điều này giúp cải thiện hiệu suất đáng kể khi calendar có nhiều ngày.
 */
const CalendarCell: React.FC<CalendarCellProps> = React.memo(
  ({
    date,
    selectedDate,
    onSelectDate,
    disabled = false,
    isToday = false,
    isCurrentMonth = true,
    className = '',
    rangeMode = false,
    startDate,
    endDate,
    inRange = false,
    focusedDate,
  }) => {
    // Hooks
    useTheme();
    const responsive = useCalendarResponsive();

    // Kiểm tra ngày được chọn
    const isSelected = isSameDayFn(date, selectedDate);

    // Kiểm tra ngày bắt đầu và kết thúc range
    const isStartDate = rangeMode && isSameDayFn(date, startDate);
    const isEndDate = rangeMode && isSameDayFn(date, endDate);

    // Kiểm tra ngày đang được focus
    const isFocused = isSameDayFn(date, focusedDate);

    // Xử lý click vào ngày
    const handleClick = () => {
      if (disabled) return;
      onSelectDate?.(date);
    };

    // Responsive cell size
    const cellSize = responsive.cellSize;
    const fontSize = responsive.fontSize;

    // Base classes with responsive sizing
    const baseClasses = `
      flex items-center justify-center rounded-full transition-colors duration-200
      ${responsive.touchOptimized ? 'touch-manipulation' : ''}
      ${fontSize === 'sm' ? 'text-xs' : fontSize === 'lg' ? 'text-base' : 'text-sm'}
    `.trim();

    // Current month classes
    const currentMonthClasses = isCurrentMonth
      ? 'text-gray-900 dark:text-gray-100'
      : 'text-gray-400 dark:text-gray-500';

    // Today classes
    const todayClasses = isToday ? 'border border-primary dark:border-primary-light' : '';

    // Selected classes với hover effect riêng
    const selectedClasses =
      isSelected || isStartDate || isEndDate
        ? 'bg-primary text-white dark:bg-primary-light dark:text-dark font-medium hover:bg-primary-600 dark:hover:bg-primary-400'
        : '';

    // Disabled classes - chỉ áp dụng hover khi không được chọn
    const disabledClasses = disabled
      ? 'opacity-50 cursor-not-allowed'
      : isSelected || isStartDate || isEndDate
        ? 'cursor-pointer'
        : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800';

    // Range classes
    const rangeClasses =
      rangeMode && inRange && !isStartDate && !isEndDate
        ? 'bg-primary/10 dark:bg-primary-light/10 rounded-none'
        : '';

    // Range edge classes
    const rangeEdgeClasses =
      rangeMode && (isStartDate || isEndDate)
        ? isStartDate && endDate
          ? 'rounded-r-none'
          : isEndDate && startDate
            ? 'rounded-l-none'
            : ''
        : '';

    // Focus classes
    const focusClasses =
      isFocused && !isSelected && !isStartDate && !isEndDate
        ? 'ring-2 ring-primary dark:ring-primary-light'
        : '';

    // Combine all classes
    const cellClasses = [
      baseClasses,
      currentMonthClasses,
      todayClasses,
      selectedClasses,
      disabledClasses,
      rangeClasses,
      rangeEdgeClasses,
      focusClasses,
      className,
    ].join(' ');

    return (
      <div
        className={cellClasses}
        onClick={handleClick}
        role="gridcell"
        aria-selected={isSelected || isStartDate || isEndDate}
        aria-disabled={disabled}
        aria-current={isToday ? 'date' : undefined}
        aria-label={`${date.getDate()}, ${date.toLocaleDateString()}`}
        tabIndex={disabled ? -1 : 0}
        data-date={date.toISOString().split('T')[0]} // For keyboard navigation
        style={{
          width: cellSize,
          height: cellSize,
          minWidth: responsive.touchOptimized ? responsive.minTouchTarget : cellSize,
          minHeight: responsive.touchOptimized ? responsive.minTouchTarget : cellSize,
        }}
        onKeyDown={e => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleClick();
          }
        }}
      >
        {date.getDate()}
      </div>
    );
  }
);

export default CalendarCell;
