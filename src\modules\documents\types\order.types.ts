/**
 * <PERSON><PERSON><PERSON> nghĩ<PERSON> cá<PERSON> types cho Order trong module business
 */

export * from '../services/order.service';

/**
 * Interface cho địa chỉ
 */
export interface AddressDto {
  province: string;
  district: string;
  ward: string;
  address: string;
  provinceId?: number;
  districtId?: number;
  wardCode?: string;
}

/**
 * Interface cho thông tin khách hàng trong đơn hàng
 */
export interface OrderCustomerDto {
  id?: number;
  name: string;
  email: string;
  phone: string;
  address: AddressDto;
}

/**
 * Interface cho sản phẩm trong đơn hàng (enhanced)
 */
export interface OrderItemDto {
  productId: number;
  variantId?: number;
  productName: string;
  variantName?: string;
  productType?: import('./product.types').ProductTypeEnum;
  quantity: number;
  price: number;
  totalPrice: number;
  shipmentConfig?: {
    lengthCm?: number;
    widthCm?: number;
    heightCm?: number;
    weightGram?: number;
  };
  image?: string;
  sku?: string;
}

/**
 * Enum cho phương thức vận chuyển
 */
export enum ShippingMethod {
  GHN = 'GHN',
  GHTK = 'GHTK',
  SELF = 'SELF',
}

/**
 * Enum cho phương thức giao hàng sản phẩm số
 */
export enum DigitalDeliveryMethod {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  ZALO = 'ZALO',
  TELEGRAM = 'TELEGRAM',
  WHATSAPP = 'WHATSAPP',
  DOWNLOAD_LINK = 'DOWNLOAD_LINK',
}

/**
 * Interface cho thông tin giao hàng sản phẩm số
 */
export interface DigitalDeliveryDto {
  method: DigitalDeliveryMethod;
  recipient: string; // email, phone number, username, etc.
  message?: string;
  scheduledDelivery?: string;
  deliveryStatus?: 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED';
}

/**
 * Interface cho thông tin vận chuyển
 */
export interface ShippingDto {
  method: ShippingMethod;
  serviceId?: number;
  serviceName?: string;
  fromAddress: AddressDto;
  toAddress: AddressDto;
  fee: number;
  estimatedDelivery?: string;
  note?: string;
  trackingCode?: string;
}

/**
 * Enum cho trạng thái vận chuyển
 */
export enum ShippingStatus {
  PENDING = 'PENDING',
  PICKED_UP = 'PICKED_UP',
  IN_TRANSIT = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  FAILED = 'FAILED',
  RETURNED = 'RETURNED',
}

/**
 * Interface cho tạo đơn hàng mới (enhanced)
 */
export interface CreateEnhancedOrderDto {
  customer: OrderCustomerDto;
  items: OrderItemDto[];
  shipping?: ShippingDto;
  digitalDelivery?: DigitalDeliveryDto;
  payment: {
    method: string;
    status: string;
    codAmount?: number;
  };
  notes?: string;
  tags?: string[];
}

/**
 * Interface cho đơn hàng (enhanced)
 */
export interface EnhancedOrderDto {
  id: number;
  orderNumber: string;
  customer: OrderCustomerDto;
  items: OrderItemDto[];
  subtotal: number;
  shippingFee: number;
  tax?: number;
  totalAmount: number;
  status: string;
  paymentMethod: string;
  paymentStatus: string;
  shipping: ShippingDto;
  shippingStatus: ShippingStatus;
  notes?: string;
  tags?: string[];
  createdAt: number;
  updatedAt: number;
}

/**
 * Interface cho tham số lọc đơn hàng (enhanced)
 */
export interface EnhancedOrderQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  paymentStatus?: string;
  shippingStatus?: ShippingStatus;
  shippingMethod?: ShippingMethod;
  customerId?: number;
  fromDate?: string;
  toDate?: string;
  minAmount?: number;
  maxAmount?: number;
  tags?: string[];
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface cho thống kê đơn hàng
 */
export interface OrderStatsDto {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  pendingOrders: number;
  processingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  totalShippingFee: number;
}
