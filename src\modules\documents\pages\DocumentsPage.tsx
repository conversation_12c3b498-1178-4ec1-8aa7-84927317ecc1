import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Card, Table, IconCard, Tooltip } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import {
  useVirtualWarehouses,
  useDeleteVirtualWarehouse,
  useDeleteMultipleVirtualWarehouses,
} from '../hooks/useVirtualWarehouseQuery';
import {
  VirtualWarehouseQueryParams,
  VirtualWarehouseStatus,
  VirtualWarehouseListItemDto,
} from '../types/virtual-warehouse.types';
import CreateVirtualWarehouseForm, {
  CreateVirtualWarehouseFormValues,
} from '../components/forms/CreateVirtualWarehouseForm';
import EditVirtualWarehouseForm, {
  EditVirtualWarehouseFormValues,
} from '../components/forms/EditVirtualWarehouseForm';
import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';

/**
 * Trang quản lý kho ảo
 */
const DocumentsPage: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);
  const navigate = useNavigate();

  // State cho form và modal
  const { isVisible: isFormVisible, showForm, hideForm } = useSlideForm();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);
  const [selectedWarehouseId, setSelectedWarehouseId] = useState<number | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // Xử lý xem chi tiết
  const handleView = useCallback(
    (id: number) => {
      navigate(`/business/warehouse/virtual/${id}`);
    },
    [navigate]
  );

  // Xử lý chỉnh sửa
  const handleEdit = useCallback(
    (id: number) => {
      setSelectedWarehouseId(id);
      showForm();
    },
    [setSelectedWarehouseId, showForm]
  );

  // Xử lý hiển thị modal xác nhận xóa
  const handleShowDeleteConfirm = useCallback(
    (id: number) => {
      setSelectedWarehouseId(id);
      setIsDeleteModalOpen(true);
    },
    [setSelectedWarehouseId]
  );

  // Xử lý xóa nhiều
  const handleBulkDelete = useCallback(() => {
    if (selectedRowKeys.length > 0) {
      setIsBulkDeleteModalOpen(true);
    }
  }, [selectedRowKeys]);

  // Định nghĩa cột cho bảng
  const columns: TableColumn<VirtualWarehouseListItemDto>[] = useMemo(
    () => [
      {
        key: 'name',
        title: t('business:virtualWarehouse.name'),
        dataIndex: 'name',
        sortable: true,
        render: (value: unknown, record: VirtualWarehouseListItemDto) => (
          <button
            onClick={() => handleView(record.warehouseId)}
            className="text-blue-600 hover:text-blue-800 hover:underline font-medium"
          >
            {value as string}
          </button>
        ),
        align: 'left',
      },
      {
        key: 'description',
        title: t('business:virtualWarehouse.description'),
        dataIndex: 'description',
        sortable: true,
        render: (value: unknown) => {
          const desc = value as string;
          return desc ? (
            <span className="text-gray-600 truncate max-w-xs block" title={desc}>
              {desc}
            </span>
          ) : (
            <span className="text-gray-400">-</span>
          );
        },
      },
      {
        key: 'associatedSystem',
        title: t('business:virtualWarehouse.associatedSystem'),
        dataIndex: 'associatedSystem',
        sortable: true,
        render: (value: unknown) => {
          const system = value as string;
          return system ? (
            <span className="text-gray-600">{system}</span>
          ) : (
            <span className="text-gray-400">-</span>
          );
        },
      },
      {
        key: 'status',
        title: t('business:virtualWarehouse.status'),
        dataIndex: 'status',
        sortable: true,
        render: (value: unknown) => {
          const status = value as VirtualWarehouseStatus;
          const statusConfig = {
            [VirtualWarehouseStatus.ACTIVE]: {
              label: t('common:status.active'),
              className: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
            },
            [VirtualWarehouseStatus.INACTIVE]: {
              label: t('common:status.inactive'),
              className: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
            },
          };

          const config = statusConfig[status];
          return (
            <span
              className={`px-2 py-1 rounded-full text-xs font-medium ${config?.className || ''}`}
            >
              {config?.label || status}
            </span>
          );
        },
      },
      {
        key: 'actions',
        title: t('common:actions'),
        render: (_, record) => {
          return (
            <div className="flex space-x-2">
              <Tooltip content={t('common:view')}>
                <IconCard
                  icon="eye"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleView(record.warehouseId)}
                />
              </Tooltip>
              <Tooltip content={t('common:edit')}>
                <IconCard
                  icon="edit"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleEdit(record.warehouseId)}
                />
              </Tooltip>
              <Tooltip content={t('common:delete')}>
                <IconCard
                  icon="trash"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleShowDeleteConfirm(record.warehouseId)}
                />
              </Tooltip>
            </div>
          );
        },
      },
    ],
    [t, handleView, handleEdit, handleShowDeleteConfirm]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): VirtualWarehouseQueryParams => {
    const queryParams: VirtualWarehouseQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue && params.filterValue !== 'all') {
      queryParams.status = params.filterValue as VirtualWarehouseStatus;
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<VirtualWarehouseListItemDto, VirtualWarehouseQueryParams>({
      columns,
      filterOptions: [
        { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
        {
          id: 'active',
          label: t('common:status.active'),
          icon: 'check',
          value: VirtualWarehouseStatus.ACTIVE,
        },
        {
          id: 'inactive',
          label: t('common:status.inactive'),
          icon: 'x',
          value: VirtualWarehouseStatus.INACTIVE,
        },
      ],
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách kho ảo
  const { data: warehousesData, isLoading } = useVirtualWarehouses(dataTable.queryParams);

  // Xử lý dữ liệu trước khi hiển thị
  const processedData = useMemo(() => {
    if (!warehousesData?.items) return [];
    return warehousesData.items;
  }, [warehousesData]);

  // Mutations
  const { mutateAsync: deleteWarehouse } = useDeleteVirtualWarehouse();
  const { mutateAsync: deleteMultipleWarehouses } = useDeleteMultipleVirtualWarehouses();

  // Wrapper cho hàm handleSortChange để đảm bảo kiểu dữ liệu đúng
  const handleSortChangeWrapper = useCallback(
    (column: string | null, order: SortOrder | null) => {
      if (column === null || order === null) {
        dataTable.tableData.handleSortChange(null, null);
        return;
      }
      dataTable.tableData.handleSortChange(column, order as SortOrder);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [VirtualWarehouseStatus.ACTIVE]: t('common:status.active'),
      [VirtualWarehouseStatus.INACTIVE]: t('common:status.inactive'),
    },
    t,
  });

  // Xử lý thêm mới
  const handleAdd = () => {
    setSelectedWarehouseId(null);
    showForm();
  };

  // Xử lý hủy xóa
  const handleCancelDelete = () => {
    setIsDeleteModalOpen(false);
    setSelectedWarehouseId(null);
  };

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = () => {
    setIsBulkDeleteModalOpen(false);
  };

  // Xử lý xác nhận xóa
  const handleConfirmDelete = async () => {
    if (selectedWarehouseId) {
      try {
        await deleteWarehouse(selectedWarehouseId);
        setIsDeleteModalOpen(false);
        setSelectedWarehouseId(null);
      } catch (error) {
        console.error('Error deleting virtual warehouse:', error);
      }
    }
  };

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = async () => {
    if (selectedRowKeys.length > 0) {
      try {
        const ids = selectedRowKeys.map(key => Number(key));
        await deleteMultipleWarehouses(ids);
        setIsBulkDeleteModalOpen(false);
        setSelectedRowKeys([]);
      } catch (error) {
        console.error('Error deleting multiple virtual warehouses:', error);
      }
    }
  };

  // Xử lý submit form tạo mới
  const handleCreateSubmit = (values: CreateVirtualWarehouseFormValues) => {
    console.log('Create form values:', values);
    hideForm();
  };

  // Xử lý submit form chỉnh sửa
  const handleEditSubmit = (values: EditVirtualWarehouseFormValues) => {
    console.log('Edit form values:', values);
    hideForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    setSelectedWarehouseId(null);
    hideForm();
  };

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete'),
            variant: 'primary',
            onClick: handleBulkDelete,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Hiển thị ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form container với animation */}
      <SlideInForm isVisible={isFormVisible}>
        {selectedWarehouseId ? (
          <EditVirtualWarehouseForm
            warehouseId={selectedWarehouseId}
            onSubmit={handleEditSubmit}
            onCancel={handleCancel}
          />
        ) : (
          <CreateVirtualWarehouseForm onSubmit={handleCreateSubmit} onCancel={handleCancel} />
        )}
      </SlideInForm>

      {/* Modal xác nhận xóa đơn */}
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete')}
        message={t('business:virtualWarehouse.confirmDeleteMessage')}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={isBulkDeleteModalOpen}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmBulkDelete')}
        message={t('business:virtualWarehouse.confirmBulkDeleteMessage', {
          count: selectedRowKeys.length,
        })}
      />

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={processedData}
          rowKey="warehouseId"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: warehousesData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: warehousesData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default DocumentsPage;
