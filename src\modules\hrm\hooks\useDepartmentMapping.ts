import { useEffect, useState } from 'react';
import { DepartmentService } from '../services/department.service';
import type { DepartmentDto } from '../types/department.types';

/**
 * Hook để tạo mapping từ departmentId sang department name
 * Sử dụng để hiển thị tên phòng ban thay vì ID trong danh sách nhân viên
 */
export const useDepartmentMapping = () => {
  const [departmentMap, setDepartmentMap] = useState<Map<number, DepartmentDto>>(new Map());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadDepartments = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Load tất cả departments (có thể cần tăng limit nếu có nhiều phòng ban)
        const response = await DepartmentService.getDepartments({
          page: 1,
          limit: 1000, // Load nhiều để đảm bảo lấy hết
        });

        const map = new Map<number, DepartmentDto>();
        response.result.items.forEach(department => {
          map.set(department.id, department);
        });

        setDepartmentMap(map);
      } catch (err) {
        console.error('Error loading departments for mapping:', err);
        setError('Failed to load departments');
      } finally {
        setLoading(false);
      }
    };

    loadDepartments();
  }, []);

  /**
   * Lấy tên phòng ban từ ID
   * @param departmentId ID phòng ban
   * @returns Tên phòng ban hoặc fallback string
   */
  const getDepartmentName = (departmentId: number | null): string => {
    if (!departmentId) return '-';
    
    const department = departmentMap.get(departmentId);
    return department?.name || `Phòng ban ${departmentId}`;
  };

  /**
   * Lấy thông tin đầy đủ phòng ban từ ID
   * @param departmentId ID phòng ban
   * @returns Department object hoặc null
   */
  const getDepartment = (departmentId: number | null): DepartmentDto | null => {
    if (!departmentId) return null;
    return departmentMap.get(departmentId) || null;
  };

  return {
    departmentMap,
    loading,
    error,
    getDepartmentName,
    getDepartment,
  };
};
