import { TFunction } from 'i18next';
import { z } from 'zod';

/**
 * Schema cho form tạo tài khoản người dùng cho nhân viên
 * @param t Hàm dịch
 * @param autoGeneratePassword Có tự động tạo mật khẩu không
 * @returns Schema Zod
 */
export const createUserForEmployeeSchema = (t: TFunction, autoGeneratePassword = false, hasEmployeeId = false) => {
  // Schema cơ bản chỉ có email
  let baseSchema = z.object({
    email: z
      .string()
      .min(1, t('hrm:employee.form.validation.emailRequired', 'Email là bắt buộc'))
      .email(t('hrm:employee.form.validation.emailInvalid', 'Email không hợp lệ')),
  });

  // Chỉ validate employeeId nếu không có employeeId từ props
  if (!hasEmployeeId) {
    baseSchema = baseSchema.extend({
      employeeId: z
        .number()
        .min(1, t('hrm:employee.form.validation.employeeRequired', '<PERSON><PERSON> lòng chọn nhân viên')),
    });
  }

  // Nếu không auto generate password, thêm các trường mật khẩu
  if (!autoGeneratePassword) {
    return baseSchema.extend({
      password: z
        .string()
        .min(8, t('hrm:employee.form.validation.passwordMinLength', 'Mật khẩu phải có ít nhất 8 ký tự'))
        .max(50, t('hrm:employee.form.validation.passwordMaxLength', 'Mật khẩu không được vượt quá 50 ký tự')),
      confirmPassword: z
        .string()
        .min(1, t('hrm:employee.form.validation.confirmPasswordRequired', 'Xác nhận mật khẩu là bắt buộc')),
    }).refine((data) => data.password === data.confirmPassword, {
      message: t('hrm:employee.form.validation.passwordsMatch', 'Mật khẩu và xác nhận mật khẩu không khớp'),
      path: ['confirmPassword'],
    });
  }

  return baseSchema;
};

/**
 * Kiểu dữ liệu cho form tạo tài khoản người dùng
 */
export type CreateUserForEmployeeFormValues = z.infer<ReturnType<typeof createUserForEmployeeSchema>>;
