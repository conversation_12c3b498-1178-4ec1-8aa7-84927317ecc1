import { useCallback, useEffect, useState } from 'react';

/**
 * Interface cho bulk delete mutation
 */
interface BulkDeleteMutation {
  mutateAsync: (ids: number[]) => Promise<any>;
  isPending: boolean;
}

/**
 * Interface cho data items
 */
interface DataItem {
  id: number;
  [key: string]: any;
}

/**
 * Interface cho data response
 */
interface DataResponse {
  items: DataItem[];
  [key: string]: any;
}

/**
 * Props cho useBulkSelection hook
 */
interface UseBulkSelectionProps {
  /**
   * Data response từ API
   */
  data?: DataResponse;
  
  /**
   * Bulk delete mutation từ React Query
   */
  bulkDeleteMutation: BulkDeleteMutation;
  
  /**
   * Callback được gọi sau khi xóa thành công (optional)
   */
  onDeleteSuccess?: () => void;
  
  /**
   * Callback được gọi khi có lỗi (optional)
   */
  onDeleteError?: (error: any) => void;
}

/**
 * Return type cho useBulkSelection hook
 */
interface UseBulkSelectionReturn {
  /**
   * Danh sách keys được chọn
   */
  selectedRowKeys: React.Key[];
  
  /**
   * Function để set selected keys
   */
  setSelectedRowKeys: (keys: React.Key[]) => void;
  
  /**
   * State hiển thị modal confirm
   */
  showBulkDeleteConfirm: boolean;
  
  /**
   * Số lượng items sẽ được xóa (stable count)
   */
  deleteCount: number;
  
  /**
   * Function hiển thị modal confirm
   */
  handleShowBulkDeleteConfirm: () => void;
  
  /**
   * Function hủy bulk delete
   */
  handleCancelBulkDelete: () => void;
  
  /**
   * Function xác nhận bulk delete
   */
  handleConfirmBulkDelete: () => Promise<void>;
  
  /**
   * State loading của bulk delete
   */
  isDeleting: boolean;
}

/**
 * Custom hook để quản lý bulk selection và bulk delete logic
 * Giải quyết vấn đề selectedRowKeys accumulation và timing issues
 */
export const useBulkSelection = ({
  data,
  bulkDeleteMutation,
  onDeleteSuccess,
  onDeleteError,
}: UseBulkSelectionProps): UseBulkSelectionReturn => {
  // States
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [deleteCount, setDeleteCount] = useState(0);

  // Cleanup selectedRowKeys khi data thay đổi (sau khi xóa thành công)
  useEffect(() => {
    if (data?.items) {
      // Lọc ra những keys không còn tồn tại trong data mới
      const existingIds = data.items.map(item => item.id);
      const validSelectedKeys = selectedRowKeys.filter(key => 
        existingIds.includes(Number(key))
      );
      
      // Nếu có keys không hợp lệ, cập nhật selectedRowKeys
      if (validSelectedKeys.length !== selectedRowKeys.length) {
        setSelectedRowKeys(validSelectedKeys);
      }
    }
  }, [data?.items, selectedRowKeys]);

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      return;
    }
    // Lưu số lượng items được chọn tại thời điểm hiển thị modal
    setDeleteCount(selectedRowKeys.length);
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
    setDeleteCount(0);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    try {
      const ids = selectedRowKeys.map(key => Number(key));
      
      // Reset selectedRowKeys và modal state ngay lập tức để tránh timing issue
      setSelectedRowKeys([]);
      setShowBulkDeleteConfirm(false);
      setDeleteCount(0);
      
      // Gọi API xóa
      await bulkDeleteMutation.mutateAsync(ids);
      
      // Callback success nếu có
      onDeleteSuccess?.();
    } catch (error) {
      console.error('Error bulk deleting items:', error);
      
      // Đóng modal khi có lỗi
      setShowBulkDeleteConfirm(false);
      setDeleteCount(0);
      
      // Callback error nếu có
      onDeleteError?.(error);
    }
  }, [selectedRowKeys, bulkDeleteMutation, onDeleteSuccess, onDeleteError]);

  return {
    selectedRowKeys,
    setSelectedRowKeys,
    showBulkDeleteConfirm,
    deleteCount,
    handleShowBulkDeleteConfirm,
    handleCancelBulkDelete,
    handleConfirmBulkDelete,
    isDeleting: bulkDeleteMutation.isPending,
  };
};
