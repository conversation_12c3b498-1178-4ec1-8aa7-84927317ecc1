import { ReactNode } from 'react';
import {
  Task,
  CreateApiCallTaskParams,
  CreateFileUploadTaskParams,
  CreateCustomTaskParams,
} from '@/shared/types/task-queue.types';
import { UseTaskQueueOptions } from '@/shared/hooks/common/useTaskQueue';

/**
 * Context value cho Task Queue
 */
export interface TaskQueueContextValue {
  /**
   * Danh sách task
   */
  tasks: Task[];

  /**
   * Trạng thái queue
   */
  isRunning: boolean;

  /**
   * Số lượng task đang chạy
   */
  runningCount: number;

  /**
   * Thêm task API call vào queue
   */
  addApiCallTask: (params: CreateApiCallTaskParams) => string;

  /**
   * Thêm task upload file vào queue
   */
  addFileUploadTask: (params: CreateFileUploadTaskParams) => string;

  /**
   * Thêm task tùy chỉnh vào queue
   */
  addCustomTask: (params: CreateCustomTaskParams) => string;

  /**
   * Cập nhật task
   */
  updateTask: (id: string, updates: Partial<Task>) => void;

  /**
   * Xóa task
   */
  removeTask: (id: string) => void;

  /**
   * Hủy task
   */
  cancelTask: (id: string) => void;

  /**
   * Thử lại task
   */
  retryTask: (id: string) => void;

  /**
   * Xóa tất cả task đã hoàn thành
   */
  clearCompletedTasks: () => void;

  /**
   * Bắt đầu queue
   */
  startQueue: () => void;

  /**
   * Tạm dừng queue
   */
  pauseQueue: () => void;
}

/**
 * Props cho TaskQueueProvider
 */
export interface TaskQueueProviderProps {
  /**
   * Các tùy chọn cho Task Queue
   */
  options?: UseTaskQueueOptions;

  /**
   * Children
   */
  children: ReactNode;
}
