/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import {
  Card,
  FormGrid,
  Form,
  FormItem,
  Input,
  Button,
  Typography,
  Select,
} from '@/shared/components/common';

export interface IndustryInfoFormProps {
  /**
   * Callback khi submit form
   */
  onSubmit?: (data: IndustryInfoFormData) => void;

  /**
   * Dữ liệu mặc định
   */
  defaultValues?: Partial<IndustryInfoFormData>;

  /**
   * Trạng thái loading
   */
  isLoading?: boolean;
}

export interface IndustryInfoFormData {
  bankName: string;
  accountNumber: string;
  idNumber: string;
  storeName: string;
  accountName: string;
  phoneNumber: string;
  storeAddress: string;
}

/**
 * Component hiển thị form thông tin ngành hàng
 */
const IndustryInfoForm: React.FC<IndustryInfoFormProps> = ({
  onSubmit,
  defaultValues,
  isLoading = false,
}) => {
  const handleSubmit = (data: any) => {
    if (onSubmit) {
      onSubmit(data as IndustryInfoFormData);
    }
  };

  return (
    <Card className="p-6 w-full">
      {/* Tiêu đề */}
      <div className="mb-4">
        <Typography variant="h5" className="text-red-600 mb-2">
          Thông tin ngân hàng
        </Typography>
      </div>

      {/* Form */}
      <Form
        onSubmit={handleSubmit}
        className="w-full"
        defaultValues={{
          bankName: '',
          accountNumber: '',
          idNumber: '',
          storeName: '',
          accountName: '',
          phoneNumber: '',
          storeAddress: '',
          ...defaultValues,
        }}
      >
        {/* Chọn ngân hàng */}
        <div className="mb-4">
          <FormItem name="bankName" label="Chọn ngân hàng" className="w-full">
            <Select
              className="w-full"
              placeholder="Chọn ngân hàng"
              options={[
                { value: 'mb', label: 'Ngân hàng TMCP Quân Đội (MBBank)' },
                { value: 'vcb', label: 'Ngân hàng TMCP Ngoại thương Việt Nam (Vietcombank)' },
                { value: 'bidv', label: 'Ngân hàng TMCP Đầu tư và Phát triển Việt Nam (BIDV)' },
                { value: 'vtb', label: 'Ngân hàng TMCP Công Thương Việt Nam (VietinBank)' },
                { value: 'tcb', label: 'Ngân hàng TMCP Kỹ Thương Việt Nam (Techcombank)' },
                { value: 'acb', label: 'Ngân hàng TMCP Á Châu (ACB)' },
                { value: 'vpb', label: 'Ngân hàng TMCP Việt Nam Thịnh Vượng (VPBank)' },
              ]}
            />
          </FormItem>
        </div>

        <FormGrid columns={2} columnsSm={1} gap="md" className="mb-6 w-full">
          {/* Cột bên trái */}
          <div>
            <FormItem name="accountNumber" label="Số tài khoản" className="w-full">
              <Input placeholder="VD: **********" className="w-full" />
            </FormItem>

            <FormItem name="idNumber" label="Số CMND/CCCD" className="w-full">
              <Input placeholder="VD: **********" className="w-full" />
            </FormItem>

            <FormItem name="storeName" label="Tên điểm bán" className="w-full">
              <Input placeholder="VD: siêu thị abc" className="w-full" />
            </FormItem>
          </div>

          {/* Cột bên phải */}
          <div>
            <FormItem name="accountName" label="Tên tài khoản" className="w-full">
              <Input placeholder="VD: NGUYEN VAN A" className="w-full" />
            </FormItem>

            <FormItem name="phoneNumber" label="Số điện thoại" className="w-full">
              <Input placeholder="VD: **********" className="w-full" />
            </FormItem>

            <FormItem name="storeAddress" label="Địa chỉ điểm bán" className="w-full">
              <Input placeholder="VD: 123 Bắc Từ Liêm - Hà Nội" className="w-full" />
            </FormItem>
          </div>
        </FormGrid>

        {/* Nút hành động */}
        <Button
          type="submit"
          className="w-full bg-gradient-to-r from-red-600 to-red-400 hover:from-red-700 hover:to-red-500 border-none"
          isLoading={isLoading}
        >
          Kết nối
        </Button>
      </Form>
    </Card>
  );
};

export default IndustryInfoForm;
