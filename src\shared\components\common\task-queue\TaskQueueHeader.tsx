/**
 * Component hiển thị header của TaskQueuePanel
 */
import React from 'react';
import { IconButton } from '@/shared/components/common';

/**
 * Props cho TaskQueueHeader
 */
export interface TaskQueueHeaderProps {
  /**
   * S<PERSON> lượng task theo trạng thái
   */
  taskCounts: {
    total: number;
    pending: number;
    running: number;
    completed: number;
    failed: number;
    cancelled: number;
  };

  /**
   * Queue có đang chạy không
   */
  isRunning: boolean;

  /**
   * Panel có đang mở rộng không
   */
  isExpanded: boolean;

  /**
   * Panel có đang siêu thu gọn không
   */
  isSuperCollapsed?: boolean;

  /**
   * Callback khi đóng panel
   */
  onClose?: () => void;

  /**
   * Callback khi mở rộng/thu gọn panel
   */
  onToggleExpand?: () => void;

  /**
   * Callback khi siêu thu gọn panel
   */
  onToggleSuperCollapse?: () => void;

  /**
   * Callback khi xóa tất cả task đã hoàn thành
   */
  onClearCompleted?: () => void;

  /**
   * Callback khi bắt đầu/dừng queue
   */
  onToggleQueue?: () => void;

  /**
   * Cho phép siêu thu gọn panel
   */
  allowSuperCollapse?: boolean;
}

/**
 * Component hiển thị header của TaskQueuePanel
 */
const TaskQueueHeader: React.FC<TaskQueueHeaderProps> = ({
  taskCounts,

  isExpanded,
  isSuperCollapsed = false,
  onClose,
  onToggleExpand,
  onToggleSuperCollapse,

  allowSuperCollapse = true,
}) => {
  return (
    <div
      className={`${isSuperCollapsed ? 'p-2 rounded-lg' : 'p-3 border-b'} bg-card dark:bg-card border-border`}
    >
      <div className="flex items-center justify-between min-w-0">
        <div className="flex items-center min-w-0 flex-1">
          <h2
            className={`text-sm font-medium text-foreground ${isSuperCollapsed ? 'max-w-[120px] truncate' : ''}`}
          >
            {isSuperCollapsed ? 'Tác vụ' : 'Quản lý tác vụ'}
          </h2>
          {taskCounts.total > 0 && (
            <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-primary/10 text-primary rounded-full flex-shrink-0">
              {taskCounts.total}
            </span>
          )}
        </div>

        <div className="flex items-center space-x-1 flex-shrink-0">
          {/* Nút đa chức năng: siêu thu gọn hoặc mở rộng/thu gọn - chỉ hiển thị khi không ở chế độ siêu thu gọn */}
          {!isSuperCollapsed &&
          ((allowSuperCollapse && onToggleSuperCollapse) || onToggleExpand) ? (
            <IconButton
              icon={isExpanded ? 'chevron-down' : 'minus'}
              size="sm"
              variant="ghost"
              onClick={() => {
                // Nếu đang mở rộng, thì thu gọn
                if (isExpanded && onToggleExpand) {
                  onToggleExpand();
                }
                // Nếu đang thu gọn bình thường, thì siêu thu gọn hoặc mở rộng
                else if (!isExpanded && !isSuperCollapsed) {
                  if (allowSuperCollapse && onToggleSuperCollapse) {
                    onToggleSuperCollapse();
                  } else if (onToggleExpand) {
                    onToggleExpand();
                  }
                }
              }}
              title={isExpanded ? 'Thu gọn' : 'Siêu thu gọn'}
            />
          ) : null}

          {/* Nút mở rộng từ chế độ siêu thu gọn - chỉ hiển thị khi ở chế độ siêu thu gọn */}
          {isSuperCollapsed && onToggleSuperCollapse && (
            <IconButton
              icon="plus"
              size="sm"
              variant="ghost"
              onClick={e => {
                e?.preventDefault();
                e?.stopPropagation();
                onToggleSuperCollapse();
              }}
              title="Hiển thị đầy đủ"
              className="flex-shrink-0"
            />
          )}

          {/* Nút đóng - luôn hiển thị và có thể nhấn */}
          {onClose && (
            <IconButton
              icon="x"
              size="sm"
              variant="ghost"
              onClick={e => {
                e?.preventDefault();
                e?.stopPropagation();
                try {
                  onClose();
                } catch (error) {
                  console.error('Error closing panel:', error);
                }
              }}
              title="Đóng panel"
              className="hover:bg-destructive/10 hover:text-destructive relative z-10 flex-shrink-0 ml-1"
            />
          )}
        </div>
      </div>

      {/* Thống kê */}
      {taskCounts.total > 0 && !isSuperCollapsed && (
        <div className="mt-2 flex items-center justify-between text-xs text-muted">
          <div className="flex space-x-2">
            {taskCounts.running > 0 && (
              <span className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-primary mr-1"></span>
                <span>Đang chạy: {taskCounts.running}</span>
              </span>
            )}

            {taskCounts.pending > 0 && (
              <span className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-warning mr-1"></span>
                <span>Đang chờ: {taskCounts.pending}</span>
              </span>
            )}

            {taskCounts.completed > 0 && (
              <span className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-success mr-1"></span>
                <span>Thành công: {taskCounts.completed}</span>
              </span>
            )}

            {taskCounts.failed > 0 && (
              <span className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-destructive mr-1"></span>
                <span>Lỗi: {taskCounts.failed}</span>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskQueueHeader;
