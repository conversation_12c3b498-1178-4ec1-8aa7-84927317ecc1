import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import {
  ObjectiveDto,
  ObjectiveQueryDto,
  CreateObjectiveDto,
  UpdateObjectiveDto,
} from '../types/objective.types';

/**
 * Service cho các API liên quan đến mục tiêu
 */
export const ObjectiveService = {
  /**
   * L<PERSON>y danh sách mục tiêu với phân trang và lọc
   * @param params Tham số truy vấn
   * @returns Danh sách mục tiêu đã phân trang
   */
  async getObjectives(
    params?: ObjectiveQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<ObjectiveDto>>> {
    return apiClient.get<PaginatedResult<ObjectiveDto>>('/api/okrs/objectives', {
      params,
    });
  },

  /**
   * L<PERSON>y chi tiết mục tiêu theo ID
   * @param id ID mục tiêu
   * @returns Chi tiết mục tiêu
   */
  async getObjective(id: number): Promise<ApiResponseDto<ObjectiveDto>> {
    return apiClient.get<ObjectiveDto>(`/api/okrs/objectives/${id}`);
  },

  /**
   * Tạo mới mục tiêu
   * @param data Dữ liệu tạo mục tiêu
   * @returns Mục tiêu đã tạo
   */
  async createObjective(data: CreateObjectiveDto): Promise<ApiResponseDto<ObjectiveDto>> {
    return apiClient.post<ObjectiveDto>('/api/okrs/objectives', data);
  },

  /**
   * Cập nhật mục tiêu
   * @param id ID mục tiêu
   * @param data Dữ liệu cập nhật
   * @returns Mục tiêu đã cập nhật
   */
  async updateObjective(
    id: number,
    data: UpdateObjectiveDto
  ): Promise<ApiResponseDto<ObjectiveDto>> {
    return apiClient.put<ObjectiveDto>(`/api/okrs/objectives/${id}`, data);
  },

  /**
   * Xóa mục tiêu
   * @param id ID mục tiêu
   * @returns Kết quả xóa
   */
  async deleteObjective(id: number): Promise<ApiResponseDto<void>> {
    return apiClient.delete<void>(`/api/okrs/objectives/${id}`);
  },

  /**
   * Xóa nhiều mục tiêu
   * @param data Dữ liệu chứa danh sách ID cần xóa
   * @returns Kết quả xóa
   */
  async bulkDeleteObjectives(data: { ids: number[] }): Promise<ApiResponseDto<void>> {
    return apiClient.delete<void>('/api/okrs/objectives/bulk-delete', { data });
  },
};
