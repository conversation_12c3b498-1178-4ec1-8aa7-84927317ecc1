# Webapp Chat WebSocket - Hướng dẫn Setup và Test

## 🚀 Tổng quan

Đã tích hợp thành công WebSocket cho webapp chat với các tính năng:

- ✅ **Real-time messaging** với AI agent
- ✅ **Streaming response** từ AI
- ✅ **Typing indicators** cho user và AI
- ✅ **Auto-reconnection** khi mất kết nối
- ✅ **JWT Authentication** bảo mật
- ✅ **Error handling** toàn diện
- ✅ **Demo page** để test

## 📁 Cấu trúc Files đã tạo

### Backend (đã có sẵn)
```
backend/chat/
├── gateways/webapp-chat.gateway.ts     # WebSocket Gateway
├── controllers/webapp-chat.controller.ts # REST API Controller
└── services/webapp-chat.service.ts     # Business Logic
```

### Frontend (mới tạo)
```
src/
├── shared/
│   ├── services/
│   │   └── webapp-chat-websocket.service.ts    # WebSocket Service
│   ├── hooks/chat/
│   │   └── useWebappChatWebSocket.ts           # React Hook
│   └── components/layout/chat-panel/
│       └── WebappChatPanel.tsx                 # Chat Component
├── pages/demo/
│   └── WebappChatDemo.tsx                      # Demo Page
└── shared/routers/modules/
    └── componentRoutes.tsx                     # Route config
```

### Documentation
```
docs/
└── webapp-chat-websocket-integration.md       # Technical docs
```

## 🛠️ Setup

### 1. Environment Variables

Thêm vào file `.env`:

```bash
# WebSocket URL
VITE_WEBSOCKET_URL=ws://localhost:3001
```

### 2. Backend Requirements

Đảm bảo backend đang chạy với:
- NestJS WebSocket Gateway tại namespace `webapp-chat`
- Socket.IO server tại port 3001
- JWT authentication enabled

### 3. Frontend Dependencies

Các dependencies cần thiết (đã có sẵn):
```json
{
  "socket.io-client": "^4.x.x",
  "react": "^18.x.x",
  "react-i18next": "^x.x.x"
}
```

## 🧪 Testing

### 1. Truy cập Demo Pages

```bash
# Start frontend
npm run dev

# Demo pages:
# 1. ChatPanel với cả hai mode (demo + websocket)
http://localhost:5173/demo/chat-panel-modes

# 2. WebSocket configuration demo
http://localhost:5173/demo/webapp-chat
```

### 2. Test Scenarios

#### A. Connection Test
1. Mở demo page
2. Cấu hình WebSocket URL: `ws://localhost:3001`
3. Nhấn "Mở Chat"
4. Kiểm tra connection status (màu xanh = connected)

#### B. Messaging Test
1. Gửi tin nhắn: "Hello AI"
2. Kiểm tra user message hiển thị ngay lập tức
3. Kiểm tra AI typing indicator
4. Kiểm tra AI response (streaming hoặc complete)

#### C. Streaming Test
1. Gửi tin nhắn dài để trigger streaming
2. Quan sát streaming chunks hiển thị real-time
3. Kiểm tra message hoàn chỉnh sau khi streaming end

#### D. Reconnection Test
1. Tắt backend server
2. Kiểm tra error status hiển thị
3. Bật lại backend
4. Nhấn "Reconnect" hoặc đợi auto-reconnect

### 3. Debug Tools

#### Browser Console
```javascript
// Kiểm tra WebSocket connection
console.log('WebSocket status:', window.webappChatService?.getConnectionStatus());

// Monitor events
window.webappChatService?.on('connection_status_changed', console.log);
```

#### Network Tab
- Kiểm tra WebSocket connection trong Network tab
- Monitor WebSocket frames (messages)

## 🔧 Cách sử dụng trong Production

### 1. Basic Usage - WebSocket Mode

```tsx
import { ChatPanel } from '@/shared/components/layout/chat-panel';

function MyApp() {
  return (
    <ChatPanel
      onClose={() => setShowChat(false)}
      mode="websocket"
      websocketConfig={{
        url: import.meta.env.VITE_WEBSOCKET_URL,
        namespace: 'webapp-chat',
        auth: {
          token: localStorage.getItem('access_token'),
        },
      }}
    />
  );
}
```

### 2. Demo Mode (for testing without backend)

```tsx
import { ChatPanel } from '@/shared/components/layout/chat-panel';

function MyApp() {
  return (
    <ChatPanel
      onClose={() => setShowChat(false)}
      mode="demo"
    />
  );
}
```

### 3. Advanced Usage với Hook

```tsx
import { useWebappChatWebSocket } from '@/shared/hooks/chat/useWebappChatWebSocket';

function CustomChat() {
  const {
    isConnected,
    messages,
    sendMessage,
    streamingMessage,
    isStreaming,
    isAITyping,
  } = useWebappChatWebSocket({
    config: {
      url: 'ws://localhost:3001',
      namespace: 'webapp-chat',
      auth: { token: 'your-jwt-token' },
    },
    autoJoinConversation: true,
  });

  return (
    <div>
      <div>Status: {isConnected ? 'Connected' : 'Disconnected'}</div>
      <div>Messages: {messages.length}</div>
      {isAITyping && <div>AI is typing...</div>}
      {isStreaming && <div>Streaming: {streamingMessage?.content}</div>}
    </div>
  );
}
```

## 🐛 Troubleshooting

### Connection Issues

**Problem**: Không kết nối được WebSocket
```bash
# Solution 1: Kiểm tra backend
curl http://localhost:3001/health

# Solution 2: Kiểm tra CORS
# Đảm bảo backend có cấu hình CORS cho WebSocket

# Solution 3: Kiểm tra URL
# Đảm bảo URL đúng format: ws://localhost:3001
```

### Authentication Issues

**Problem**: WebSocket bị reject do auth
```typescript
// Solution: Kiểm tra JWT token
const token = localStorage.getItem('access_token');
console.log('Token:', token);

// Đảm bảo token hợp lệ và chưa expire
```

### Message Issues

**Problem**: Tin nhắn không gửi được
```typescript
// Solution: Kiểm tra conversation state
console.log('Current conversation:', currentConversationId);

// Đảm bảo đã join conversation trước khi gửi message
await joinConversation();
await sendMessage('Hello');
```

### Streaming Issues

**Problem**: Streaming không hoạt động
```typescript
// Solution: Kiểm tra event listeners
service.on('ai_stream_chunk', (data) => {
  console.log('Stream chunk:', data);
});

service.on('ai_stream_end', (data) => {
  console.log('Stream ended:', data);
});
```

## 📊 Performance Tips

### 1. Message Optimization
```typescript
// Limit message history
const MAX_MESSAGES = 100;
const messages = allMessages.slice(-MAX_MESSAGES);
```

### 2. Typing Debounce
```typescript
// Debounce typing indicators
const debouncedStartTyping = debounce(startTyping, 300);
```

### 3. Memory Management
```typescript
// Cleanup on unmount
useEffect(() => {
  return () => {
    service.disconnect();
    service.destroy();
  };
}, []);
```

## 🔒 Security Considerations

1. **JWT Token**: Luôn validate token trước khi kết nối
2. **Input Sanitization**: Sanitize tất cả user input
3. **Rate Limiting**: Implement rate limiting cho messages
4. **HTTPS**: Sử dụng WSS (WebSocket Secure) trong production

## 📈 Next Steps

1. **File Upload**: Tích hợp upload files trong chat
2. **Message Reactions**: Thêm emoji reactions
3. **Message History**: Pagination cho lịch sử chat
4. **Multi-user**: Support multiple users trong conversation
5. **Voice Messages**: Tích hợp voice recording
6. **Push Notifications**: Notifications khi có tin nhắn mới

## 🆘 Support

Nếu gặp vấn đề:

1. Kiểm tra console logs
2. Verify backend WebSocket endpoint
3. Test với demo page trước
4. Kiểm tra network connectivity
5. Validate JWT token

---

**Status**: ✅ Ready for testing
**Demo URLs**:
- http://localhost:5173/demo/chat-panel-modes (Recommended)
- http://localhost:5173/demo/webapp-chat
**Documentation**: docs/webapp-chat-websocket-integration.md
