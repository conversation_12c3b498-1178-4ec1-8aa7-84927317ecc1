/**
 * SMS Integration Module
 * Quản lý tích hợp các nhà cung cấp dịch vụ SMS
 */

// Types
export * from './types';

// Constants
export * from './constants';

// Schemas
export * from './schemas';

// Services
export * from './services';

// Hooks
export * from './hooks';

// Components
export { default as SmsProviderCard } from './components/SmsProviderCard';
export { default as SmsProviderForm } from './components/SmsProviderForm';
export { default as SmsProviderList } from './components/SmsProviderList';

// Pages
export { default as SmsIntegrationPage } from './pages/SmsIntegrationPage';

/**
 * SMS Integration Module Configuration
 */
export const SMS_INTEGRATION_CONFIG = {
  name: 'SMS Integration',
  version: '1.0.0',
  description: 'SMS providers integration and management',
  supportedProviders: [
    'twilio',
    'aws-sns',
    'viettel',
    'vnpt',
    'fpt',
    'custom'
  ],
  routes: {
    main: '/integrations/sms',
    providers: '/integrations/sms/providers',
    settings: '/integrations/sms/settings'
  }
} as const;
