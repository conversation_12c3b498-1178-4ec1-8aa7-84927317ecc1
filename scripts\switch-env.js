#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> để switch giữa các environment configurations
 * Usage: node scripts/switch-env.js [localhost|remote|production]
 */

const fs = require('fs');
const path = require('path');

const environments = {
  localhost: '.env.localhost',
  remote: '.env.remote', 
  production: '.env.production',
  testing: '.env.testing'
};

function switchEnvironment(envName) {
  const envFile = environments[envName];
  
  if (!envFile) {
    console.error(`❌ Unknown environment: ${envName}`);
    console.log('Available environments:', Object.keys(environments).join(', '));
    process.exit(1);
  }

  const sourcePath = path.join(process.cwd(), envFile);
  const targetPath = path.join(process.cwd(), '.env');

  // Check if source file exists
  if (!fs.existsSync(sourcePath)) {
    console.error(`❌ Environment file not found: ${envFile}`);
    process.exit(1);
  }

  try {
    // Copy environment file to .env
    fs.copyFileSync(sourcePath, targetPath);
    console.log(`✅ Switched to ${envName} environment`);
    console.log(`📁 Copied ${envFile} → .env`);
    
    // Show current configuration
    const content = fs.readFileSync(targetPath, 'utf8');
    const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'));
    
    console.log('\n📋 Current configuration:');
    lines.forEach(line => {
      if (line.includes('=')) {
        const [key, value] = line.split('=');
        console.log(`   ${key}=${value}`);
      }
    });
    
  } catch (error) {
    console.error(`❌ Failed to switch environment: ${error.message}`);
    process.exit(1);
  }
}

function showCurrentEnvironment() {
  const envPath = path.join(process.cwd(), '.env');
  
  if (!fs.existsSync(envPath)) {
    console.log('❌ No .env file found');
    return;
  }

  const content = fs.readFileSync(envPath, 'utf8');
  
  // Try to detect which environment is currently active
  let detectedEnv = 'unknown';
  for (const [envName, envFile] of Object.entries(environments)) {
    const envFilePath = path.join(process.cwd(), envFile);
    if (fs.existsSync(envFilePath)) {
      const envFileContent = fs.readFileSync(envFilePath, 'utf8');
      if (content === envFileContent) {
        detectedEnv = envName;
        break;
      }
    }
  }

  console.log(`🔍 Current environment: ${detectedEnv}`);
  
  const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'));
  console.log('\n📋 Current configuration:');
  lines.forEach(line => {
    if (line.includes('=')) {
      const [key, value] = line.split('=');
      console.log(`   ${key}=${value}`);
    }
  });
}

function showHelp() {
  console.log(`
🔧 Environment Switcher

Usage:
  node scripts/switch-env.js [environment]
  npm run env [environment]

Available environments:
  localhost    - Local development (ws://localhost:3001)
  remote       - Remote development (ws://*************:3001)  
  production   - Production environment
  testing      - Testing environment

Commands:
  node scripts/switch-env.js localhost    # Switch to localhost
  node scripts/switch-env.js remote       # Switch to remote server
  node scripts/switch-env.js current      # Show current environment
  node scripts/switch-env.js help         # Show this help

Examples:
  npm run env localhost    # Quick switch to localhost
  npm run env remote       # Quick switch to remote
  npm run env current      # Show current config
`);
}

// Main execution
const args = process.argv.slice(2);
const command = args[0];

if (!command || command === 'help') {
  showHelp();
} else if (command === 'current') {
  showCurrentEnvironment();
} else {
  switchEnvironment(command);
}
