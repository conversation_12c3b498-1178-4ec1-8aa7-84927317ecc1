import { PaginatedResult } from '@/shared/dto/response/api-response.dto';
import { SortDirection } from '@/shared/dto/request/query.dto';

// Re-export SortDirection để các file khác có thể import
export { SortDirection };

/**
 * Enum cho các trường sắp xếp khách hàng chuyển đổi
 */
export enum UserConvertCustomerSortField {
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  PLATFORM = 'platform',
}

/**
 * DTO cho các tham số truy vấn danh sách khách hàng chuyển đổi
 */
export interface QueryUserConvertCustomerDto {
  page?: number;
  limit?: number;
  search?: string;
  platform?: string;
  agentId?: string;
  sortBy?: UserConvertCustomerSortField;
  sortDirection?: SortDirection;
}

/**
 * DTO cho danh sách khách hàng chuyển đổi
 */
export interface UserConvertCustomerListItemDto {
  id: number;
  avatar: string | null;
  name: string | null;
  email: { primary?: string } | string | null;
  phone: string | null;
  platform: string | null;
  timezone: string | null;
  createdAt: number;
  updatedAt: number;
  metadata: Record<string, unknown> | null;
}

/**
 * Type cho kết quả phân trang khách hàng chuyển đổi
 */
export type PaginatedUserConvertCustomerResult = PaginatedResult<UserConvertCustomerListItemDto>;

/**
 * Interface cho tham số truy vấn khách hàng (legacy - để tương thích)
 */
export interface CustomerQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  status?: string;
}

/**
 * Interface cho dữ liệu khách hàng (legacy - để tương thích)
 */
export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  status: string;
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lastOrderDate?: string;
  customerSince: string;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}
