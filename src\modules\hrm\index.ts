/**
 * HRM Module Exports
 */

// Types
export * from './types/employee.types';
export * from './types/department.types';
export * from './types/permission.types';
export * from './types/recruitment.types';

// Services
export * from './services/employee.service';
export * from './services/employee-statistics.service';
export * from './services/department.service';
export * from './services/permission.service';
export * from './services/recruitment.service';

// Hooks
export * from './hooks/useEmployees';
export * from './hooks/useEmployeeStatistics';
export * from './hooks/useDepartments';
export * from './hooks/usePermissions';
export * from './hooks/useRecruitment';

// Pages
export { default as HrmHomePage } from './pages/HrmHomePage';
export { default as EmployeesPage } from './pages/EmployeesPage';
export { default as DepartmentsPage } from './pages/DepartmentsPage';
export { default as PermissionsPage } from './pages/PermissionsPage';
export { default as CreateUserForEmployeePage } from './pages/CreateUserForEmployeePage';

// Recruitment Pages
export { default as RecruitmentHomePage } from './pages/RecruitmentHomePage';
export { default as JobPositionsPage } from './pages/JobPositionsPage';
export { default as CandidatesPage } from './pages/CandidatesPage';

// Routes
export { default as hrmRoutes } from './routers/hrmRoutes';

// Locales
export { default as hrmResources } from './locales';
