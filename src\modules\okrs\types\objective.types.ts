import { SortDirection } from '@/shared/dto/request/query.dto';

/**
 * Enum định nghĩa các loại mục tiêu
 */
export enum ObjectiveType {
  COMPANY = 'COMPANY',
  DEPARTMENT = 'DEPARTMENT',
  INDIVIDUAL = 'INDIVIDUAL',
}

/**
 * Enum định nghĩa các trạng thái mục tiêu
 */
export enum ObjectiveStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  AT_RISK = 'at-risk',
  BEHIND = 'behind',
}

/**
 * Interface cho dữ liệu chu kỳ OKR
 */
export interface CycleDto {
  id: number;
  name: string;
  startDate: string;
  endDate: string;
  status: string;
}

/**
 * Interface cho dữ liệu mục tiêu
 */
export interface ObjectiveDto {
  id: number;
  title: string;
  description: string | null;
  ownerId: number;
  departmentId: number | null;
  parentId: number | null;
  cycleId: number;
  cycle?: CycleDto;
  type: ObjectiveType;
  progress: number | null;
  status: string | null;
  createdBy: number;
  createdAt: number | null;
  updatedAt: number | null;
}

/**
 * Interface cho tham số truy vấn danh sách mục tiêu
 */
export interface ObjectiveQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: SortDirection;
  type?: ObjectiveType;
  cycleId?: number;
  ownerId?: number;
  departmentId?: number;
  status?: string;
  parentId?: number;
  startDate?: Date;
  endDate?: Date;
}

/**
 * Interface cho dữ liệu tạo mục tiêu mới
 */
export interface CreateObjectiveDto {
  title: string;
  description?: string;
  ownerId: number;
  departmentId?: number;
  parentId?: number;
  cycleId: number;
  type: ObjectiveType;
  startDate?: string;
  endDate?: string;
}

/**
 * Interface cho dữ liệu cập nhật mục tiêu
 */
export interface UpdateObjectiveDto {
  title?: string;
  description?: string;
  ownerId?: number;
  departmentId?: number;
  parentId?: number;
  status?: string;
  type?: ObjectiveType;
  startDate?: string;
  endDate?: string;
}
