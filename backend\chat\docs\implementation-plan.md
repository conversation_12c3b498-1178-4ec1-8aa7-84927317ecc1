# Kế hoạch triển khai <PERSON>l Calling & SQL Generation AI

## 🎯 <PERSON><PERSON><PERSON> tiêu

<PERSON> cấp module chat từ **hard-coded intent handlers** thành **dynamic AI agent** với khả năng:
1. **Tool Calling**: Gọi functions/tools động dựa trên OpenAI Function Calling
2. **SQL Generation**: Tự động tạo SQL queries từ natural language

## 📋 Phân tích Gap hiện tại

### ❌ **Thiếu <PERSON>l Calling**
```typescript
// Hiện tại: Hard-coded
switch (intent) {
  case 'get_todo_statistics':
    return this.handleTodoStatistics(content, context, tenantId);
  case 'get_employee_info':
    return this.handleEmployeeInfo(content, context, tenantId);
}

// Cần: Dynamic Tool Calling
const tools = await this.toolRegistry.getAvailableTools();
const selectedTool = await this.aiService.selectTool(query, tools);
const result = await this.toolRegistry.executeTool(selectedTool, params);
```

### ❌ **<PERSON><PERSON><PERSON><PERSON>**
```typescript
// Hiện tại: Pre-built service calls
const stats = await this.todoService.getStatistics(tenantId);
const employees = await this.employeeService.findAll(tenantId);

// Cần: Dynamic SQL Generation
const sql = await this.textToSqlService.generateSQL(query, schema);
const result = await this.databaseService.executeQuery(sql, tenantId);
```

## 🚀 Implementation Plan

### **Phase 1: Tool Calling Infrastructure** (Ưu tiên cao)

#### **1.1 Tool Definition System**
```typescript
// File: src/modules/chat/interfaces/tool.interface.ts
interface ToolDefinition {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required: string[];
  };
  handler: (params: any, context: ToolContext) => Promise<any>;
  category: 'database' | 'business' | 'utility' | 'external';
  tenantIsolated: boolean;
}

interface ToolContext {
  tenantId: number;
  userId: number;
  conversationId: number;
  userContext?: any;
}
```

#### **1.2 Tool Registry Service**
```typescript
// File: src/modules/chat/services/tool-registry.service.ts
@Injectable()
export class ToolRegistryService {
  private tools: Map<string, ToolDefinition> = new Map();
  
  registerTool(tool: ToolDefinition): void;
  getAvailableTools(category?: string): ToolDefinition[];
  getTool(name: string): ToolDefinition | null;
  executeTool(name: string, params: any, context: ToolContext): Promise<any>;
  validateToolParams(tool: ToolDefinition, params: any): boolean;
}
```

#### **1.3 Enhanced OpenAI Service**
```typescript
// File: src/shared/services/ai/enhanced-openai.service.ts
@Injectable()
export class EnhancedOpenAiService extends OpenAiService {
  async chatCompletionWithTools(
    messages: any[],
    tools: ToolDefinition[],
    model: string = 'gpt-4'
  ): Promise<{
    message: string;
    toolCalls?: Array<{
      name: string;
      parameters: any;
    }>;
  }>;
}
```

#### **1.4 Business Tools Implementation**
```typescript
// File: src/modules/chat/tools/business-tools.ts
export const TODO_STATISTICS_TOOL: ToolDefinition = {
  name: 'get_todo_statistics',
  description: 'Lấy thống kê công việc của người dùng hoặc team',
  parameters: {
    type: 'object',
    properties: {
      userId: { type: 'number', description: 'ID người dùng (optional)' },
      teamId: { type: 'number', description: 'ID team (optional)' },
      dateRange: { type: 'string', description: 'Khoảng thời gian: today, week, month' }
    },
    required: []
  },
  handler: async (params, context) => {
    // Implementation
  },
  category: 'business',
  tenantIsolated: true
};
```

### **Phase 2: SQL Generation AI** (Ưu tiên cao)

#### **2.1 Database Schema Service**
```typescript
// File: src/modules/chat/services/database-schema.service.ts
@Injectable()
export class DatabaseSchemaService {
  async getSchemaForTenant(tenantId: number): Promise<DatabaseSchema>;
  async getTableInfo(tableName: string, tenantId: number): Promise<TableInfo>;
  async getRelationships(tenantId: number): Promise<TableRelationship[]>;
  async validateTableAccess(tableName: string, tenantId: number): Promise<boolean>;
}

interface DatabaseSchema {
  tables: TableInfo[];
  relationships: TableRelationship[];
  views: ViewInfo[];
}
```

#### **2.2 Text-to-SQL Service**
```typescript
// File: src/modules/chat/services/text-to-sql.service.ts
@Injectable()
export class TextToSqlService {
  async generateSQL(
    query: string, 
    schema: DatabaseSchema,
    tenantId: number
  ): Promise<{
    sql: string;
    explanation: string;
    confidence: number;
    estimatedRows: number;
  }>;
  
  async validateSQL(sql: string, tenantId: number): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }>;
  
  async optimizeSQL(sql: string): Promise<string>;
}
```

#### **2.3 Safe SQL Executor**
```typescript
// File: src/modules/chat/services/safe-sql-executor.service.ts
@Injectable()
export class SafeSqlExecutorService {
  async executeQuery(
    sql: string, 
    tenantId: number,
    options: {
      maxRows?: number;
      timeout?: number;
      readOnly?: boolean;
    }
  ): Promise<{
    data: any[];
    rowCount: number;
    executionTime: number;
    fromCache: boolean;
  }>;
  
  private validateSqlSafety(sql: string): boolean;
  private injectTenantFilter(sql: string, tenantId: number): string;
}
```

### **Phase 3: Enhanced AI Orchestrator** (Ưu tiên trung bình)

#### **3.1 New AI Orchestrator**
```typescript
// File: src/modules/chat/services/enhanced-ai-orchestrator.service.ts
@Injectable()
export class EnhancedAIOrchestatorService {
  async processMessageWithTools(
    conversation: ChatConversation,
    message: ChatMessage,
    tenantId: number
  ): Promise<AIResponse>;
  
  private async selectAndExecuteTools(
    query: string,
    context: ConversationContext,
    tenantId: number
  ): Promise<ToolExecutionResult[]>;
  
  private async generateResponseWithToolResults(
    query: string,
    toolResults: ToolExecutionResult[],
    context: ConversationContext
  ): Promise<string>;
}
```

### **Phase 4: Advanced Features** (Ưu tiên thấp)

#### **4.1 Tool Performance Monitoring**
```typescript
// File: src/modules/chat/services/tool-monitoring.service.ts
@Injectable()
export class ToolMonitoringService {
  async logToolExecution(toolName: string, params: any, result: any, duration: number): Promise<void>;
  async getToolPerformanceStats(toolName: string): Promise<ToolStats>;
  async detectAnomalies(): Promise<ToolAnomaly[]>;
}
```

#### **4.2 Conversation Analytics**
```typescript
// File: src/modules/chat/services/conversation-analytics.service.ts
@Injectable()
export class ConversationAnalyticsService {
  async trackToolUsage(conversationId: number, toolName: string): Promise<void>;
  async getPopularTools(tenantId: number): Promise<ToolUsageStats[]>;
  async getUserSatisfactionScore(conversationId: number): Promise<number>;
}
```

## 📅 Timeline & Milestones

### **Week 1-2: Phase 1 - Tool Calling Infrastructure**
- [x] Day 1-2: Tool interfaces và registry service ✅ COMPLETED
- [x] Day 3-4: Enhanced OpenAI service với function calling ✅ COMPLETED
- [x] Day 5-7: Business tools implementation ✅ COMPLETED
- [x] Day 8-10: Integration testing ✅ COMPLETED

### **Week 3-4: Phase 2 - SQL Generation AI**
- [ ] Day 1-3: Database schema service
- [ ] Day 4-6: Text-to-SQL service với OpenAI
- [ ] Day 7-9: Safe SQL executor với security
- [ ] Day 10-14: Testing và optimization

### **Week 5: Phase 3 - Enhanced AI Orchestrator**
- [ ] Day 1-3: New orchestrator implementation
- [ ] Day 4-5: Integration với existing services
- [ ] Day 6-7: End-to-end testing

### **Week 6: Phase 4 - Advanced Features**
- [ ] Day 1-3: Monitoring và analytics
- [ ] Day 4-5: Performance optimization
- [ ] Day 6-7: Documentation và deployment

## 🔧 Technical Implementation Details

### **Tool Calling Flow**
```mermaid
graph TD
    A[User Message] --> B[AI Orchestrator]
    B --> C[OpenAI Function Calling]
    C --> D[Tool Selection]
    D --> E[Tool Registry]
    E --> F[Tool Execution]
    F --> G[Result Processing]
    G --> H[Response Generation]
    H --> I[User Response]
```

### **SQL Generation Flow**
```mermaid
graph TD
    A[Natural Language Query] --> B[Schema Analysis]
    B --> C[Text-to-SQL AI]
    C --> D[SQL Validation]
    D --> E[Security Check]
    E --> F[Tenant Isolation]
    F --> G[Query Execution]
    G --> H[Result Formatting]
    H --> I[Natural Language Response]
```

## 🛡️ Security Considerations

### **Tool Calling Security**
- ✅ Tool permission system
- ✅ Parameter validation
- ✅ Rate limiting per tool
- ✅ Audit logging

### **SQL Generation Security**
- ✅ Read-only queries only
- ✅ Tenant isolation injection
- ✅ SQL injection prevention
- ✅ Query complexity limits
- ✅ Execution timeout

## 📊 Success Metrics

### **Tool Calling Metrics**
- Tool execution success rate > 95%
- Average tool response time < 2s
- User satisfaction score > 4.5/5

### **SQL Generation Metrics**
- SQL accuracy rate > 90%
- Query execution success rate > 95%
- Average query generation time < 3s

## 🚀 Next Steps

1. **Tạo interfaces và base services**
2. **Implement Tool Registry**
3. **Enhance OpenAI Service**
4. **Build Business Tools**
5. **Implement SQL Generation**
6. **Integration Testing**
7. **Performance Optimization**
8. **Documentation & Deployment**

---

**Estimated Total Development Time: 6 weeks**
**Team Size: 1-2 developers**
**Priority: High (Tool Calling), High (SQL Generation)**
