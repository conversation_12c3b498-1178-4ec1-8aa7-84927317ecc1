/**
 * Debug script để kiểm tra authentication token
 * Chạy trong browser console
 */

console.log('=== DEBUG AUTHENTICATION TOKEN ===');

// 1. Kiểm tra localStorage
const accessToken = localStorage.getItem('access_token');
console.log('🔑 Access Token from localStorage:', accessToken);

if (!accessToken) {
  console.error('❌ Không có access_token trong localStorage!');
  console.log('💡 Cần đăng nhập để có token');
} else {
  console.log('✅ Có access_token');
  
  // 2. Kiểm tra format token
  if (accessToken.startsWith('Bearer ')) {
    console.log('📝 Token format: Bearer token');
  } else {
    console.log('📝 Token format: Raw token (không có Bearer prefix)');
  }
  
  // 3. Thử decode JWT (nếu là JWT)
  try {
    const parts = accessToken.replace('Bearer ', '').split('.');
    if (parts.length === 3) {
      const payload = JSON.parse(atob(parts[1]));
      console.log('🔓 JWT Payload:', payload);
      
      // Kiểm tra expiry
      if (payload.exp) {
        const expiry = new Date(payload.exp * 1000);
        const now = new Date();
        console.log('⏰ Token expires at:', expiry);
        console.log('⏰ Current time:', now);
        console.log('✅ Token valid:', expiry > now);
      }
    }
  } catch (error) {
    console.log('⚠️ Không thể decode JWT:', error.message);
  }
}

// 4. Kiểm tra các key khác có thể chứa token
const allKeys = Object.keys(localStorage);
console.log('🗂️ Tất cả localStorage keys:', allKeys);

const tokenKeys = allKeys.filter(key => 
  key.toLowerCase().includes('token') || 
  key.toLowerCase().includes('auth') ||
  key.toLowerCase().includes('jwt')
);

if (tokenKeys.length > 0) {
  console.log('🔍 Các key có thể chứa token:');
  tokenKeys.forEach(key => {
    const value = localStorage.getItem(key);
    console.log(`  ${key}:`, value?.substring(0, 50) + '...');
  });
}

// 5. Kiểm tra sessionStorage
const sessionToken = sessionStorage.getItem('access_token');
if (sessionToken) {
  console.log('🔑 Access Token from sessionStorage:', sessionToken);
}

// 6. Kiểm tra cookies
const cookies = document.cookie.split(';').map(c => c.trim());
const authCookies = cookies.filter(c => 
  c.toLowerCase().includes('token') || 
  c.toLowerCase().includes('auth') ||
  c.toLowerCase().includes('jwt')
);

if (authCookies.length > 0) {
  console.log('🍪 Auth cookies:', authCookies);
}

console.log('=== END DEBUG ===');
