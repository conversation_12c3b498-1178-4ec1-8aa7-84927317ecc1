import React from 'react';
import { useTranslation } from 'react-i18next';

import ModuleCard from '@/modules/components/card/ModuleCard';
import { ResponsiveGrid } from '@/shared/components/common';

/**
 * Trang chủ module Todolist
 */
const TodolistHomePage: React.FC = () => {
  const { t } = useTranslation(['common', 'todolist']);

  // Danh sách các module con của Todolist
  const subModules = [
    {
      id: 'dashboard',
      title: t('todolist:modules.dashboard.title', 'Dashboard'),
      description: t('todolist:modules.dashboard.description', 'Tổng quan về công việc và dự án'),
      icon: 'chart',
      count: '-',
      countLabel: '',
      linkTo: '/todolist/dashboard',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'tasks',
      title: t('todolist:modules.tasks.title', '<PERSON><PERSON> sách công việc'),
      description: t('todolist:modules.tasks.description', '<PERSON><PERSON><PERSON><PERSON> lý các công vi<PERSON>c cần thực hiện'),
      icon: 'check',
      count: 24,
      countLabel: t('todolist:modules.tasks.countLabel', 'Công việc'),
      linkTo: '/todolist/tasks',
      linkText: t('common:view', 'Xem'),
    }
  ];

  return (
    <div>
      <ResponsiveGrid
        gap={4}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
      >
        {subModules.map(module => (
          <ModuleCard
            key={module.id}
            title={module.title}
            description={module.description}
            icon={module.icon}
            linkTo={module.linkTo}
            className="h-full"
          />
        ))}
      </ResponsiveGrid>
    </div>
  );
};

export default TodolistHomePage;
