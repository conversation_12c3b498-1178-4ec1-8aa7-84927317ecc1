/**
 * Hook cho thống kê nhân viên
 */
import { useQuery } from '@tanstack/react-query';

import { EmployeeStatisticsService } from '../services/employee-statistics.service';

// Query keys
export const EMPLOYEE_STATISTICS_QUERY_KEYS = {
  ALL: ['employee-statistics'] as const,
  OVERVIEW: () => [...EMPLOYEE_STATISTICS_QUERY_KEYS.ALL, 'overview'] as const,
  DATE_RANGE: (startDate: string, endDate: string) =>
    [...EMPLOYEE_STATISTICS_QUERY_KEYS.ALL, 'date-range', startDate, endDate] as const,
  DEPARTMENT: (departmentId: number) =>
    [...EMPLOYEE_STATISTICS_QUERY_KEYS.ALL, 'department', departmentId] as const,
};

/**
 * Hook để lấy thống kê tổng quan nhân viên
 */
export const useEmployeeStatistics = () => {
  return useQuery({
    queryKey: EMPLOYEE_STATISTICS_QUERY_KEYS.OVERVIEW(),
    queryFn: () => EmployeeStatisticsService.getEmployeeStatistics(),
    select: data => data.result,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy thống kê nhân viên theo khoảng thời gian
 */
export const useEmployeeStatisticsByDateRange = (startDate: string, endDate: string) => {
  return useQuery({
    queryKey: EMPLOYEE_STATISTICS_QUERY_KEYS.DATE_RANGE(startDate, endDate),
    queryFn: () => EmployeeStatisticsService.getEmployeeStatisticsByDateRange(startDate, endDate),
    select: data => data.result,
    enabled: !!startDate && !!endDate,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy thống kê nhân viên theo phòng ban
 */
export const useEmployeeStatisticsByDepartment = (departmentId: number) => {
  return useQuery({
    queryKey: EMPLOYEE_STATISTICS_QUERY_KEYS.DEPARTMENT(departmentId),
    queryFn: () => EmployeeStatisticsService.getEmployeeStatisticsByDepartment(departmentId),
    select: data => data.result,
    enabled: !!departmentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
