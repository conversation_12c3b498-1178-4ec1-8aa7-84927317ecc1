import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import {
  CreateWarehouseCustomFieldDto,
  UpdateWarehouseCustomFieldDto,
  WarehouseCustomFieldDto,
} from '../types/warehouse.types';

/**
 * Service xử lý API liên quan đến trường tùy chỉnh của kho
 */
export const WarehouseCustomFieldService = {
  /**
   * Lấy danh sách trường tùy chỉnh của kho
   * @param warehouseId ID của kho
   * @returns Danh sách trường tùy chỉnh
   */
  getCustomFields: async (warehouseId: number): Promise<ApiResponseDto<WarehouseCustomFieldDto[]>> => {
    return apiRequest.get(`/user/warehouses/${warehouseId}/custom-fields`);
  },

  /**
   * Lấy chi tiết trường tùy chỉnh của kho theo ID
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @returns Chi tiết trường tùy chỉnh
   */
  getCustomFieldById: async (warehouseId: number, fieldId: number): Promise<ApiResponseDto<WarehouseCustomFieldDto>> => {
    return apiRequest.get(`/user/warehouses/${warehouseId}/custom-fields/${fieldId}`);
  },

  /**
   * Thêm trường tùy chỉnh cho kho
   * @param warehouseId ID của kho
   * @param data Dữ liệu tạo trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh đã tạo
   */
  addCustomField: async (warehouseId: number, data: CreateWarehouseCustomFieldDto): Promise<ApiResponseDto<WarehouseCustomFieldDto>> => {
    return apiRequest.post(`/user/warehouses/${warehouseId}/custom-fields`, data);
  },

  /**
   * Cập nhật trường tùy chỉnh của kho
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @param data Dữ liệu cập nhật trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh đã cập nhật
   */
  updateCustomField: async (
    warehouseId: number,
    fieldId: number,
    data: UpdateWarehouseCustomFieldDto
  ): Promise<ApiResponseDto<WarehouseCustomFieldDto>> => {
    return apiRequest.put(`/user/warehouses/${warehouseId}/custom-fields/${fieldId}`, data);
  },

  /**
   * Xóa trường tùy chỉnh của kho
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @returns Thông báo xóa thành công
   */
  deleteCustomField: async (warehouseId: number, fieldId: number): Promise<ApiResponseDto<null>> => {
    return apiRequest.delete(`/user/warehouses/${warehouseId}/custom-fields/${fieldId}`);
  },
};
