import React from 'react';

import { ObjectiveDto, ObjectiveType } from '../types/objective.types';

import EditCompanyObjectiveForm from './EditCompanyObjectiveForm';
import EditDepartmentObjectiveForm from './EditDepartmentObjectiveForm';
import EditObjectiveForm from './EditObjectiveForm';

export interface EditObjectiveFlowProps {
  /**
   * Objective cần chỉnh sửa
   */
  objective: ObjectiveDto;

  /**
   * Callback khi form được submit thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi form bị đóng
   */
  onCancel?: () => void;
}

/**
 * Component điều hướng form chỉnh sửa objective dựa trên loại objective
 */
const EditObjectiveFlow: React.FC<EditObjectiveFlowProps> = ({
  objective,
  onSuccess,
  onCancel,
}) => {
  // Render form tương ứng với loại mục tiêu
  const renderEditForm = () => {
    switch (objective.type) {
      case ObjectiveType.INDIVIDUAL:
        return (
          <EditObjectiveForm
            objective={objective}
            onSuccess={onSuccess}
            onCancel={onCancel}
          />
        );

      case ObjectiveType.DEPARTMENT:
        return (
          <EditDepartmentObjectiveForm
            objective={objective}
            onSuccess={onSuccess}
            onCancel={onCancel}
          />
        );

      case ObjectiveType.COMPANY:
        return (
          <EditCompanyObjectiveForm
            objective={objective}
            onSuccess={onSuccess}
            onCancel={onCancel}
          />
        );

      default:
        return null;
    }
  };

  return <>{renderEditForm()}</>;
};

export default EditObjectiveFlow;
