import React from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, Typography, Button } from '@/shared/components/common';

export interface ConfirmDeleteModalProps {
  /**
   * Trạng thái hiển thị của modal
   */
  isOpen: boolean;

  /**
   * Callback khi đóng modal
   */
  onClose: () => void;

  /**
   * Callback khi xác nhận xóa
   */
  onConfirm: () => void;

  /**
   * Tiêu đề của modal
   */
  title: string;

  /**
   * Nội dung thông báo
   */
  message: string;

  /**
   * Tên của item cần xóa (nếu có)
   */
  itemName?: string;

  /**
   * Trạng thái đang submit
   */
  isSubmitting?: boolean;

  /**
   * Nội dung nút xác nhận
   * @default 'Xóa'
   */
  confirmButtonText?: string;

  /**
   * Nội dung nút hủy
   * @default 'Hủy'
   */
  cancelButtonText?: string;

  /**
   * Variant của nút xác nhận
   * @default 'danger'
   */
  confirmButtonVariant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'outline' | 'ghost';
}

/**
 * Modal xác nhận xóa dùng chung
 */
const ConfirmDeleteModal: React.FC<ConfirmDeleteModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  itemName,
  isSubmitting = false,
  confirmButtonText,
  cancelButtonText,
  confirmButtonVariant = 'danger',
}) => {
  const { t } = useTranslation('common');

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="sm"
      footer={
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            {cancelButtonText || t('common.cancel', 'Hủy')}
          </Button>
          <Button variant={confirmButtonVariant} onClick={onConfirm} disabled={isSubmitting}>
            {confirmButtonText || t('common.delete', 'Xóa')}
          </Button>
        </div>
      }
    >
      <div className="py-4">
        <Typography>{message}</Typography>
        {itemName && (
          <Typography variant="body2" className="mt-2 font-semibold">
            {itemName}
          </Typography>
        )}
      </div>
    </Modal>
  );
};

export default ConfirmDeleteModal;
