import { ProductField } from '../types/product-import.types';

// Default Product Fields for Import (matching backend DTO)
export const DEFAULT_PRODUCT_FIELDS: ProductField[] = [
  {
    key: 'name',
    label: '<PERSON>ên sản phẩm',
    type: 'text',
    required: true,
  },
  {
    key: 'listPrice',
    label: '<PERSON><PERSON><PERSON> niêm yết',
    type: 'number',
    required: false,
  },
  {
    key: 'salePrice',
    label: '<PERSON><PERSON><PERSON> bán',
    type: 'number',
    required: false,
  },
  {
    key: 'description',
    label: '<PERSON><PERSON> tả',
    type: 'textarea',
    required: false,
  },
  {
    key: 'tags',
    label: 'Tags (phân cách bằng dấu phẩy)',
    type: 'text',
    required: false,
  },
  {
    key: 'widthCm',
    label: 'Chiều rộng (cm)',
    type: 'number',
    required: false,
  },
  {
    key: 'heightCm',
    label: 'Chiều cao (cm)',
    type: 'number',
    required: false,
  },
  {
    key: 'lengthCm',
    label: '<PERSON><PERSON><PERSON> dà<PERSON> (cm)',
    type: 'number',
    required: false,
  },
  {
    key: 'weightGram',
    label: 'Trọng lượng (gram)',
    type: 'number',
    required: false,
  },
];

// File Upload Constraints
export const PRODUCT_IMPORT_FILE_CONSTRAINTS = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_ROWS: 10000,
  SUPPORTED_FORMATS: ['.xlsx', '.xls', '.csv'],
  SUPPORTED_MIME_TYPES: [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
    'text/csv',
  ],
};

// Validation Messages
export const PRODUCT_IMPORT_VALIDATION_MESSAGES = {
  REQUIRED_FIELD: 'Trường này là bắt buộc',
  INVALID_NUMBER: 'Giá trị phải là số',
  INVALID_PRICE: 'Giá phải là số dương',
  INVALID_DIMENSIONS: 'Kích thước phải là số dương',
  INVALID_WEIGHT: 'Trọng lượng phải là số dương',
  NAME_TOO_LONG: 'Tên sản phẩm quá dài (tối đa 255 ký tự)',
  DESCRIPTION_TOO_LONG: 'Mô tả quá dài (tối đa 1000 ký tự)',
  TAGS_TOO_LONG: 'Tags quá dài (tối đa 500 ký tự)',
  MISSING_PRICE: 'Cần có ít nhất giá niêm yết hoặc giá bán',
};

// Auto-mapping suggestions for common column names
export const PRODUCT_COLUMN_AUTO_MAPPING: Record<string, string> = {
  // Name variations
  'name': 'name',
  'product_name': 'name',
  'product name': 'name',
  'tên sản phẩm': 'name',
  'ten san pham': 'name',
  'title': 'name',

  // Price variations
  'list_price': 'listPrice',
  'list price': 'listPrice',
  'giá niêm yết': 'listPrice',
  'gia niem yet': 'listPrice',
  'price': 'listPrice',
  'cost': 'listPrice',

  'sale_price': 'salePrice',
  'sale price': 'salePrice',
  'giá bán': 'salePrice',
  'gia ban': 'salePrice',
  'selling_price': 'salePrice',
  'selling price': 'salePrice',

  // Description variations
  'description': 'description',
  'desc': 'description',
  'mô tả': 'description',
  'mo ta': 'description',
  'note': 'description',
  'notes': 'description',

  // Tags variations
  'tags': 'tags',
  'tag': 'tags',
  'nhãn': 'tags',
  'nhan': 'tags',
  'keywords': 'tags',
  'từ khóa': 'tags',
  'tu khoa': 'tags',

  // Shipment config variations
  'width': 'widthCm',
  'width_cm': 'widthCm',
  'chiều rộng': 'widthCm',
  'chieu rong': 'widthCm',

  'height': 'heightCm',
  'height_cm': 'heightCm',
  'chiều cao': 'heightCm',
  'chieu cao': 'heightCm',

  'length': 'lengthCm',
  'length_cm': 'lengthCm',
  'chiều dài': 'lengthCm',
  'chieu dai': 'lengthCm',

  'weight': 'weightGram',
  'weight_gram': 'weightGram',
  'trọng lượng': 'weightGram',
  'trong luong': 'weightGram',
};

// Import Progress Polling
export const PRODUCT_IMPORT_POLLING = {
  INTERVAL: 1000, // 1 second
  MAX_ATTEMPTS: 300, // 5 minutes max
  TIMEOUT: 300000, // 5 minutes
};

// Query Keys for React Query
export const PRODUCT_IMPORT_QUERY_KEYS = {
  ALL: ['product-import'] as const,
  PROGRESS: (jobId: string) => [...PRODUCT_IMPORT_QUERY_KEYS.ALL, 'progress', jobId] as const,
  VALIDATION: (jobId: string) => [...PRODUCT_IMPORT_QUERY_KEYS.ALL, 'validation', jobId] as const,
  TEMPLATES: () => [...PRODUCT_IMPORT_QUERY_KEYS.ALL, 'templates'] as const,
};

// Default Import Options
export const DEFAULT_PRODUCT_IMPORT_OPTIONS = {
  skipInvalidRows: true,
  updateExisting: false,
  sendNotification: false,
};

// Batch Processing
export const PRODUCT_IMPORT_BATCH_SIZE = 100; // Process 100 products at a time
