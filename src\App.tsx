import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { HelmetProvider } from 'react-helmet-async';
import { ThemeProvider } from '@/shared/contexts/theme';
import { LanguageProvider } from '@/shared/contexts/language';
import { ChatPanelProvider } from '@/shared/contexts/chat-panel';
import { RPointProvider } from '@/shared/contexts/rpoint';
import { store, persistor } from '@/shared/store';
import AppRouter from '@/shared/routers';
import { Loading } from '@/shared/components/common';

// Import i18n (needs to be bundled)
import '@/lib/i18n';
import TaskQueueProvider from './shared/contexts/TaskQueueContext';

const App = () => {
  return (
    <Provider store={store}>
      <PersistGate loading={<Loading />} persistor={persistor}>
        <HelmetProvider>
          <ThemeProvider>
            <LanguageProvider>
              <ChatPanelProvider>
                <RPointProvider>
                  <TaskQueueProvider
                    options={{
                      concurrency: 3,
                      defaultMaxRetries: 3,
                      autoRemoveCompletedAfter: 60000,
                    }}
                  >
                    <AppRouter />
                  </TaskQueueProvider>
                </RPointProvider>
              </ChatPanelProvider>
            </LanguageProvider>
          </ThemeProvider>
        </HelmetProvider>
      </PersistGate>
    </Provider>
  );
};

export default App;
