# Employee Forms Documentation

## Tổng quan

Module này chứa các form để quản lý nhân viên trong hệ thống HRM.

## Components

### 1. CreateEmployeeForm
- **<PERSON><PERSON><PERSON> đích**: Form tạo nhân viên mới
- **Gia<PERSON> diện**: Sử dụng CollapsibleCard để tổ chức thông tin theo nhóm
- **Schema**: `createEmployeeSchema`
- **Props**:
  - `initialData?`: D<PERSON> liệu khởi tạo (tùy chọn)
  - `onSubmit`: Callback khi submit form
  - `onCancel`: Callback khi hủy form
  - `isSubmitting?`: Trạng thái đang submit
  - `isEditMode?`: Chế độ chỉnh sửa

### 2. EditEmployeeForm
- **Mục đích**: Form sửa thông tin nhân viên
- **Gia<PERSON> diện**: Giống hệt CreateEmployeeForm
- **Schema**: `updateEmployeeSchema` (partial của createEmployeeSchema)
- **Props**:
  - `initialData`: <PERSON><PERSON> liệu nhân viên hiện tại (bắt buộc)
  - `onSubmit`: Callback khi submit form
  - `onCancel`: Callback khi hủy form
  - `isSubmitting?`: Trạng thái đang submit

## Cấu trúc Form

Cả hai form đều có cấu trúc giống nhau với 4 nhóm thông tin:

### 1. Thông tin cơ bản (mở mặc định)
- Tên nhân viên (bắt buộc)
- Chức danh
- Cấp bậc
- Phòng ban (AsyncSelectWithPagination)
- Quản lý (AsyncSelectWithPagination)
- Loại hợp đồng
- Trạng thái
- Ngày vào làm
- Ngày kết thúc thử việc

### 2. Thông tin cá nhân (đóng mặc định)
- Ngày sinh
- Giới tính
- Tình trạng hôn nhân
- Số người phụ thuộc

### 3. Liên hệ khẩn cấp (đóng mặc định)
- Tên liên hệ
- Số điện thoại
- Mối quan hệ

### 4. Ghi chú (đóng mặc định)
- Ghi chú

## Sử dụng trong EmployeesPage

### Form thêm nhân viên
```tsx
<SlideInForm isVisible={isVisible}>
  <CreateEmployeeForm
    onSubmit={handleSubmit}
    onCancel={handleCancel}
    isSubmitting={createEmployeeMutation.isPending}
  />
</SlideInForm>
```

### Form sửa nhân viên
```tsx
<SlideInForm isVisible={isEditFormVisible}>
  {selectedEmployeeData && (
    <EditEmployeeForm
      initialData={selectedEmployeeData}
      onSubmit={handleEditSubmit}
      onCancel={handleCloseEditForm}
      isSubmitting={updateEmployeeMutation.isPending}
    />
  )}
</SlideInForm>
```

## Actions trong bảng

Đã thêm action "Sửa" vào menu actions của mỗi hàng trong bảng:

```tsx
{
  id: 'edit',
  label: t('common:edit', 'Sửa'),
  icon: 'edit',
  onClick: () => handleOpenEditForm(record.id),
}
```

## Hooks sử dụng

- `useEmployee(id)`: Lấy chi tiết nhân viên theo ID
- `useUpdateEmployee()`: Cập nhật thông tin nhân viên
- `useCreateEmployee()`: Tạo nhân viên mới

## Validation

- Sử dụng Zod schema để validate dữ liệu
- `createEmployeeSchema`: Cho form tạo mới
- `updateEmployeeSchema`: Cho form cập nhật (partial của createEmployeeSchema)

## Xử lý lỗi

- Sử dụng `useFormErrors` hook để hiển thị lỗi field-specific
- Lỗi từ API sẽ được map vào các field tương ứng

## Tính năng

✅ Form tạo nhân viên mới
✅ Form sửa nhân viên với giao diện giống hệt form tạo
✅ Validation đầy đủ
✅ Xử lý lỗi field-specific
✅ Loading state
✅ AsyncSelectWithPagination cho phòng ban và quản lý
✅ CollapsibleCard để tổ chức thông tin
✅ Responsive design
✅ Internationalization (i18n)
