/**
 * Demo page để test ChatPanel với cả hai mode: demo và websocket
 */

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button } from '@/shared/components/common';
import { ChatPanel } from '@/shared/components/layout/chat-panel';
import { WebappChatConfig } from '@/shared/services/webapp-chat-websocket.service';

const ChatPanelModeDemo: React.FC = () => {
  const { t } = useTranslation(['common', 'chat']);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [mode, setMode] = useState<'demo' | 'websocket'>('demo');
  const [config, setConfig] = useState<WebappChatConfig>({
    url: import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:3001',
    namespace: 'webapp-chat',
    auth: {
      token: localStorage.getItem('access_token') || undefined,
    },
    autoConnect: true,
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    timeout: 20000,
  });

  const handleOpenChat = () => {
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
  };

  const handleKeywordDetected = (keyword: string) => {
    console.log('Keyword detected:', keyword);
  };

  const handleModeChange = (newMode: 'demo' | 'websocket') => {
    setMode(newMode);
    // Đóng chat khi đổi mode để reset state
    setIsChatOpen(false);
  };

  return (
    <div className="w-full bg-background text-foreground min-h-screen">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Typography variant="h1" className="mb-4">
            ChatPanel Mode Demo
          </Typography>
          <Typography variant="body1" className="text-gray-600 dark:text-gray-400">
            Demo để test ChatPanel với cả hai mode: Demo (simulation) và WebSocket (real-time)
          </Typography>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Control Panel */}
          <div className="lg:col-span-1">
            <Card className="p-6">
              <Typography variant="h3" className="mb-4">
                Điều khiển
              </Typography>

              {/* Mode Selection */}
              <div className="mb-6">
                <Typography variant="h4" className="mb-3">
                  Chế độ Chat
                </Typography>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="mode"
                      value="demo"
                      checked={mode === 'demo'}
                      onChange={() => handleModeChange('demo')}
                      className="rounded"
                    />
                    <span>{t('chat:demoMode')}</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="mode"
                      value="websocket"
                      checked={mode === 'websocket'}
                      onChange={() => handleModeChange('websocket')}
                      className="rounded"
                    />
                    <span>{t('chat:websocketMode')}</span>
                  </label>
                </div>
              </div>

              {/* WebSocket Configuration (only show when websocket mode) */}
              {mode === 'websocket' && (
                <div className="mb-6">
                  <Typography variant="h4" className="mb-3">
                    Cấu hình WebSocket
                  </Typography>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        WebSocket URL
                      </label>
                      <input
                        type="text"
                        value={config.url}
                        onChange={(e) => setConfig(prev => ({ ...prev, url: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm"
                        placeholder="ws://localhost:3001"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Namespace
                      </label>
                      <input
                        type="text"
                        value={config.namespace || ''}
                        onChange={(e) => setConfig(prev => ({ ...prev, namespace: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm"
                        placeholder="webapp-chat"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Auth Token
                      </label>
                      <input
                        type="text"
                        value={config.auth?.token || ''}
                        onChange={(e) => setConfig(prev => ({ 
                          ...prev, 
                          auth: { ...prev.auth, token: e.target.value }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm"
                        placeholder="JWT Token"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Chat Control */}
              <div>
                <Button
                  onClick={handleOpenChat}
                  variant="primary"
                  className="w-full"
                  disabled={isChatOpen}
                >
                  {isChatOpen ? 'Chat đang mở' : 'Mở Chat'}
                </Button>
              </div>
            </Card>

            {/* Info Panel */}
            <Card className="p-6 mt-6">
              <Typography variant="h3" className="mb-4">
                Thông tin Mode
              </Typography>
              
              <div className="space-y-3">
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                  <Typography variant="h4" className="text-blue-700 dark:text-blue-300 mb-2">
                    {mode === 'demo' ? t('chat:demoMode') : t('chat:websocketMode')}
                  </Typography>
                  <Typography variant="body2" className="text-blue-600 dark:text-blue-400">
                    {mode === 'demo' 
                      ? t('chat:simulatedChat') + ' - Tin nhắn được mô phỏng với delay 1 giây'
                      : t('chat:realTimeChat') + ' - Kết nối thực với AI agent qua WebSocket'
                    }
                  </Typography>
                </div>

                {mode === 'websocket' && (
                  <div className="space-y-2 text-sm">
                    <div>
                      <strong>Backend URL:</strong> {config.url}/{config.namespace}
                    </div>
                    <div>
                      <strong>Events:</strong> webapp_chat:*
                    </div>
                    <div>
                      <strong>Auth:</strong> {config.auth?.token ? 'Có token' : 'Không có token'}
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </div>

          {/* Chat Panel */}
          <div className="lg:col-span-2">
            {isChatOpen ? (
              <Card className="h-[600px] overflow-hidden">
                <ChatPanel
                  onClose={handleCloseChat}
                  onKeywordDetected={handleKeywordDetected}
                  websocketConfig={mode === 'websocket' ? config : undefined}
                  mode={mode}
                />
              </Card>
            ) : (
              <Card className="p-8 text-center h-[600px] flex items-center justify-center">
                <div>
                  <Typography variant="h3" className="mb-4 text-gray-500 dark:text-gray-400">
                    Chat chưa được mở
                  </Typography>
                  <Typography variant="body1" className="text-gray-400 dark:text-gray-500 mb-6">
                    Chọn mode và nhấn "Mở Chat" để bắt đầu test
                  </Typography>
                  <Button onClick={handleOpenChat} variant="primary">
                    Mở Chat
                  </Button>
                </div>
              </Card>
            )}
          </div>
        </div>

        {/* Instructions */}
        <Card className="p-6 mt-8">
          <Typography variant="h3" className="mb-4">
            Hướng dẫn sử dụng
          </Typography>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Typography variant="h4" className="mb-3 text-blue-600 dark:text-blue-400">
                Demo Mode
              </Typography>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li>• Chat mô phỏng không cần backend</li>
                <li>• AI response được tạo tự động</li>
                <li>• Delay 1 giây để mô phỏng thời gian xử lý</li>
                <li>• Phù hợp để test UI/UX</li>
                <li>• Không có typing indicators thực</li>
              </ul>
            </div>

            <div>
              <Typography variant="h4" className="mb-3 text-green-600 dark:text-green-400">
                WebSocket Mode
              </Typography>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li>• Kết nối thực với backend WebSocket</li>
                <li>• AI streaming response real-time</li>
                <li>• Typing indicators cho user và AI</li>
                <li>• Auto-reconnection khi mất kết nối</li>
                <li>• Cần backend chạy tại URL đã cấu hình</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-md">
            <Typography variant="body2" className="text-yellow-700 dark:text-yellow-300">
              <strong>Lưu ý:</strong> Để test WebSocket mode, đảm bảo backend đang chạy và có thể kết nối được. 
              Mở Developer Console để xem logs WebSocket và debug các sự kiện.
            </Typography>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ChatPanelModeDemo;
