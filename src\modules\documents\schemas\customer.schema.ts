import { z } from 'zod';
import { TFunction } from 'i18next';
import { UserConvertCustomerSortField, SortDirection } from '../types/customer.types';

/**
 * Schema validation cho form khách hàng với đa ngôn ngữ
 */
export const createCustomerFormSchema = (t: TFunction) => z.object({
  name: z
    .string()
    .min(1, t('validation:required', { field: t('business:customer.form.name') }))
    .min(2, t('validation:minLength', { field: t('business:customer.form.name'), length: 2 }))
    .max(100, t('validation:maxLength', { field: t('business:customer.form.name'), length: 100 })),
  email: z
    .string()
    .min(1, t('validation:required', { field: t('business:customer.form.email') }))
    .email(t('validation:email')),
  phone: z
    .string()
    .min(1, t('validation:required', { field: t('business:customer.form.phone') }))
    .regex(/^[0-9+\-\s()]+$/, t('validation:phone'))
    .min(10, t('validation:minLength', { field: t('business:customer.form.phone'), length: 10 })),
  tags: z
    .union([
      z.string(),
      z.array(z.string())
    ])
    .optional()
    .transform((val) => {
      if (Array.isArray(val)) {
        return val.join(', ');
      }
      return val || '';
    }),
});

/**
 * Schema validation cho query khách hàng
 */
export const customerQuerySchema = z.object({
  page: z.coerce.number().int().positive().optional().default(1),
  limit: z.coerce.number().int().positive().optional().default(10),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional(),
  status: z.string().optional(),
});

/**
 * Schema validation cho query khách hàng chuyển đổi
 */
export const convertCustomerQuerySchema = z.object({
  page: z.coerce.number().int().positive().optional().default(1),
  limit: z.coerce.number().int().positive().optional().default(10),
  search: z.string().optional(),
  platform: z.string().optional(),
  agentId: z.string().optional(),
  sortBy: z.nativeEnum(UserConvertCustomerSortField).optional().default(UserConvertCustomerSortField.CREATED_AT),
  sortDirection: z.nativeEnum(SortDirection).optional().default(SortDirection.DESC),
});

/**
 * Schema validation cho cập nhật khách hàng
 */
export const createUpdateCustomerSchema = (t: TFunction) => z.object({
  name: z
    .string()
    .min(2, t('validation:minLength', { field: t('business:customer.form.name'), length: 2 }))
    .max(100, t('validation:maxLength', { field: t('business:customer.form.name'), length: 100 }))
    .optional(),
  email: z
    .string()
    .email(t('validation:email'))
    .optional(),
  phone: z
    .string()
    .regex(/^[0-9+\-\s()]+$/, t('validation:phone'))
    .min(10, t('validation:minLength', { field: t('business:customer.form.phone'), length: 10 }))
    .optional(),
  tags: z
    .union([
      z.string(),
      z.array(z.string())
    ])
    .optional()
    .transform((val) => {
      if (Array.isArray(val)) {
        return val.join(', ');
      }
      return val || '';
    }),
});

/**
 * Schema validation cho cập nhật thông tin cơ bản khách hàng
 */
export const createUpdateCustomerBasicInfoSchema = (t: TFunction) => z.object({
  name: z
    .string()
    .min(1, t('validation:required', { field: t('business:common.form.name') }))
    .min(2, t('validation:minLength', { field: t('business:common.form.name'), length: 2 }))
    .max(100, t('validation:maxLength', { field: t('business:common.form.name'), length: 100 })),
  phone: z
    .string()
    .min(1, t('validation:required', { field: t('business:common.form.phone') }))
    .regex(/^[0-9+\-\s()]+$/, t('validation:phone'))
    .min(10, t('validation:minLength', { field: t('business:common.form.phone'), length: 10 })),
  email: z.object({
    primary: z
      .string()
      .min(1, t('validation:required', { field: t('business:common.form.email') }))
      .email(t('validation:email')),
    secondary: z
      .string()
      .email(t('validation:email'))
      .optional()
      .or(z.literal(''))
  }),
  address: z
    .string()
    .max(255, t('validation:maxLength', { field: t('business:common.form.address'), length: 255 }))
    .optional(),
  avatarFile: z.object({
    fileName: z.string(),
    mimeType: z.string()
  }).optional()
});

/**
 * Type cho form values cập nhật thông tin cơ bản
 */
export type UpdateCustomerBasicInfoFormValues = z.infer<ReturnType<typeof createUpdateCustomerBasicInfoSchema>>;

/**
 * Schema validation cho cập nhật social links khách hàng
 */
export const createUpdateCustomerSocialLinksSchema = (t: TFunction) => z.object({
  facebookLink: z
    .string()
    .url(t('validation:url'))
    .optional()
    .or(z.literal('')),
  twitterLink: z
    .string()
    .url(t('validation:url'))
    .optional()
    .or(z.literal('')),
  linkedinLink: z
    .string()
    .url(t('validation:url'))
    .optional()
    .or(z.literal('')),
  zaloLink: z
    .string()
    .optional()
    .or(z.literal('')), // Zalo có thể là số điện thoại hoặc URL
  websiteLink: z
    .string()
    .url(t('validation:url'))
    .optional()
    .or(z.literal(''))
});

/**
 * Type cho form values cập nhật social links
 */
export type UpdateCustomerSocialLinksFormValues = z.infer<ReturnType<typeof createUpdateCustomerSocialLinksSchema>>;

/**
 * Type definitions
 */
export type CustomerFormValues = z.infer<ReturnType<typeof createCustomerFormSchema>>;
export type CustomerQueryParams = z.infer<typeof customerQuerySchema>;
export type ConvertCustomerQueryParams = z.infer<typeof convertCustomerQuerySchema>;
export type UpdateCustomerValues = z.infer<ReturnType<typeof createUpdateCustomerSchema>>;
