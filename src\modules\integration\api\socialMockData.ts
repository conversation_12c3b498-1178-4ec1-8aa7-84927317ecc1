import { SocialNetwork, SocialNetworksResponse, SocialNetworksParams } from '../types/social';

/**
 * <PERSON><PERSON> sách mạng xã hội mẫu
 */
const mockSocialNetworks: SocialNetwork[] = [
  {
    id: 'facebook',
    name: 'Facebook',
    code: 'facebook',
    url: 'https://facebook.com',
    color: '#1877F2',
    order: 1,
    active: true,
  },
  {
    id: 'zalo',
    name: 'Zalo',
    code: 'zalo',
    url: 'https://zalo.me',
    color: '#0068FF',
    order: 2,
    active: true,
  },
  {
    id: 'threads',
    name: 'Threads',
    code: 'threads',
    url: 'https://threads.net',
    color: '#000000',
    order: 3,
    active: true,
  },
  {
    id: 'instagram',
    name: 'Instagram',
    code: 'instagram',
    url: 'https://instagram.com',
    color: '#E4405F',
    order: 4,
    active: true,
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    code: 'linkedin',
    url: 'https://linkedin.com',
    color: '#0A66C2',
    order: 5,
    active: true,
  },
  {
    id: 'tiktok',
    name: 'Tik<PERSON><PERSON>',
    code: 'tiktok',
    url: 'https://tiktok.com',
    color: '#000000',
    order: 6,
    active: true,
  },
  {
    id: 'telegram',
    name: 'Telegram',
    code: 'telegram',
    url: 'https://telegram.org',
    color: '#26A5E4',
    order: 7,
    active: true,
  },
  {
    id: 'website',
    name: 'Website',
    code: 'website',
    url: 'https://example.com',
    color: '#FF5722',
    order: 8,
    active: true,
  },
  {
    id: 'youtube',
    name: 'YouTube',
    code: 'youtube',
    url: 'https://youtube.com',
    color: '#FF0000',
    order: 9,
    active: false,
  },
  {
    id: 'twitter',
    name: 'Twitter',
    code: 'twitter',
    url: 'https://twitter.com',
    color: '#1DA1F2',
    order: 10,
    active: false,
  },
];

/**
 * Lấy danh sách mạng xã hội
 */
export const fetchSocialNetworks = async (
  params?: SocialNetworksParams
): Promise<SocialNetworksResponse> => {
  // Giả lập độ trễ của API
  await new Promise(resolve => setTimeout(resolve, 800));

  // Lọc theo trạng thái kích hoạt nếu có
  let filteredNetworks = [...mockSocialNetworks];

  if (params?.activeOnly) {
    filteredNetworks = filteredNetworks.filter(network => network.active);
  }

  // Sắp xếp theo thứ tự nếu có
  if (params?.sortByOrder) {
    filteredNetworks.sort((a, b) => (a.order || 999) - (b.order || 999));
  }

  return {
    items: filteredNetworks,
    total: filteredNetworks.length,
  };
};

/**
 * Lấy thông tin chi tiết của mạng xã hội theo ID
 */
export const fetchSocialNetworkById = async (id: string): Promise<SocialNetwork | null> => {
  // Giả lập độ trễ của API
  await new Promise(resolve => setTimeout(resolve, 500));

  const network = mockSocialNetworks.find(net => net.id === id);
  return network || null;
};

/**
 * Cập nhật URL của mạng xã hội
 */
export const updateSocialNetworkUrl = async (
  id: string,
  url: string
): Promise<SocialNetwork | null> => {
  // Giả lập độ trễ của API
  await new Promise(resolve => setTimeout(resolve, 700));

  const network = mockSocialNetworks.find(net => net.id === id);

  if (!network) {
    return null;
  }

  network.url = url;
  return network;
};

/**
 * Cập nhật trạng thái kích hoạt của mạng xã hội
 */
export const updateSocialNetworkStatus = async (
  id: string,
  active: boolean
): Promise<SocialNetwork | null> => {
  // Giả lập độ trễ của API
  await new Promise(resolve => setTimeout(resolve, 600));

  const network = mockSocialNetworks.find(net => net.id === id);

  if (!network) {
    return null;
  }

  network.active = active;
  return network;
};
