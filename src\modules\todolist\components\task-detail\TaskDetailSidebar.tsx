import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Typography,
  Select,
  RatingSelector,
  AsyncSelectWithPagination
} from '@/shared/components/common';

import { TaskDto, TaskStatus, TaskPriority } from '../../types/task.types';
import { UserService, UserQueryDto } from '../../services/userService';
import { TodoService, ScoreTodoDto } from '../../services/todoService';

interface TaskDetailSidebarProps {
  task: TaskDto;
  onUpdateStatus: (status: TaskStatus) => void;
  onUpdatePriority: (priority: TaskPriority) => void;
  onUpdateAssignee: (assigneeId: string) => void;
  onUpdateExpectedStars: (stars: number) => void;
  onRefresh?: () => void;
}

/**
 * Task detail sidebar component
 */
const TaskDetailSidebar: React.FC<TaskDetailSidebarProps> = ({
  task,
  onUpdateStatus,
  onUpdatePriority,
  onUpdateAssignee,
  onUpdateExpectedStars,
}) => {
  const { t } = useTranslation(['todolist']);

  // Status options for Select
  const statusOptions = useMemo(() => [
    { value: TaskStatus.PENDING, label: t('todolist:task.status.todo', 'To Do') },
    { value: TaskStatus.IN_PROGRESS, label: t('todolist:task.status.inProgress', 'In Progress') },
    { value: TaskStatus.COMPLETED, label: t('todolist:task.status.done', 'Done') },
    { value: TaskStatus.REJECTED, label: t('todolist:task.status.cancelled', 'Cancelled') },
  ], [t]);

  // Priority options for Select
  const priorityOptions = useMemo(() => [
    { value: TaskPriority.LOW, label: t('todolist:task.priority.low', 'Low') },
    { value: TaskPriority.MEDIUM, label: t('todolist:task.priority.medium', 'Medium') },
    { value: TaskPriority.HIGH, label: t('todolist:task.priority.high', 'High') },
    { value: TaskPriority.URGENT, label: t('todolist:task.priority.urgent', 'Urgent') },
  ], [t]);

  // Load users for assignee selection
  const loadUsers = async ({ search, page = 1, limit = 20 }) => {
    try {
      const query: UserQueryDto = {
        page,
        limit,
        search,
        hasEmployee: true, // Only users with employee records
      };

      const response = await UserService.findAllUsers(query);

      // Transform API response to AsyncSelectWithPagination format
      const transformedItems = response.data.items.map(user => ({
        value: user.id.toString(),
        label: user.fullName || user.email,
        data: {
          email: user.email,
          employeeCode: user.employeeCode,
          departmentName: user.departmentName,
          position: user.position,
        }
      }));

      return {
        items: transformedItems,
        totalItems: response.data.totalItems,
        totalPages: response.data.totalPages,
        currentPage: response.data.currentPage,
      };
    } catch (error) {
      console.error('Error loading users:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }
  };

  return (
    <div className="p-4">
      <Typography variant="h6" className="mb-4">
        {t('todolist:task.details.title', 'Task Details')}
      </Typography>

      <div className="space-y-6">
        {/* Status */}
        <div>
          <Typography variant="subtitle2" className="text-gray-500 dark:text-gray-400 mb-2">
            {t('todolist:task.fields.status', 'Status')}
          </Typography>
          <Select
            value={task.status}
            onChange={(value) => onUpdateStatus(value as TaskStatus)}
            options={statusOptions}
            placeholder={t('todolist:task.selectStatus', 'Select status')}
            fullWidth
          />
        </div>

        {/* Priority */}
        <div>
          <Typography variant="subtitle2" className="text-gray-500 dark:text-gray-400 mb-2">
            {t('todolist:task.fields.priority', 'Priority')}
          </Typography>
          <Select
            value={task.priority}
            onChange={(value) => onUpdatePriority(value as TaskPriority)}
            options={priorityOptions}
            placeholder={t('todolist:task.selectPriority', 'Select priority')}
            fullWidth
          />
        </div>

        {/* Assignee */}
        <div>
          <Typography variant="subtitle2" className="text-gray-500 dark:text-gray-400 mb-2">
            {t('todolist:task.fields.assignee', 'Assignee')}
          </Typography>
          <AsyncSelectWithPagination
            value={task.assigneeId?.toString()}
            onChange={(value) => {
              if (value) {
                onUpdateAssignee(value.toString());
              }
            }}
            loadOptions={loadUsers}
            placeholder={t('todolist:task.searchAssignee', 'Search for user...')}
            fullWidth
          />
        </div>

        {/* Expected Stars */}
        <RatingSelector
          value={task.expectedStars || 0}
          onChange={onUpdateExpectedStars}
          label={t('todolist:task.fields.expectedStars', 'Expected Stars')}
          size="lg"
          interactive={true}
        />

        {/* Awarded Stars */}
        {task.awardedStars && (
          <RatingSelector
            value={task.awardedStars}
            onChange={() => {}} // Read-only
            label={t('todolist:task.fields.awardedStars', 'Awarded Stars')}
            size="lg"
            interactive={false}
          />
        )}
      </div>
    </div>
  );
};

export default TaskDetailSidebar;
