import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Card,
  Loading,
} from '@/shared/components/common';
import { OTPInput } from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';
import { useSendOTP, useVerifyOTP } from '../hooks/useBankAccount';
import { BankAccountDto, getOTPLength, getBankInfo } from '../types/bank-account.types';

interface OTPVerificationFormProps {
  /**
   * Thông tin tài khoản ngân hàng cần xác thực
   */
  bankAccount: BankAccountDto;

  /**
   * Callback khi xác thực thành công
   */
  onSuccess: (bankAccount: BankAccountDto) => void;

  /**
   * Callback khi hủy
   */
  onCancel: () => void;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component form xác thực OTP cho tài khoản ngân hàng
 */
const OTPVerificationForm: React.FC<OTPVerificationFormProps> = ({
  bankAccount,
  onSuccess,
  onCancel,
  className = '',
}) => {
  const { t } = useTranslation(['integration', 'common']);

  // State
  const [otpCode, setOtpCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [canResend, setCanResend] = useState(false);

  // Hooks
  const sendOTPMutation = useSendOTP();
  const verifyOTPMutation = useVerifyOTP();

  // Lấy thông tin ngân hàng và độ dài OTP
  const bankInfo = getBankInfo(bankAccount.bankCode);
  const otpLength = getOTPLength(bankAccount.bankCode);

  // Effect để đếm ngược thời gian gửi lại OTP
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
    // Return undefined for the else case to satisfy TypeScript
    return undefined;
  }, [countdown]);

  // Gửi OTP lần đầu khi component mount
  useEffect(() => {
    handleSendOTP();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /**
   * Xử lý gửi OTP
   */
  const handleSendOTP = async () => {
    try {
      await sendOTPMutation.mutateAsync(bankAccount.id);

      NotificationUtil.success({
        title: t('integration:bankAccount.otpSent', 'Mã OTP đã được gửi'),
        message: t(
          'integration:bankAccount.otpSentDescription',
          'Vui lòng kiểm tra tin nhắn SMS trên điện thoại của bạn'
        ),
      });

      // Bắt đầu đếm ngược 60 giây
      setCountdown(60);
      setCanResend(false);
      setOtpCode('');
    } catch (error) {
      console.error('Error sending OTP:', error);
      NotificationUtil.error({
        title: t('integration:bankAccount.otpSendError', 'Gửi OTP thất bại'),
        message: t(
          'integration:bankAccount.otpSendErrorDescription',
          'Không thể gửi mã OTP. Vui lòng thử lại sau.'
        ),
      });
    }
  };

  /**
   * Xử lý xác thực OTP
   */
  const handleVerifyOTP = async () => {
    if (otpCode.length !== otpLength) {
      NotificationUtil.warning({
        title: t('integration:bankAccount.invalidOtpLength', 'Mã OTP không đúng độ dài'),
        message: t(
          'integration:bankAccount.invalidOtpLengthDescription',
          `Mã OTP phải có ${otpLength} ký tự`
        ),
      });
      return;
    }

    try {
      const response = await verifyOTPMutation.mutateAsync({
        bankAccountId: bankAccount.id,
        otpCode,
      });

      NotificationUtil.success({
        title: t('integration:bankAccount.otpVerifySuccess', 'Xác thực thành công'),
        message: t(
          'integration:bankAccount.otpVerifySuccessDescription',
          'Tài khoản ngân hàng đã được kích hoạt thành công'
        ),
      });

      onSuccess(response.result);
    } catch (error) {
      console.error('Error verifying OTP:', error);
      NotificationUtil.error({
        title: t('integration:bankAccount.otpVerifyError', 'Xác thực thất bại'),
        message: t(
          'integration:bankAccount.otpVerifyErrorDescription',
          'Mã OTP không đúng hoặc đã hết hạn. Vui lòng thử lại.'
        ),
      });
      setOtpCode('');
    }
  };

  /**
   * Xử lý thay đổi OTP
   */
  const handleOTPChange = (value: string) => {
    setOtpCode(value);
  };

  /**
   * Xử lý hoàn thành nhập OTP
   */
  const handleOTPComplete = (value: string) => {
    setOtpCode(value);
    // Tự động xác thực khi nhập đủ
    if (value.length === otpLength) {
      setTimeout(() => {
        handleVerifyOTP();
      }, 500);
    }
  };

  return (
    <Card className={`p-6 max-w-md mx-auto ${className}`}>
      <div className="text-center mb-6">
        <Typography variant="h5" className="mb-2">
          {t('integration:bankAccount.otpVerification', 'Xác thực OTP')}
        </Typography>
        <Typography variant="body2" color="muted">
          {t(
            'integration:bankAccount.otpVerificationDescription',
            'Nhập mã OTP được gửi đến số điện thoại đã đăng ký với {{bankName}}',
            { bankName: bankInfo?.label || bankAccount.bankName }
          )}
        </Typography>
      </div>

      {/* Thông tin tài khoản */}
      <div className="bg-muted rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center mb-2">
          <Typography variant="caption" color="muted">
            {t('integration:bankAccount.bankName', 'Ngân hàng')}
          </Typography>
          <Typography variant="body2" className="font-medium">
            {bankAccount.bankName}
          </Typography>
        </div>
        <div className="flex justify-between items-center mb-2">
          <Typography variant="caption" color="muted">
            {t('integration:bankAccount.accountNumber', 'Số tài khoản')}
          </Typography>
          <Typography variant="body2" className="font-medium">
            {bankAccount.accountNumber}
          </Typography>
        </div>
        <div className="flex justify-between items-center">
          <Typography variant="caption" color="muted">
            {t('integration:bankAccount.phoneNumber', 'Số điện thoại')}
          </Typography>
          <Typography variant="body2" className="font-medium">
            {bankAccount.phoneNumber.replace(/(\d{4})(\d{3})(\d{3})/, '$1***$3')}
          </Typography>
        </div>
      </div>

      {/* OTP Input */}
      <div className="mb-6">
        <Typography variant="body1" className="mb-4 text-center">
          {t('integration:bankAccount.enterOtp', 'Nhập mã OTP')}
        </Typography>
        <OTPInput
          length={otpLength}
          value={otpCode}
          onChange={handleOTPChange}
          onComplete={handleOTPComplete}
          disabled={verifyOTPMutation.isPending}
          autoFocus
        />
      </div>

      {/* Resend OTP */}
      <div className="text-center mb-6">
        {countdown > 0 ? (
          <Typography variant="body2" color="muted">
            {t(
              'integration:bankAccount.resendOtpCountdown',
              'Gửi lại mã sau {{countdown}} giây',
              { countdown }
            )}
          </Typography>
        ) : (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSendOTP}
            disabled={!canResend || sendOTPMutation.isPending}
            isLoading={sendOTPMutation.isPending}
          >
            {t('integration:bankAccount.resendOtp', 'Gửi lại mã OTP')}
          </Button>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-3">
        <Button
          variant="secondary"
          fullWidth
          onClick={onCancel}
          disabled={verifyOTPMutation.isPending}
        >
          {t('common:cancel', 'Hủy')}
        </Button>
        <Button
          variant="primary"
          fullWidth
          onClick={handleVerifyOTP}
          disabled={otpCode.length !== otpLength || verifyOTPMutation.isPending}
          isLoading={verifyOTPMutation.isPending}
        >
          {t('integration:bankAccount.verify', 'Xác thực')}
        </Button>
      </div>

      {/* Loading overlay */}
      {(sendOTPMutation.isPending || verifyOTPMutation.isPending) && (
        <div className="absolute inset-0 bg-background/50 flex items-center justify-center rounded-lg">
          <Loading />
        </div>
      )}
    </Card>
  );
};

export default OTPVerificationForm;
