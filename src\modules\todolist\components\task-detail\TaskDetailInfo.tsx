import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Typo<PERSON>, Divider, Textarea, Button } from '@/shared/components/common';
import { formatTimestamp } from '@/shared/utils/form-date-utils';

import { TaskDto } from '../../types/task.types';

interface TaskDetailInfoProps {
  task: TaskDto;
  onUpdateDescription?: (description: string) => void;
}

/**
 * Task detail information component
 */
const TaskDetailInfo: React.FC<TaskDetailInfoProps> = ({ task, onUpdateDescription }) => {
  const { t } = useTranslation(['todolist']);
  const [isEditing, setIsEditing] = useState(false);
  const [description, setDescription] = useState(task.description || '');

  const handleSave = () => {
    if (onUpdateDescription) {
      onUpdateDescription(description);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setDescription(task.description || '');
    setIsEditing(false);
  };

  return (
    <div className="space-y-4">
      <div>
        <div className="flex justify-between items-center mb-2">
          <Typography variant="h6">
            {t('todolist:task.fields.description', 'Description')}
          </Typography>
          {!isEditing && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
            >
              {t('common:edit', 'Edit')}
            </Button>
          )}
        </div>

        {isEditing ? (
          <div className="space-y-3">
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder={t('todolist:task.descriptionPlaceholder', 'Enter task description...')}
              rows={4}
              autoSize={{ minRows: 4, maxRows: 10 }}
              fullWidth
            />
            <div className="flex justify-end space-x-2">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                {t('common:cancel', 'Cancel')}
              </Button>
              <Button variant="primary" size="sm" onClick={handleSave}>
                {t('common:save', 'Save')}
              </Button>
            </div>
          </div>
        ) : (
          <div className="min-h-[100px] p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
            <Typography variant="body1" className="whitespace-pre-wrap">
              {task.description || t('todolist:task.noDescription', 'No description provided')}
            </Typography>
          </div>
        )}
      </div>

      <Divider />

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Typography variant="subtitle2" className="text-gray-500">
            {t('todolist:task.fields.createdAt', 'Created At')}
          </Typography>
          <Typography variant="body2">{formatTimestamp(task.createdAt || 0)}</Typography>
        </div>
        <div>
          <Typography variant="subtitle2" className="text-gray-500">
            {t('todolist:task.fields.updatedAt', 'Updated At')}
          </Typography>
          <Typography variant="body2">{formatTimestamp(task.updatedAt || 0)}</Typography>
        </div>
        {task.completedAt && (
          <div>
            <Typography variant="subtitle2" className="text-gray-500">
              {t('todolist:task.fields.completedAt', 'Completed At')}
            </Typography>
            <Typography variant="body2">{formatTimestamp(task.completedAt)}</Typography>
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskDetailInfo;
