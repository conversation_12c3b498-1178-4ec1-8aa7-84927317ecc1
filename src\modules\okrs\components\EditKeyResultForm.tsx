import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import {
  Button,
  Card,
  Form,
  FormGrid,
  FormItem,
  Input,
  Select,
  Textarea,
  Typography,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { useFormErrors } from '@/shared/hooks';
import { NotificationUtil } from '@/shared/utils/notification';

import { loadKeyResultsForAsyncSelect, useUpdateKeyResult } from '../hooks/useKeyResults';
import {
  CheckInFrequency,
  KeyResultDto,
  KeyResultStatus,
  UpdateKeyResultDto,
} from '../types/key-result.types';

interface EditKeyResultFormProps {
  keyResult: KeyResultDto;
  onSuccess?: () => void;
  onCancel?: () => void;
}

// Schema validation cho form
const editKeyResultSchema = z.object({
  title: z.string().min(1, 'Tiêu đề không được để trống'),
  description: z.string().optional(),
  targetValue: z.number().min(0, 'Giá trị mục tiêu phải >= 0'),
  currentValue: z.number().min(0, 'Giá trị hiện tại phải >= 0').optional(),
  startValue: z.number().min(0, 'Giá trị ban đầu phải >= 0').optional(),
  unit: z.string().optional(),
  format: z.string().optional(),
  status: z.nativeEnum(KeyResultStatus).optional(),
  measurementMethod: z.string().optional(),
  weight: z.number().min(0).max(100, 'Trọng số phải từ 0-100').optional(),
  checkInFrequency: z.nativeEnum(CheckInFrequency).optional(),
  supportingKeyResultId: z.number().optional(),
});

type EditKeyResultFormData = z.infer<typeof editKeyResultSchema>;

/**
 * Form chỉnh sửa Key Result
 */
const EditKeyResultForm: React.FC<EditKeyResultFormProps> = ({
  keyResult,
  onSuccess,
  onCancel,
}) => {
  const { t } = useTranslation(['okrs', 'common']);
  const { formRef, setFormErrors } = useFormErrors<EditKeyResultFormData>();

  // Hook để cập nhật key result
  const updateKeyResultMutation = useUpdateKeyResult();

  // Default values từ keyResult hiện tại
  const defaultValues: EditKeyResultFormData = {
    title: keyResult.title || '',
    description: keyResult.description || '',
    targetValue: keyResult.targetValue || 0,
    currentValue: keyResult.currentValue || 0,
    startValue: keyResult.startValue || 0,
    unit: keyResult.unit || '',
    format: keyResult.format || 'number',
    status: keyResult.status || KeyResultStatus.ACTIVE,
    measurementMethod: keyResult.measurementMethod || '',
    weight: keyResult.weight || 100,
    checkInFrequency: keyResult.checkInFrequency || CheckInFrequency.WEEKLY,
    supportingKeyResultId: undefined,
  };

  // Xử lý submit form
  const handleSubmit = async (values: EditKeyResultFormData) => {
    try {
      const updateData: UpdateKeyResultDto = {
        title: values.title,
        description: values.description,
        targetValue: values.targetValue,
        currentValue: values.currentValue,
        startValue: values.startValue,
        unit: values.unit,
        format: values.format,
        status: values.status,
        measurementMethod: values.measurementMethod,
        weight: values.weight,
        checkInFrequency: values.checkInFrequency,
      };

      await updateKeyResultMutation.mutateAsync({
        id: keyResult.id,
        data: updateData,
      });

      NotificationUtil.success({
        message: t('okrs:keyResult.updateSuccess', 'Cập nhật Key Result thành công'),
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      console.error('Error updating key result:', error);
      
      // Xử lý lỗi validation từ API
      if (error?.response?.data?.errors) {
        setFormErrors(error.response.data.errors);
      } else {
        NotificationUtil.error({
          message: t('okrs:keyResult.updateError', 'Có lỗi xảy ra khi cập nhật Key Result'),
        });
      }
    }
  };

  return (
    <Card allowOverflow={true}>
      <Typography variant="h4" className="mb-4">
        {t('okrs:keyResult.editTitle', 'Chỉnh sửa Key Result')}
      </Typography>

      <Form
        ref={formRef}
        schema={editKeyResultSchema}
        onSubmit={handleSubmit}
        defaultValues={defaultValues}
      >
        <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
          <FormItem
            name="title"
            label={t('okrs:keyResult.form.title', 'Tiêu đề')}
            required
            className="col-span-full"
          >
            <Input
              placeholder={t('okrs:keyResult.form.titlePlaceholder', 'Nhập tiêu đề kết quả chính')}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('okrs:keyResult.form.description', 'Mô tả')}
            className="col-span-full"
          >
            <Textarea
              rows={3}
              placeholder={t(
                'okrs:keyResult.form.descriptionPlaceholder',
                'Mô tả chi tiết về kết quả chính'
              )}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="targetValue"
            label={t('okrs:keyResult.form.targetValue', 'Giá trị mục tiêu')}
            required
            className="col-span-full md:col-span-1"
          >
            <Input type="number" fullWidth />
          </FormItem>

          <FormItem
            name="currentValue"
            label={t('okrs:keyResult.form.currentValue', 'Giá trị hiện tại')}
            className="col-span-full md:col-span-1"
          >
            <Input type="number" fullWidth />
          </FormItem>

          <FormItem
            name="startValue"
            label={t('okrs:keyResult.form.startValue', 'Giá trị ban đầu')}
            className="col-span-full md:col-span-1"
          >
            <Input type="number" fullWidth />
          </FormItem>

          <FormItem
            name="unit"
            label={t('okrs:keyResult.form.unit', 'Đơn vị')}
            className="col-span-full md:col-span-1"
          >
            <Input
              placeholder={t('okrs:keyResult.form.unitPlaceholder', 'VD: khách hàng, đơn hàng, %')}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="format"
            label={t('okrs:keyResult.form.format', 'Định dạng hiển thị')}
            className="col-span-full md:col-span-1"
          >
            <div className="relative z-[9999]">
              <Select
                options={[
                  { value: 'number', label: t('okrs:keyResult.format.number', 'Số') },
                  {
                    value: 'percentage',
                    label: t('okrs:keyResult.format.percentage', 'Phần trăm'),
                  },
                  { value: 'currency', label: t('okrs:keyResult.format.currency', 'Tiền tệ') },
                  { value: 'boolean', label: t('okrs:keyResult.format.boolean', 'Có/Không') },
                ]}
                fullWidth
              />
            </div>
          </FormItem>

          <FormItem
            name="status"
            label={t('okrs:keyResult.form.status', 'Trạng thái')}
            className="col-span-full md:col-span-1"
          >
            <div className="relative z-[9999]">
              <Select
                options={[
                  { value: KeyResultStatus.ACTIVE, label: t('okrs:keyResult.status.active', 'Đang hoạt động') },
                  { value: KeyResultStatus.COMPLETED, label: t('okrs:keyResult.status.completed', 'Hoàn thành') },
                  { value: KeyResultStatus.PAUSED, label: t('okrs:keyResult.status.paused', 'Tạm dừng') },
                  { value: KeyResultStatus.CANCELED, label: t('okrs:keyResult.status.canceled', 'Đã hủy') },
                ]}
                fullWidth
              />
            </div>
          </FormItem>

          <FormItem
            name="measurementMethod"
            label={t('okrs:keyResult.form.measurementMethod', 'Phương pháp đo lường')}
            className="col-span-full md:col-span-1"
          >
            <Input
              placeholder={t(
                'okrs:keyResult.form.measurementMethodPlaceholder',
                'VD: Số lượng đăng ký mới'
              )}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="weight"
            label={t('okrs:keyResult.form.weight', 'Trọng số (%)')}
            className="col-span-full md:col-span-1"
          >
            <Input type="number" min={0} max={100} fullWidth />
          </FormItem>

          <FormItem
            name="checkInFrequency"
            label={t('okrs:keyResult.form.checkInFrequency', 'Tần suất cập nhật')}
            className="col-span-full md:col-span-1"
          >
            <div className="relative z-[9999]">
              <Select
                options={[
                  {
                    value: CheckInFrequency.DAILY,
                    label: t('okrs:keyResult.frequency.daily', 'Hàng ngày'),
                  },
                  {
                    value: CheckInFrequency.WEEKLY,
                    label: t('okrs:keyResult.frequency.weekly', 'Hàng tuần'),
                  },
                  {
                    value: CheckInFrequency.MONTHLY,
                    label: t('okrs:keyResult.frequency.monthly', 'Hàng tháng'),
                  },
                ]}
                fullWidth
              />
            </div>
          </FormItem>

          <FormItem
            name="supportingKeyResultId"
            label={t('okrs:keyResult.form.supportingKeyResult', 'Key Result phụ trợ')}
            className="col-span-full md:col-span-1"
          >
            <div className="relative z-[9999]">
              <AsyncSelectWithPagination
                loadOptions={loadKeyResultsForAsyncSelect}
                placeholder={t(
                  'okrs:keyResult.form.supportingKeyResultPlaceholder',
                  'Chọn Key Result phụ trợ'
                )}
                debounceTime={300}
                noOptionsMessage={t('common:noResults', 'Không tìm thấy Key Result')}
                loadingMessage={t('common:loading', 'Đang tìm kiếm...')}
                autoLoadInitial={true}
                itemsPerPage={10}
                fullWidth
              />
            </div>
          </FormItem>
        </FormGrid>

        <div className="mt-6 flex justify-end space-x-3">
          <Button variant="outline" onClick={onCancel}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button type="submit" variant="primary" isLoading={updateKeyResultMutation.isPending}>
            {t('common:save', 'Lưu')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default EditKeyResultForm;
