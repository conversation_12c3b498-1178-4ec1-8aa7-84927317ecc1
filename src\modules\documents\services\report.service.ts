import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import {
  ReportOverviewQueryDto,
  ReportOverviewResponseDto,
  SalesChartQueryDto,
  SalesChartResponseDto,
  OrdersChartQueryDto,
  OrdersChartResponseDto,
  CustomersChartQueryDto,
  CustomersChartResponseDto,
  ProductsChartQueryDto,
  ProductsChartResponseDto,
  TopSellingProductsQueryDto,
  TopSellingProductsResponseDto,
  PotentialCustomersQueryDto,
  PotentialCustomersResponseDto,
  ReportPeriodEnum,
} from '../types/report.types';
import * as reportApi from '../api/report.api';

/**
 * Business Report Services Layer
 * Contains business logic and data transformation
 */

/**
 * Lấy dữ liệu tổng quan báo cáo với business logic
 */
export const getReportOverviewWithBusinessLogic = async (
  params?: ReportOverviewQueryDto
): Promise<ApiResponseDto<ReportOverviewResponseDto>> => {
  // Set default values
  const defaultParams: ReportOverviewQueryDto = {
    period: ReportPeriodEnum.MONTH,
    ...params,
  };

  // Validate date range
  if (defaultParams.startDate && defaultParams.endDate) {
    const startDate = new Date(defaultParams.startDate);
    const endDate = new Date(defaultParams.endDate);
    
    if (endDate < startDate) {
      throw new Error('Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu');
    }
  }

  return reportApi.getReportOverview(defaultParams);
};

/**
 * Lấy dữ liệu biểu đồ doanh thu với business logic
 */
export const getSalesChartWithBusinessLogic = async (
  params?: SalesChartQueryDto
): Promise<ApiResponseDto<SalesChartResponseDto>> => {
  // Set default values
  const defaultParams: SalesChartQueryDto = {
    period: ReportPeriodEnum.MONTH,
    groupBy: 'month',
    ...params,
  };

  // Validate date range
  if (defaultParams.startDate && defaultParams.endDate) {
    const startDate = new Date(defaultParams.startDate);
    const endDate = new Date(defaultParams.endDate);
    
    if (endDate < startDate) {
      throw new Error('Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu');
    }
  }

  return reportApi.getSalesChart(defaultParams);
};

/**
 * Lấy dữ liệu biểu đồ đơn hàng với business logic
 */
export const getOrdersChartWithBusinessLogic = async (
  params?: OrdersChartQueryDto
): Promise<ApiResponseDto<OrdersChartResponseDto>> => {
  // Set default values
  const defaultParams: OrdersChartQueryDto = {
    period: ReportPeriodEnum.MONTH,
    groupBy: 'month',
    ...params,
  };

  // Validate date range
  if (defaultParams.startDate && defaultParams.endDate) {
    const startDate = new Date(defaultParams.startDate);
    const endDate = new Date(defaultParams.endDate);
    
    if (endDate < startDate) {
      throw new Error('Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu');
    }
  }

  return reportApi.getOrdersChart(defaultParams);
};

/**
 * Lấy dữ liệu biểu đồ khách hàng với business logic
 */
export const getCustomersChartWithBusinessLogic = async (
  params?: CustomersChartQueryDto
): Promise<ApiResponseDto<CustomersChartResponseDto>> => {
  // Set default values
  const defaultParams: CustomersChartQueryDto = {
    period: ReportPeriodEnum.MONTH,
    groupBy: 'month',
    ...params,
  };

  // Validate date range
  if (defaultParams.startDate && defaultParams.endDate) {
    const startDate = new Date(defaultParams.startDate);
    const endDate = new Date(defaultParams.endDate);
    
    if (endDate < startDate) {
      throw new Error('Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu');
    }
  }

  return reportApi.getCustomersChart(defaultParams);
};

/**
 * Lấy dữ liệu biểu đồ sản phẩm với business logic
 */
export const getProductsChartWithBusinessLogic = async (
  params?: ProductsChartQueryDto
): Promise<ApiResponseDto<ProductsChartResponseDto>> => {
  // Set default values
  const defaultParams: ProductsChartQueryDto = {
    period: ReportPeriodEnum.MONTH,
    groupBy: 'month',
    limit: 10,
    ...params,
  };

  // Validate limit
  if (defaultParams.limit && (defaultParams.limit < 1 || defaultParams.limit > 100)) {
    throw new Error('Limit phải từ 1 đến 100');
  }

  // Validate date range
  if (defaultParams.startDate && defaultParams.endDate) {
    const startDate = new Date(defaultParams.startDate);
    const endDate = new Date(defaultParams.endDate);
    
    if (endDate < startDate) {
      throw new Error('Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu');
    }
  }

  return reportApi.getProductsChart(defaultParams);
};

/**
 * Lấy danh sách sản phẩm bán chạy với business logic
 */
export const getTopSellingProductsWithBusinessLogic = async (
  params?: TopSellingProductsQueryDto
): Promise<ApiResponseDto<TopSellingProductsResponseDto>> => {
  // Set default values
  const defaultParams: TopSellingProductsQueryDto = {
    period: ReportPeriodEnum.MONTH,
    limit: 10,
    sortBy: 'revenue',
    ...params,
  };

  // Validate limit
  if (defaultParams.limit && (defaultParams.limit < 1 || defaultParams.limit > 50)) {
    throw new Error('Limit phải từ 1 đến 50');
  }

  // Validate date range
  if (defaultParams.startDate && defaultParams.endDate) {
    const startDate = new Date(defaultParams.startDate);
    const endDate = new Date(defaultParams.endDate);
    
    if (endDate < startDate) {
      throw new Error('Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu');
    }
  }

  return reportApi.getTopSellingProducts(defaultParams);
};

/**
 * Lấy danh sách khách hàng tiềm năng với business logic
 */
export const getPotentialCustomersWithBusinessLogic = async (
  params?: PotentialCustomersQueryDto
): Promise<ApiResponseDto<PotentialCustomersResponseDto>> => {
  // Set default values
  const defaultParams: PotentialCustomersQueryDto = {
    period: ReportPeriodEnum.MONTH,
    limit: 10,
    minScore: 0,
    ...params,
  };

  // Validate limit
  if (defaultParams.limit && (defaultParams.limit < 1 || defaultParams.limit > 50)) {
    throw new Error('Limit phải từ 1 đến 50');
  }

  // Validate minScore
  if (defaultParams.minScore && (defaultParams.minScore < 0 || defaultParams.minScore > 100)) {
    throw new Error('Điểm tối thiểu phải từ 0 đến 100');
  }

  // Validate date range
  if (defaultParams.startDate && defaultParams.endDate) {
    const startDate = new Date(defaultParams.startDate);
    const endDate = new Date(defaultParams.endDate);
    
    if (endDate < startDate) {
      throw new Error('Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu');
    }
  }

  return reportApi.getPotentialCustomers(defaultParams);
};

/**
 * Report Service object for easier import
 */
export const ReportService = {
  getReportOverview: getReportOverviewWithBusinessLogic,
  getSalesChart: getSalesChartWithBusinessLogic,
  getOrdersChart: getOrdersChartWithBusinessLogic,
  getCustomersChart: getCustomersChartWithBusinessLogic,
  getProductsChart: getProductsChartWithBusinessLogic,
  getTopSellingProducts: getTopSellingProductsWithBusinessLogic,
  getPotentialCustomers: getPotentialCustomersWithBusinessLogic,
};
