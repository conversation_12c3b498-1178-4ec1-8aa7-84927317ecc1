import { useQuery } from '@tanstack/react-query';
import { SmsProviderQueryParams } from '../types';
import { smsIntegrationService, SmsIntegrationService } from '../services';
import { SMS_INTEGRATION_QUERY_KEYS } from '../constants';

/**
 * Hook for fetching SMS providers list
 */
export const useSmsProviders = (params?: SmsProviderQueryParams) => {
  const queryParams = params ? { ...params } as Record<string, unknown> : {};
  return useQuery({
    queryKey: SMS_INTEGRATION_QUERY_KEYS.PROVIDER_LIST(queryParams),
    queryFn: () => smsIntegrationService.getProvidersWithBusinessLogic(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook for fetching a single SMS provider
 */
export const useSmsProvider = (id: string, enabled = true) => {
  return useQuery({
    queryKey: SMS_INTEGRATION_QUERY_KEYS.PROVIDER(id),
    queryFn: () => SmsIntegrationService.getProvider(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

/**
 * Hook for getting active SMS providers only
 */
export const useActiveSmsProviders = () => {
  return useSmsProviders({
    status: 'active',
    limit: 100, // Get all active providers
    sortBy: 'name',
    sortOrder: 'asc',
  });
};

/**
 * Hook for getting the default SMS provider
 */
export const useDefaultSmsProvider = () => {
  const { data: providersData, ...rest } = useSmsProviders({
    limit: 100,
  });

  const defaultProvider = providersData?.items.find(provider => provider.isDefault);

  return {
    ...rest,
    data: defaultProvider,
    isLoading: rest.isLoading,
    error: rest.error,
  };
};
