/**
 * Facebook Integration Types
 * Định nghĩa các interface cho Facebook integration
 */

/**
 * Enum cho các trường sắp xếp Facebook Page
 */
export enum FacebookPageSortBy {
  CREATED_AT = 'created_at',
  PAGE_NAME = 'page_name',
  PERSONAL_NAME = 'personal_name',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * Interface cho thông tin Agent được kết nối với Facebook Page
 */
export interface AgentInfo {
  id: string;
  name: string;
  avatar?: string | null;
}

/**
 * Interface cho Facebook Page Response
 */
export interface FacebookPageDto {
  facebookPageId: string;
  facebookPersonalId: string;
  facebookPersonalName: string;
  pageName: string;
  avatarPage: string | null;
  isActive: boolean;
  agentId: string | null | undefined;
  isError: boolean;
  agent?: AgentInfo;
}

/**
 * Interface cho Facebook Page Query Parameters
 */
export interface FacebookPageQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  facebookPersonalId?: string;
  sortBy?: FacebookPageSortBy;
  sortOrder?: SortOrder;
  keyword?: string;
}

/**
 * Interface cho Facebook Auth Response
 */
export interface FacebookAuthResponseDto {
  authUrl: string;
}

/**
 * Interface cho Facebook Callback
 */
export interface FacebookCallbackDto {
  code: string;
  redirectEndpoint: string;
}

/**
 * Interface cho Callback Response
 */
export interface CallbackResponseDto {
  pageCount: number;
  pageSuccess?: string[];
  pageError?: string[];
}

/**
 * Interface cho Create Facebook Auth URL
 */
export interface CreateFacebookUrlAuthDto {
  redirectEndpoint: string;
}

/**
 * Interface cho Delete Facebook Pages
 */
export interface DeleteFacebookPagesDto {
  pageIds: string[];
}

/**
 * Interface cho Facebook Personal Response
 */
export interface FacebookPersonalResponseDto {
  id: string;
  facebookPersonalId: string;
  name: string;
  accessToken: string;
  expirationDate: Date;
  expirationDateUnix: number;
  userId: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface cho Connect Agent to Facebook Page
 */
export interface ConnectAgentToFacebookPageDto {
  agentId: string;
  facebookPageId: string;
}

/**
 * Interface cho Disconnect Agent from Facebook Page
 */
export interface DisconnectAgentFromFacebookPageDto {
  facebookPageId: string;
}
