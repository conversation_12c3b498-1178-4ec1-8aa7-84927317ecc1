/**
 * Types for bank account integration
 */

/**
 * Bank account information
 */
export interface BankAccount {
  /**
   * Unique identifier for the account
   */
  id: string;

  /**
   * Bank code (e.g., MB, VCB, TCB)
   */
  bankCode: string;

  /**
   * Bank name
   */
  bankName: string;

  /**
   * Account holder name
   */
  accountName: string;

  /**
   * Account number (may be partially masked)
   */
  accountNumber: string;

  /**
   * Bank logo URL
   */
  logoUrl?: string;

  /**
   * Date when the account was linked
   */
  linkedDate: string;

  /**
   * Whether this is the default account
   */
  isDefault?: boolean;
}

/**
 * Response from the API for bank accounts list
 */
export interface BankAccountsResponse {
  /**
   * Status code
   */
  code: number;

  /**
   * Response message
   */
  message: string;

  /**
   * List of bank accounts
   */
  result: BankAccount[];
}

/**
 * Parameters for fetching bank accounts
 */
export interface BankAccountsParams {
  /**
   * User ID
   */
  userId?: string;

  /**
   * Filter by bank code
   */
  bankCode?: string;
}
