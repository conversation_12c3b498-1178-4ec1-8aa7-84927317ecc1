import { z } from 'zod';
import { BankCode } from '../types/bank-account.types';

/**
 * Schema validation cho form tạo tài khoản ngân hàng
 */
export const createBankAccountSchema = z.object({
  bankCode: z.nativeEnum(BankCode, {
    required_error: '<PERSON>ui lòng chọn ngân hàng',
    invalid_type_error: 'Ngân hàng không hợp lệ',
  }),
  accountNumber: z
    .string()
    .min(1, 'Số tài khoản là bắt buộc')
    .min(6, 'Số tài khoản phải có ít nhất 6 ký tự')
    .max(20, 'Số tài khoản không được quá 20 ký tự')
    .regex(/^[0-9]+$/, 'Số tài khoản chỉ được chứa số'),
  idNumber: z
    .string()
    .min(1, '<PERSON>ố CMND/CCCD là bắt buộc')
    .min(9, 'Số CMND/CCCD phải có ít nhất 9 ký tự')
    .max(12, 'Số CMND/CCCD không được quá 12 ký tự')
    .regex(/^[0-9]+$/, 'Số CMND/CCCD chỉ được chứa số'),
  phoneNumber: z
    .string()
    .min(1, 'Số điện thoại là bắt buộc')
    .min(10, 'Số điện thoại phải có ít nhất 10 ký tự')
    .max(11, 'Số điện thoại không được quá 11 ký tự')
    .regex(/^[0-9]+$/, 'Số điện thoại chỉ được chứa số')
    .regex(/^(0[3|5|7|8|9])[0-9]{8,9}$/, 'Số điện thoại không đúng định dạng'),
  storeName: z
    .string()
    .min(1, 'Tên điểm bán là bắt buộc')
    .min(2, 'Tên điểm bán phải có ít nhất 2 ký tự')
    .max(100, 'Tên điểm bán không được quá 100 ký tự'),
  storeAddress: z
    .string()
    .min(1, 'Địa chỉ điểm bán là bắt buộc')
    .min(10, 'Địa chỉ điểm bán phải có ít nhất 10 ký tự')
    .max(200, 'Địa chỉ điểm bán không được quá 200 ký tự'),
});

/**
 * Schema validation cho form cập nhật tài khoản ngân hàng
 */
export const updateBankAccountSchema = z.object({
  storeName: z
    .string()
    .min(2, 'Tên điểm bán phải có ít nhất 2 ký tự')
    .max(100, 'Tên điểm bán không được quá 100 ký tự')
    .optional(),
  storeAddress: z
    .string()
    .min(10, 'Địa chỉ điểm bán phải có ít nhất 10 ký tự')
    .max(200, 'Địa chỉ điểm bán không được quá 200 ký tự')
    .optional(),
  phoneNumber: z
    .string()
    .min(10, 'Số điện thoại phải có ít nhất 10 ký tự')
    .max(11, 'Số điện thoại không được quá 11 ký tự')
    .regex(/^[0-9]+$/, 'Số điện thoại chỉ được chứa số')
    .regex(/^(0[3|5|7|8|9])[0-9]{8,9}$/, 'Số điện thoại không đúng định dạng')
    .optional(),
});

/**
 * Schema validation cho OTP verification
 */
export const otpVerificationSchema = z.object({
  bankAccountId: z.string().min(1, 'ID tài khoản ngân hàng là bắt buộc'),
  otpCode: z
    .string()
    .min(1, 'Mã OTP là bắt buộc')
    .min(4, 'Mã OTP phải có ít nhất 4 ký tự')
    .max(8, 'Mã OTP không được quá 8 ký tự')
    .regex(/^[0-9]+$/, 'Mã OTP chỉ được chứa số'),
});

/**
 * Schema validation cho tạo tài khoản ảo
 */
export const createVirtualAccountSchema = z.object({
  bankAccountId: z.string().min(1, 'ID tài khoản ngân hàng là bắt buộc'),
});

/**
 * Type definitions từ schemas
 */
export type CreateBankAccountFormData = z.infer<typeof createBankAccountSchema>;
export type UpdateBankAccountFormData = z.infer<typeof updateBankAccountSchema>;
export type OTPVerificationFormData = z.infer<typeof otpVerificationSchema>;
export type CreateVirtualAccountFormData = z.infer<typeof createVirtualAccountSchema>;

/**
 * Schema validation cho query parameters
 */
export const bankAccountQuerySchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional(),
  bankCode: z.nativeEnum(BankCode).optional(),
  status: z.string().optional(),
});

export type BankAccountQueryFormData = z.infer<typeof bankAccountQuerySchema>;
