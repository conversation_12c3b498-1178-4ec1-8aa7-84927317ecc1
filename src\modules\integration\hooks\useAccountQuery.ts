import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import AccountService from '../services/accountService';
import { BankAccount, BankAccountsParams } from '../types/account';

/**
 * Hook for fetching linked bank accounts
 */
export const useLinkedAccounts = (params?: BankAccountsParams) => {
  return useQuery({
    queryKey: ['linkedAccounts', params],
    queryFn: () => AccountService.getLinkedAccounts(params),
    select: (data: { result: BankAccount[] }) => data.result,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for adding a new linked bank account
 */
export const useAddLinkedAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (account: Omit<BankAccount, 'id' | 'linkedDate'>) =>
      AccountService.addLinkedAccount(account),
    onSuccess: () => {
      // Invalidate the linked accounts query to refetch data
      queryClient.invalidateQueries({ queryKey: ['linkedAccounts'] });
    },
  });
};

/**
 * Hook for removing a linked bank account
 */
export const useRemoveLinkedAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (accountId: string) => AccountService.removeLinkedAccount(accountId),
    onSuccess: () => {
      // Invalidate the linked accounts query to refetch data
      queryClient.invalidateQueries({ queryKey: ['linkedAccounts'] });
    },
  });
};
