import {
  <PERSON>,
  Post,
  Get,
  Body,
  Query,
  Headers,
  HttpStatus,
  HttpException,
  Logger,
  RawBodyRequest,
  Req,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Request } from 'express';
import { MessageProcessorService } from '../services/message-processor.service';
import { FacebookService } from '../services/facebook.service';
import { ConfigService } from '@nestjs/config';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { FacebookPageConfigRepository } from '../repositories/facebook-page-config.repository';

/**
 * Interface cho Facebook Webhook Entry
 */
interface FacebookWebhookEntry {
  id: string;
  time: number;
  messaging: Array<{
    sender: { id: string };
    recipient: { id: string };
    timestamp: number;
    message?: {
      mid: string;
      text?: string;
      attachments?: any[];
      quick_reply?: { payload: string };
    };
    postback?: {
      payload: string;
      title: string;
    };
  }>;
}

/**
 * Interface cho Facebook Webhook Body
 */
interface FacebookWebhookBody {
  object: string;
  entry: FacebookWebhookEntry[];
}

/**
 * Controller xử lý webhook từ Facebook Messenger
 */
@ApiTags(SWAGGER_API_TAG.CHAT)
@Controller('/chat/facebook-webhook')
export class FacebookWebhookController {
  private readonly logger = new Logger(FacebookWebhookController.name);

  constructor(
    private readonly messageProcessor: MessageProcessorService,
    private readonly facebookService: FacebookService,
    private readonly configService: ConfigService,
    private readonly facebookPageConfigRepository: FacebookPageConfigRepository,
  ) {}

  /**
   * Verify webhook từ Facebook
   */
  @Get()
  @ApiOperation({
    summary: 'Verify Facebook webhook',
    description: 'Endpoint để Facebook verify webhook subscription',
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook verified successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Webhook verification failed',
  })
  verifyWebhook(
    @Query('hub.mode') mode: string,
    @Query('hub.verify_token') verifyToken: string,
    @Query('hub.challenge') challenge: string,
  ): string {
    this.logger.log('Facebook webhook verification request received');

    const expectedVerifyToken = this.configService.get<string>(
      'FACEBOOK_WEBHOOK_VERIFY_TOKEN',
    );

    if (mode === 'subscribe' && verifyToken === expectedVerifyToken) {
      this.logger.log('Facebook webhook verified successfully');
      return challenge;
    }

    this.logger.error('Facebook webhook verification failed');
    throw new HttpException(
      'Webhook verification failed',
      HttpStatus.FORBIDDEN,
    );
  }

  /**
   * Nhận webhook events từ Facebook
   */
  @Post()
  @ApiOperation({
    summary: 'Receive Facebook webhook events',
    description: 'Endpoint nhận các events từ Facebook Messenger',
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook processed successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid webhook data',
  })
  async receiveWebhook(
    @Req() req: RawBodyRequest<Request>,
    @Body() body: FacebookWebhookBody,
    @Headers('x-hub-signature-sha1') signature: string,
  ): Promise<{ status: string }> {
    try {
      this.logger.log('Facebook webhook event received');

      // Verify webhook signature
      const appSecret = this.configService.get<string>('FACEBOOK_APP_SECRET');
      const rawBody = req.rawBody?.toString() || JSON.stringify(body);

      if (
        !appSecret ||
        !signature ||
        !this.facebookService.verifyWebhookSignature(
          rawBody,
          signature,
          appSecret,
        )
      ) {
        this.logger.error('Invalid webhook signature');
        throw new HttpException(
          'Invalid webhook signature',
          HttpStatus.FORBIDDEN,
        );
      }

      // Xử lý từng entry
      if (body.object === 'page') {
        await Promise.all(
          body.entry.map((entry) => this.processWebhookEntry(entry)),
        );
      }

      return { status: 'success' };
    } catch (error) {
      this.logger.error(`Webhook processing error: ${error.message}`);

      // Vẫn trả về 200 để Facebook không retry
      return { status: 'error' };
    }
  }

  /**
   * Xử lý một webhook entry
   */
  private async processWebhookEntry(
    entry: FacebookWebhookEntry,
  ): Promise<void> {
    const pageId = entry.id;

    // Lấy page access token (cần implement logic lấy token theo pageId)
    const pageAccessToken = await this.getPageAccessToken(pageId);

    if (!pageAccessToken) {
      this.logger.error(`No access token found for page ${pageId}`);
      return;
    }

    // Xử lý từng messaging event
    await Promise.all(
      entry.messaging.map((messagingEvent) =>
        this.processMessagingEvent(pageId, pageAccessToken, messagingEvent),
      ),
    );
  }

  /**
   * Xử lý một messaging event
   */
  private async processMessagingEvent(
    pageId: string,
    pageAccessToken: string,
    messagingEvent: any,
  ): Promise<void> {
    try {
      // Bỏ qua tin nhắn từ page (tránh loop)
      if (messagingEvent.message?.is_echo) {
        return;
      }

      // Xác định tenantId từ pageId (cần implement logic mapping)
      const tenantId = await this.getTenantIdFromPageId(pageId);

      if (!tenantId) {
        this.logger.error(`No tenant found for page ${pageId}`);
        return;
      }

      // Xử lý tin nhắn
      await this.messageProcessor.processIncomingMessage(
        pageId,
        pageAccessToken,
        messagingEvent,
        tenantId,
      );
    } catch (error) {
      this.logger.error(
        `Error processing messaging event: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Lấy page access token từ pageId
   */
  private async getPageAccessToken(pageId: string): Promise<string | null> {
    try {
      // TODO: Implement logic lấy access token từ database
      // Có thể cache trong Redis để tăng performance

      // Placeholder - cần implement với FacebookPageConfigRepository
      const pageConfig = await this.getPageConfig(pageId);
      return pageConfig?.accessToken || null;
    } catch (error) {
      this.logger.error(`Error getting page access token: ${error.message}`);
      return null;
    }
  }

  /**
   * Lấy tenantId từ pageId
   */
  private async getTenantIdFromPageId(pageId: string): Promise<number | null> {
    try {
      // TODO: Implement logic mapping pageId -> tenantId
      // Có thể cache trong Redis để tăng performance

      // Placeholder - cần implement với FacebookPageConfigRepository
      const pageConfig = await this.getPageConfig(pageId);
      return pageConfig?.tenantId || null;
    } catch (error) {
      this.logger.error(`Error getting tenant ID: ${error.message}`);
      return null;
    }
  }

  /**
   * Lấy config của page từ repository
   */
  private async getPageConfig(pageId: string): Promise<any> {
    return this.facebookPageConfigRepository.findByPageId(pageId);
  }
}
