import {
  ReportOverviewQueryDto,
  SalesChartQueryDto,
  OrdersChartQueryDto,
  CustomersChartQueryDto,
  ProductsChartQueryDto,
  TopSellingProductsQueryDto,
  PotentialCustomersQueryDto,
} from '../types/report.types';

/**
 * Query keys cho Business Report module
 */
export const REPORT_QUERY_KEYS = {
  all: ['business', 'reports'] as const,
  
  // Overview
  overview: () => [...REPORT_QUERY_KEYS.all, 'overview'] as const,
  overviewWithParams: (params: ReportOverviewQueryDto) => 
    [...REPORT_QUERY_KEYS.overview(), params] as const,
  
  // Charts
  charts: () => [...REPORT_QUERY_KEYS.all, 'charts'] as const,
  
  // Sales Chart
  salesChart: () => [...REPORT_QUERY_KEYS.charts(), 'sales'] as const,
  salesChartWithParams: (params: SalesChartQueryDto) => 
    [...REPORT_QUERY_KEYS.salesChart(), params] as const,
  
  // Orders Chart
  ordersChart: () => [...REPORT_QUERY_KEYS.charts(), 'orders'] as const,
  ordersChartWithParams: (params: OrdersChartQueryDto) => 
    [...REPORT_QUERY_KEYS.ordersChart(), params] as const,
  
  // Customers Chart
  customersChart: () => [...REPORT_QUERY_KEYS.charts(), 'customers'] as const,
  customersChartWithParams: (params: CustomersChartQueryDto) => 
    [...REPORT_QUERY_KEYS.customersChart(), params] as const,
  
  // Products Chart
  productsChart: () => [...REPORT_QUERY_KEYS.charts(), 'products'] as const,
  productsChartWithParams: (params: ProductsChartQueryDto) => 
    [...REPORT_QUERY_KEYS.productsChart(), params] as const,
  
  // Lists
  lists: () => [...REPORT_QUERY_KEYS.all, 'lists'] as const,
  
  // Top Selling Products
  topSellingProducts: () => [...REPORT_QUERY_KEYS.lists(), 'top-selling-products'] as const,
  topSellingProductsWithParams: (params: TopSellingProductsQueryDto) => 
    [...REPORT_QUERY_KEYS.topSellingProducts(), params] as const,
  
  // Potential Customers
  potentialCustomers: () => [...REPORT_QUERY_KEYS.lists(), 'potential-customers'] as const,
  potentialCustomersWithParams: (params: PotentialCustomersQueryDto) => 
    [...REPORT_QUERY_KEYS.potentialCustomers(), params] as const,
};
