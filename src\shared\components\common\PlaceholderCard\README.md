# PlaceholderCard Component

A reusable React component for displaying placeholder content, empty states, or features under development.

## Features

- 🎨 **Customizable**: Flexible styling options for icon, text, and layout
- 🌐 **i18n Support**: Built-in translation support with fallback text
- 📱 **Responsive**: Works well on all screen sizes
- 🎯 **Accessible**: Proper semantic structure and ARIA support
- 🔧 **Flexible**: Can be used with or without card wrapper

## Basic Usage

```tsx
import { PlaceholderCard } from '@/shared/components/common';

// Simple placeholder
<PlaceholderCard />

// Custom content
<PlaceholderCard
  icon="bar-chart"
  title="Statistics Dashboard"
  description="Advanced analytics coming soon"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `icon` | `string` | `'info'` | Icon name to display |
| `iconSize` | `'sm' \| 'md' \| 'lg' \| 'xl'` | `'lg'` | Size of the icon |
| `title` | `string` | - | Title text or translation key |
| `description` | `string` | - | Description text or translation key |
| `namespace` | `string` | - | Translation namespace |
| `titleFallback` | `string` | `'Feature Coming Soon'` | Fallback title text |
| `descriptionFallback` | `string` | `'This feature is currently under development'` | Fallback description text |
| `className` | `string` | `'p-6'` | Custom className for the card |
| `contentClassName` | `string` | `'text-center py-12'` | Custom className for content container |
| `iconClassName` | `string` | `'mx-auto mb-4 text-muted-foreground'` | Custom className for the icon |
| `titleClassName` | `string` | `'text-lg font-medium mb-2'` | Custom className for the title |
| `descriptionClassName` | `string` | `'text-muted-foreground'` | Custom className for the description |
| `showCard` | `boolean` | `true` | Whether to show the card wrapper |
| `children` | `React.ReactNode` | - | Custom children to render instead of default content |

## Examples

### With Translation Keys

```tsx
<PlaceholderCard
  icon="users"
  title="hrm:employee.stats.title"
  description="hrm:employee.stats.description"
  titleFallback="Employee Statistics"
  descriptionFallback="Statistics feature under development"
  namespace="hrm"
/>
```

### Without Card Wrapper

```tsx
<PlaceholderCard
  icon="settings"
  title="Configuration Panel"
  description="Settings coming soon"
  showCard={false}
/>
```

### Custom Styling

```tsx
<PlaceholderCard
  icon="star"
  title="Premium Feature"
  description="Upgrade to access this feature"
  className="p-8 bg-gradient-to-br from-purple-50 to-blue-50"
  iconClassName="mx-auto mb-4 text-purple-500"
  titleClassName="text-xl font-bold text-purple-800"
/>
```

### Custom Children

```tsx
<PlaceholderCard>
  <div className="text-center py-8">
    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
      🚀
    </div>
    <h3 className="text-lg font-medium mb-2">Custom Content</h3>
    <p className="text-muted-foreground">Your custom content here</p>
  </div>
</PlaceholderCard>
```

## Use Cases

- **Feature Under Development**: Show placeholder for features not yet implemented
- **Empty States**: Display when no data is available
- **Coming Soon**: Announce upcoming features
- **Error States**: Show when something went wrong
- **Loading States**: Display while content is being loaded

## Accessibility

The component uses semantic HTML elements and proper ARIA attributes:
- Uses `Typography` component for proper heading hierarchy
- Icon has appropriate ARIA labels
- Maintains proper color contrast ratios
- Keyboard navigation friendly

## Dependencies

- `react-i18next` for internationalization
- `Card` component for wrapper
- `Icon` component for icons
- `Typography` component for text
