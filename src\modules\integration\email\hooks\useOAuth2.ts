/**
 * OAuth2 Hooks for Email Providers
 */

import { useState, useCallback, useEffect } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { 
  OAuth2ServiceFactory, 
  OAuth2TokenManager,
  OAuth2AuthorizationResponse,
  OAuth2TokenResponse 
} from '../services/oauth';
import { OAuthTokens } from '../types/providers';

export interface UseOAuth2Options {
  providerId: string;
  redirectUri?: string;
  onSuccess?: (tokens: OAuthTokens) => void;
  onError?: (error: Error) => void;
}

export interface UseOAuth2Return {
  // States
  isAuthorizing: boolean;
  isExchangingToken: boolean;
  tokens: OAuthTokens | null;
  authorizationUrl: string | null;
  error: Error | null;
  
  // Actions
  startAuthorization: () => Promise<void>;
  exchangeCodeForTokens: (code: string, state: string) => Promise<void>;
  refreshTokens: () => Promise<void>;
  clearTokens: () => void;
  
  // Utilities
  isAuthenticated: boolean;
  isTokenExpired: boolean;
}

/**
 * Main OAuth2 hook for email providers
 */
export const useOAuth2 = (options: UseOAuth2Options): UseOAuth2Return => {
  const { providerId, redirectUri, onSuccess, onError } = options;
  
  const [authorizationUrl, setAuthorizationUrl] = useState<string | null>(null);
  const [tokens, setTokens] = useState<OAuthTokens | null>(null);
  const [error, setError] = useState<Error | null>(null);

  // Load stored tokens on mount
  useEffect(() => {
    const storedTokens = OAuth2TokenManager.getTokens(providerId);
    if (storedTokens) {
      setTokens(storedTokens);
    }
  }, [providerId]);

  // Authorization mutation
  const authorizationMutation = useMutation({
    mutationFn: async () => {
      if (!OAuth2ServiceFactory.isOAuth2Supported(providerId)) {
        throw new Error(`OAuth2 not supported for provider: ${providerId}`);
      }

      const service = OAuth2ServiceFactory.getService(providerId);
      const defaultRedirectUri = `${window.location.origin}/integration/oauth/callback`;
      
      return service.startAuthorization(redirectUri || defaultRedirectUri);
    },
    onSuccess: (data: OAuth2AuthorizationResponse) => {
      setAuthorizationUrl(data.authorizationUrl);
      setError(null);
      
      // Open authorization URL in popup or redirect
      window.open(data.authorizationUrl, 'oauth_popup', 'width=600,height=700');
    },
    onError: (error: Error) => {
      setError(error);
      onError?.(error);
    }
  });

  // Token exchange mutation
  const tokenExchangeMutation = useMutation({
    mutationFn: async ({ code, state }: { code: string; state: string }) => {
      const service = OAuth2ServiceFactory.getService(providerId);
      const defaultRedirectUri = `${window.location.origin}/integration/oauth/callback`;
      
      return service.exchangeCodeForTokens(code, state, redirectUri || defaultRedirectUri);
    },
    onSuccess: (data: OAuth2TokenResponse) => {
      const newTokens: OAuthTokens = {
        accessToken: data.accessToken,
        refreshToken: data.refreshToken,
        expiresAt: data.expiresAt,
        tokenType: data.tokenType,
        scope: data.scope
      };

      // Store tokens
      OAuth2TokenManager.storeTokens(providerId, newTokens);
      setTokens(newTokens);
      setError(null);
      
      onSuccess?.(newTokens);
    },
    onError: (error: Error) => {
      setError(error);
      onError?.(error);
    }
  });

  // Token refresh mutation
  const refreshMutation = useMutation({
    mutationFn: async () => {
      if (!tokens?.refreshToken) {
        throw new Error('No refresh token available');
      }

      const service = OAuth2ServiceFactory.getService(providerId);
      return service.refreshToken(tokens.refreshToken);
    },
    onSuccess: (data: OAuth2TokenResponse) => {
      const newTokens: OAuthTokens = {
        accessToken: data.accessToken,
        refreshToken: data.refreshToken || tokens?.refreshToken || '',
        expiresAt: data.expiresAt,
        tokenType: data.tokenType,
        scope: data.scope
      };

      OAuth2TokenManager.storeTokens(providerId, newTokens);
      setTokens(newTokens);
      setError(null);
    },
    onError: (error: Error) => {
      setError(error);
      onError?.(error);
    }
  });

  // Actions
  const startAuthorization = useCallback(async () => {
    await authorizationMutation.mutateAsync();
  }, [authorizationMutation]);

  const exchangeCodeForTokens = useCallback(async (code: string, state: string) => {
    await tokenExchangeMutation.mutateAsync({ code, state });
  }, [tokenExchangeMutation]);

  const refreshTokens = useCallback(async () => {
    await refreshMutation.mutateAsync();
  }, [refreshMutation]);

  const clearTokens = useCallback(() => {
    OAuth2TokenManager.removeTokens(providerId);
    setTokens(null);
    setAuthorizationUrl(null);
    setError(null);
  }, [providerId]);

  // Computed values
  const isAuthenticated = Boolean(tokens?.accessToken);
  const isTokenExpired = tokens ? OAuth2TokenManager.isTokenExpired(tokens) : false;

  return {
    // States
    isAuthorizing: authorizationMutation.isPending,
    isExchangingToken: tokenExchangeMutation.isPending,
    tokens,
    authorizationUrl,
    error,
    
    // Actions
    startAuthorization,
    exchangeCodeForTokens,
    refreshTokens,
    clearTokens,
    
    // Utilities
    isAuthenticated,
    isTokenExpired
  };
};

/**
 * Hook for Gmail OAuth2
 */
export const useGmailOAuth2 = (options?: Omit<UseOAuth2Options, 'providerId'>) => {
  return useOAuth2({ ...options, providerId: 'gmail' });
};

/**
 * Hook for Outlook OAuth2
 */
export const useOutlookOAuth2 = (options?: Omit<UseOAuth2Options, 'providerId'>) => {
  return useOAuth2({ ...options, providerId: 'outlook' });
};

/**
 * Hook to validate OAuth2 tokens
 */
export const useOAuth2Validation = (providerId: string, tokens: OAuthTokens | null) => {
  return useQuery({
    queryKey: ['oauth2-validation', providerId, tokens?.accessToken],
    queryFn: async () => {
      if (!tokens) return false;
      
      const service = OAuth2ServiceFactory.getService(providerId);
      return service.validateTokens(tokens);
    },
    enabled: Boolean(tokens?.accessToken),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false
  });
};

/**
 * Hook to auto-refresh tokens
 */
export const useAutoRefreshTokens = (providerId: string) => {
  const [tokens, setTokens] = useState<OAuthTokens | null>(null);

  useEffect(() => {
    const checkAndRefreshTokens = async () => {
      try {
        const validTokens = await OAuth2TokenManager.ensureValidTokens(providerId);
        setTokens(validTokens);
      } catch (error) {
        console.error('Failed to refresh tokens:', error);
        setTokens(null);
      }
    };

    // Check immediately
    checkAndRefreshTokens();

    // Set up interval to check every 5 minutes
    const interval = setInterval(checkAndRefreshTokens, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [providerId]);

  return tokens;
};
