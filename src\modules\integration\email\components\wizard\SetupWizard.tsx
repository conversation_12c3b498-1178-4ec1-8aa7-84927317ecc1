/**
 * Interactive Setup Wizard Component
 */

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Icon, Step } from '@/shared/components/common';
import { EmailProvider } from '../../types/providers';

export interface SetupWizardProps {
  provider: EmailProvider;
  onComplete: () => void;
  onCancel: () => void;
}

export interface WizardStep {
  id: string;
  title: string;
  description: string;
  content: React.ReactNode;
  isCompleted: boolean;
  isOptional?: boolean;
}

export const SetupWizard: React.FC<SetupWizardProps> = ({
  provider,
  onComplete,
  onCancel
}) => {
  const { t } = useTranslation(['integration']);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());

  // Generate steps based on provider
  const generateSteps = (): WizardStep[] => {
    const baseSteps: WizardStep[] = [
      {
        id: 'overview',
        title: t('integration:wizard.steps.overview', 'Tổng quan'),
        description: t('integration:wizard.steps.overviewDesc', 'Hiểu về nhà cung cấp'),
        content: <OverviewStep provider={provider} />,
        isCompleted: false
      }
    ];

    // Add provider-specific steps
    switch (provider.id) {
      case 'gmail':
        baseSteps.push(
          {
            id: 'enable-2fa',
            title: t('integration:wizard.steps.enable2fa', 'Bật 2FA'),
            description: t('integration:wizard.steps.enable2faDesc', 'Kích hoạt xác thực 2 bước'),
            content: <Enable2FAStep provider={provider} />,
            isCompleted: false
          },
          {
            id: 'create-app-password',
            title: t('integration:wizard.steps.createAppPassword', 'Tạo App Password'),
            description: t('integration:wizard.steps.createAppPasswordDesc', 'Tạo mật khẩu ứng dụng'),
            content: <CreateAppPasswordStep provider={provider} />,
            isCompleted: false
          }
        );
        break;

      case 'sendgrid':
        baseSteps.push(
          {
            id: 'create-api-key',
            title: t('integration:wizard.steps.createApiKey', 'Tạo API Key'),
            description: t('integration:wizard.steps.createApiKeyDesc', 'Tạo khóa API SendGrid'),
            content: <CreateApiKeyStep provider={provider} />,
            isCompleted: false
          },
          {
            id: 'verify-sender',
            title: t('integration:wizard.steps.verifySender', 'Verify Sender'),
            description: t('integration:wizard.steps.verifySenderDesc', 'Xác thực người gửi'),
            content: <VerifySenderStep provider={provider} />,
            isCompleted: false,
            isOptional: true
          }
        );
        break;

      case 'mailgun':
        baseSteps.push(
          {
            id: 'add-domain',
            title: t('integration:wizard.steps.addDomain', 'Thêm Domain'),
            description: t('integration:wizard.steps.addDomainDesc', 'Thêm và verify domain'),
            content: <AddDomainStep provider={provider} />,
            isCompleted: false
          },
          {
            id: 'configure-dns',
            title: t('integration:wizard.steps.configureDns', 'Cấu hình DNS'),
            description: t('integration:wizard.steps.configureDnsDesc', 'Thiết lập DNS records'),
            content: <ConfigureDnsStep provider={provider} />,
            isCompleted: false
          }
        );
        break;

      case 'amazon-ses':
        baseSteps.push(
          {
            id: 'create-iam-user',
            title: t('integration:wizard.steps.createIamUser', 'Tạo IAM User'),
            description: t('integration:wizard.steps.createIamUserDesc', 'Tạo user với quyền SES'),
            content: <CreateIamUserStep provider={provider} />,
            isCompleted: false
          },
          {
            id: 'verify-email',
            title: t('integration:wizard.steps.verifyEmail', 'Verify Email'),
            description: t('integration:wizard.steps.verifyEmailDesc', 'Xác thực email/domain'),
            content: <VerifyEmailStep provider={provider} />,
            isCompleted: false
          },
          {
            id: 'request-production',
            title: t('integration:wizard.steps.requestProduction', 'Request Production'),
            description: t('integration:wizard.steps.requestProductionDesc', 'Yêu cầu production access'),
            content: <RequestProductionStep provider={provider} />,
            isCompleted: false,
            isOptional: true
          }
        );
        break;
    }

    // Add final configuration step
    baseSteps.push({
      id: 'configure',
      title: t('integration:wizard.steps.configure', 'Cấu hình'),
      description: t('integration:wizard.steps.configureDesc', 'Nhập thông tin kết nối'),
      content: <ConfigurationStep provider={provider} />,
      isCompleted: false
    });

    return baseSteps;
  };

  const steps = generateSteps();
  const currentStep = steps[currentStepIndex];

  const handleNext = () => {
    if (currentStepIndex < steps.length - 1) {
      // Mark current step as completed
      setCompletedSteps(prev => new Set([...prev, currentStep.id]));
      setCurrentStepIndex(prev => prev + 1);
    } else {
      // Last step - complete wizard
      onComplete();
    }
  };

  const handleBack = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
    }
  };

  const handleStepClick = (stepIndex: number) => {
    setCurrentStepIndex(stepIndex);
  };

  const isStepCompleted = (stepId: string) => completedSteps.has(stepId);
  const canProceed = currentStep.isOptional || isStepCompleted(currentStep.id);

  return (
    <div className="max-w-4xl mx-auto">
      <Card>
        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <Typography variant="h4" className="mb-2">
              {t('integration:wizard.title', 'Hướng dẫn tích hợp {{provider}}', { 
                provider: provider.displayName 
              })}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('integration:wizard.description', 'Làm theo các bước để tích hợp thành công')}
            </Typography>
          </div>

          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <Step
                  key={step.id}
                  title={step.title}
                  description={step.description}
                  status={
                    isStepCompleted(step.id) 
                      ? 'completed' 
                      : index === currentStepIndex 
                        ? 'active' 
                        : 'pending'
                  }
                  isOptional={step.isOptional}
                  onClick={() => handleStepClick(index)}
                  className="cursor-pointer"
                />
              ))}
            </div>
          </div>

          {/* Current Step Content */}
          <div className="mb-8 min-h-[400px]">
            {currentStep.content}
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={currentStepIndex === 0}
              >
                <Icon name="arrow-left" className="w-4 h-4 mr-2" />
                {t('common:back', 'Quay lại')}
              </Button>
              
              <Button variant="outline" onClick={onCancel}>
                {t('common:cancel', 'Hủy')}
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <Typography variant="caption" className="text-muted-foreground">
                {t('integration:wizard.progress', 'Bước {{current}} / {{total}}', {
                  current: currentStepIndex + 1,
                  total: steps.length
                })}
              </Typography>
              
              <Button
                variant="primary"
                onClick={handleNext}
                disabled={!canProceed}
              >
                {currentStepIndex === steps.length - 1 ? (
                  <>
                    <Icon name="check" className="w-4 h-4 mr-2" />
                    {t('integration:wizard.complete', 'Hoàn thành')}
                  </>
                ) : (
                  <>
                    {t('common:next', 'Tiếp theo')}
                    <Icon name="arrow-right" className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

// Step Components (simplified - would be implemented separately)
const OverviewStep: React.FC<{ provider: EmailProvider }> = ({ provider }) => (
  <div className="text-center py-8">
    <Icon name="info" className="w-16 h-16 mx-auto mb-4 text-blue-500" />
    <Typography variant="h5" className="mb-4">{provider.displayName}</Typography>
    <Typography variant="body1" className="mb-4">{provider.description}</Typography>
    <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
      <div className="text-center">
        <Typography variant="caption" className="text-muted-foreground">Category</Typography>
        <Typography variant="body2">{provider.category}</Typography>
      </div>
      <div className="text-center">
        <Typography variant="caption" className="text-muted-foreground">Auth Methods</Typography>
        <Typography variant="body2">{provider.authMethods.join(', ')}</Typography>
      </div>
    </div>
  </div>
);

const Enable2FAStep: React.FC<{ provider: EmailProvider }> = () => (
  <div>Step content for enabling 2FA</div>
);

const CreateAppPasswordStep: React.FC<{ provider: EmailProvider }> = () => (
  <div>Step content for creating app password</div>
);

const CreateApiKeyStep: React.FC<{ provider: EmailProvider }> = () => (
  <div>Step content for creating API key</div>
);

const VerifySenderStep: React.FC<{ provider: EmailProvider }> = () => (
  <div>Step content for verifying sender</div>
);

const AddDomainStep: React.FC<{ provider: EmailProvider }> = () => (
  <div>Step content for adding domain</div>
);

const ConfigureDnsStep: React.FC<{ provider: EmailProvider }> = () => (
  <div>Step content for configuring DNS</div>
);

const CreateIamUserStep: React.FC<{ provider: EmailProvider }> = () => (
  <div>Step content for creating IAM user</div>
);

const VerifyEmailStep: React.FC<{ provider: EmailProvider }> = () => (
  <div>Step content for verifying email</div>
);

const RequestProductionStep: React.FC<{ provider: EmailProvider }> = () => (
  <div>Step content for requesting production access</div>
);

const ConfigurationStep: React.FC<{ provider: EmailProvider }> = () => (
  <div>Step content for final configuration</div>
);

export default SetupWizard;
