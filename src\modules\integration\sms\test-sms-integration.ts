/**
 * Test file để kiểm tra SMS Integration Module
 * Chạy file này để đảm bảo tất cả exports hoạt động đúng
 */

// Test imports
import {
  // Types
  SmsProviderFormData,

  // Constants
  SMS_PROVIDER_TYPES,
  SMS_PROVIDER_TEMPLATES,
  SMS_INTEGRATION_ENDPOINTS,

  // Schemas
  smsProviderFormSchema,

  // Config
  SMS_INTEGRATION_CONFIG,
} from './index';

console.log('✅ SMS Integration Module - All imports successful!');

// Test basic functionality
console.log('📋 SMS Integration Config:', SMS_INTEGRATION_CONFIG);
console.log('🏭 Supported Providers:', SMS_INTEGRATION_CONFIG.supportedProviders);
console.log('🛣️ Routes:', SMS_INTEGRATION_CONFIG.routes);

// Test provider templates
console.log('📝 Provider Templates:');
Object.entries(SMS_PROVIDER_TEMPLATES).forEach(([key, template]) => {
  console.log(`  - ${key}: ${template.displayName}`);
});

// Test constants
console.log('🔧 Provider Types:', Object.values(SMS_PROVIDER_TYPES));
console.log('🔗 API Endpoints:', SMS_INTEGRATION_ENDPOINTS);

// Test schema validation
try {
  const testData: SmsProviderFormData = {
    name: 'test-provider',
    type: 'twilio',
    displayName: 'Test Provider',
    credentials: {
      accountSid: 'test-sid',
      authToken: 'test-token',
    },
    settings: {
      rateLimits: {
        perSecond: 1,
        perMinute: 60,
        perHour: 3600,
        perDay: 86400,
        perMonth: 2592000,
      },
      retryConfig: {
        maxRetries: 3,
        retryDelay: 5,
        backoffMultiplier: 2,
      },
      enableDeliveryReports: true,
      enableOptOut: true,
      timezone: 'Asia/Ho_Chi_Minh',
    },
    isDefault: false,
  };
  
  smsProviderFormSchema.parse(testData);
  console.log('✅ Schema validation successful!');
} catch (error) {
  console.error('❌ Schema validation failed:', error);
}

console.log('🎉 SMS Integration Module test completed successfully!');

export default 'SMS Integration Module Test Passed';
